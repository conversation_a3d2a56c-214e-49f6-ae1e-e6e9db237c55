# This file was originally created by the Android Tools, but is now
# used by cordova-android to manage the state of the various third party
# libraries used in your application

# This is the Library Module that contains the Cordova Library, this is not
# required when using an AAR

# This is the application project.  This is only required for Android Studio Gradle projects

# Project target.
target=android-33
android.library.reference.1=CordovaLib
android.library.reference.2=app
cordova.gradle.include.1=cordova-plugin-code-push/agrobalance-build-extras.gradle
cordova.gradle.include.2=phonegap-plugin-barcodescanner/agrobalance-barcodescanner.gradle
cordova.gradle.include.3=phonegap-plugin-push/agrobalance-push.gradle
cordova.system.library.1=com.android.support:support-v13:23+
cordova.system.library.2=com.google.android.gms:play-services-gcm:9.0.2+
cordova.system.library.3=me.leolin:ShortcutBadger:1.1.4@aar
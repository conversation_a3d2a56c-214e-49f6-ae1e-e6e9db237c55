{"prepare_queue": {"installed": [], "uninstalled": []}, "config_munge": {"files": {"res/xml/config.xml": {"parents": {"/*": [{"xml": "<feature name=\"JJzip\"><param name=\"android-package\" value=\"com.jjdltc.cordova.plugin.zip.JJzip\" /></feature>", "count": 1}, {"xml": "<feature name=\"AppInfo\"><param name=\"android-package\" value=\"org.scriptotek.appinfo.AppInfo\" /></feature>", "count": 1}, {"xml": "<feature name=\"Camera\"><param name=\"android-package\" value=\"org.apache.cordova.camera.CameraLauncher\" /></feature>", "count": 1}, {"xml": "<feature name=\"Notification\"><param name=\"android-package\" value=\"org.apache.cordova.dialogs.Notification\" /></feature>", "count": 1}, {"xml": "<feature name=\"Device\"><param name=\"android-package\" value=\"org.apache.cordova.device.Device\" /></feature>", "count": 1}, {"xml": "<feature name=\"Compass\"><param name=\"android-package\" value=\"org.apache.cordova.deviceorientation.CompassListener\" /></feature>", "count": 1}, {"xml": "<feature name=\"File\"><param name=\"android-package\" value=\"org.apache.cordova.file.FileUtils\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<feature name=\"FileTransfer\"><param name=\"android-package\" value=\"org.apache.cordova.filetransfer.FileTransfer\" /></feature>", "count": 1}, {"xml": "<feature name=\"Geolocation\"><param name=\"android-package\" value=\"org.apache.cordova.geolocation.Geolocation\" /></feature>", "count": 1}, {"xml": "<feature name=\"Whitelist\"><param name=\"android-package\" value=\"org.apache.cordova.whitelist.WhitelistPlugin\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<feature name=\"BarcodeScanner\"><param name=\"android-package\" value=\"com.phonegap.plugins.barcodescanner.BarcodeScanner\" /></feature>", "count": 1}, {"xml": "<feature name=\"PushNotification\"><param name=\"android-package\" value=\"com.adobe.phonegap.push.PushPlugin\" /></feature>", "count": 1}], "/widget": [{"xml": "<feature name=\"BluetoothSerial\"><param name=\"android-package\" value=\"com.megster.cordova.BluetoothSerial\" /></feature>", "count": 1}, {"xml": "<feature name=\"Zip\"><param name=\"android-package\" value=\"org.apache.cordova.Zip\" /></feature>", "count": 1}]}}, "AndroidManifest.xml": {"parents": {"/manifest": [{"xml": "<uses-permission android:name=\"android.permission.BLUETOOTH\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.CAMERA\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />", "count": 1}, {"xml": "<uses-feature android:name=\"android.hardware.camera\" android:required=\"true\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.INTERNET\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.VIBRATE\" />", "count": 1}, {"xml": "<uses-permission android:name=\"com.google.android.c2dm.permission.RECEIVE\" />", "count": 1}, {"xml": "<uses-permission android:name=\"${applicationId}.permission.C2D_MESSAGE\" />", "count": 1}, {"xml": "<permission android:name=\"${applicationId}.permission.C2D_MESSAGE\" android:protectionLevel=\"signature\" />", "count": 1}], "/*": [{"xml": "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "count": 3}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\" />", "count": 2}, {"xml": "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\" />", "count": 2}], "/manifest/application": [{"xml": "<activity android:clearTaskOnLaunch=\"true\" android:configChanges=\"orientation|keyboardHidden|screenSize\" android:exported=\"false\" android:name=\"com.google.zxing.client.android.CaptureActivity\" android:theme=\"@android:style/Theme.NoTitleBar.Fullscreen\" android:windowSoftInputMode=\"stateAlwaysHidden\" />", "count": 1}, {"xml": "<activity android:label=\"Share\" android:name=\"com.google.zxing.client.android.encode.EncodeActivity\" />", "count": 1}, {"xml": "<activity android:exported=\"true\" android:name=\"com.adobe.phonegap.push.PushHandlerActivity\" />", "count": 1}, {"xml": "<receiver android:name=\"com.adobe.phonegap.push.BackgroundActionButtonHandler\" />", "count": 1}, {"xml": "<receiver android:exported=\"true\" android:name=\"com.google.android.gms.gcm.GcmReceiver\" android:permission=\"com.google.android.c2dm.permission.SEND\"><intent-filter><action android:name=\"com.google.android.c2dm.intent.RECEIVE\" /><category android:name=\"${applicationId}\" /></intent-filter></receiver>", "count": 1}, {"xml": "<service android:exported=\"false\" android:name=\"com.adobe.phonegap.push.GCMIntentService\"><intent-filter><action android:name=\"com.google.android.c2dm.intent.RECEIVE\" /></intent-filter></service>", "count": 1}, {"xml": "<service android:exported=\"false\" android:name=\"com.adobe.phonegap.push.PushInstanceIDListenerService\"><intent-filter><action android:name=\"com.google.android.gms.iid.InstanceID\" /></intent-filter></service>", "count": 1}, {"xml": "<service android:exported=\"false\" android:name=\"com.adobe.phonegap.push.RegistrationIntentService\" />", "count": 1}]}}, "config.xml": {"parents": {"/*": [{"xml": "<feature name=\"CodePush\"><param name=\"android-package\" value=\"com.microsoft.cordova.CodePush\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}]}}, "res/values/strings.xml": {"parents": {"/resources": [{"xml": "<string name=\"google_app_id\">751733604932</string>", "count": 1}]}}}}, "installed_plugins": {"code-push": {"PACKAGE_NAME": "com.agrobalance"}, "com.jjdltc.cordova.plugin.zip": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-appinfo": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-bluetooth-serial": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-compat": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-camera": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-dialogs": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-device": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-code-push": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-device-orientation": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-file": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-file-transfer": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-geolocation": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-whitelist": {"PACKAGE_NAME": "com.agrobalance"}, "cordova-plugin-zip": {"PACKAGE_NAME": "com.agrobalance"}, "phonegap-plugin-barcodescanner": {"PACKAGE_NAME": "com.agrobalance"}, "phonegap-plugin-push": {"SENDER_ID": "751733604932", "PACKAGE_NAME": "com.agrobalance"}}, "dependent_plugins": {}, "modules": [{"id": "code-push.AcquisitionManager", "file": "plugins/code-push/script/acquisition-sdk.js", "pluginId": "code-push", "merges": ["window"]}, {"id": "com.jjdltc.cordova.plugin.zip.JJzip", "file": "plugins/com.jjdltc.cordova.plugin.zip/www/JJzip.js", "pluginId": "com.jjdltc.cordova.plugin.zip", "clobbers": ["JJzip"]}, {"id": "cordova-plugin-appinfo.AppInfo", "file": "plugins/cordova-plugin-appinfo/www/appinfo.js", "pluginId": "cordova-plugin-appinfo", "merges": ["navigator.appInfo"]}, {"id": "cordova-plugin-bluetooth-serial.bluetoothSerial", "file": "plugins/cordova-plugin-bluetooth-serial/www/bluetoothSerial.js", "pluginId": "cordova-plugin-bluetooth-serial", "clobbers": ["window.bluetoothSerial"]}, {"id": "cordova-plugin-camera.Camera", "file": "plugins/cordova-plugin-camera/www/CameraConstants.js", "pluginId": "cordova-plugin-camera", "clobbers": ["Camera"]}, {"id": "cordova-plugin-camera.CameraPopoverOptions", "file": "plugins/cordova-plugin-camera/www/CameraPopoverOptions.js", "pluginId": "cordova-plugin-camera", "clobbers": ["CameraPopoverOptions"]}, {"id": "cordova-plugin-camera.camera", "file": "plugins/cordova-plugin-camera/www/Camera.js", "pluginId": "cordova-plugin-camera", "clobbers": ["navigator.camera"]}, {"id": "cordova-plugin-camera.CameraPopoverHandle", "file": "plugins/cordova-plugin-camera/www/CameraPopoverHandle.js", "pluginId": "cordova-plugin-camera", "clobbers": ["CameraPopoverHandle"]}, {"id": "cordova-plugin-dialogs.notification", "file": "plugins/cordova-plugin-dialogs/www/notification.js", "pluginId": "cordova-plugin-dialogs", "merges": ["navigator.notification"]}, {"id": "cordova-plugin-dialogs.notification_android", "file": "plugins/cordova-plugin-dialogs/www/android/notification.js", "pluginId": "cordova-plugin-dialogs", "merges": ["navigator.notification"]}, {"id": "cordova-plugin-device.device", "file": "plugins/cordova-plugin-device/www/device.js", "pluginId": "cordova-plugin-device", "clobbers": ["device"]}, {"id": "cordova-plugin-code-push.codePush", "file": "plugins/cordova-plugin-code-push/bin/www/codePush.js", "pluginId": "cordova-plugin-code-push", "clobbers": ["codePush"]}, {"id": "cordova-plugin-code-push.localPackage", "file": "plugins/cordova-plugin-code-push/bin/www/localPackage.js", "pluginId": "cordova-plugin-code-push", "clobbers": ["LocalPackage"]}, {"id": "cordova-plugin-code-push.remotePackage", "file": "plugins/cordova-plugin-code-push/bin/www/remotePackage.js", "pluginId": "cordova-plugin-code-push", "clobbers": ["RemotePackage"]}, {"id": "cordova-plugin-code-push.syncStatus", "file": "plugins/cordova-plugin-code-push/bin/www/syncStatus.js", "pluginId": "cordova-plugin-code-push", "clobbers": ["SyncStatus"]}, {"id": "cordova-plugin-code-push.installMode", "file": "plugins/cordova-plugin-code-push/bin/www/installMode.js", "pluginId": "cordova-plugin-code-push", "clobbers": ["InstallMode"]}, {"id": "cordova-plugin-code-push.codePushUtil", "file": "plugins/cordova-plugin-code-push/bin/www/codePushUtil.js", "pluginId": "cordova-plugin-code-push", "runs": true}, {"id": "cordova-plugin-code-push.fileUtil", "file": "plugins/cordova-plugin-code-push/bin/www/fileUtil.js", "pluginId": "cordova-plugin-code-push", "runs": true}, {"id": "cordova-plugin-code-push.httpRequester", "file": "plugins/cordova-plugin-code-push/bin/www/httpRequester.js", "pluginId": "cordova-plugin-code-push", "runs": true}, {"id": "cordova-plugin-code-push.nativeAppInfo", "file": "plugins/cordova-plugin-code-push/bin/www/nativeAppInfo.js", "pluginId": "cordova-plugin-code-push", "runs": true}, {"id": "cordova-plugin-code-push.package", "file": "plugins/cordova-plugin-code-push/bin/www/package.js", "pluginId": "cordova-plugin-code-push", "runs": true}, {"id": "cordova-plugin-code-push.sdk", "file": "plugins/cordova-plugin-code-push/bin/www/sdk.js", "pluginId": "cordova-plugin-code-push", "runs": true}, {"id": "cordova-plugin-device-orientation.CompassError", "file": "plugins/cordova-plugin-device-orientation/www/CompassError.js", "pluginId": "cordova-plugin-device-orientation", "clobbers": ["CompassError"]}, {"id": "cordova-plugin-device-orientation.CompassHeading", "file": "plugins/cordova-plugin-device-orientation/www/CompassHeading.js", "pluginId": "cordova-plugin-device-orientation", "clobbers": ["CompassHeading"]}, {"id": "cordova-plugin-device-orientation.compass", "file": "plugins/cordova-plugin-device-orientation/www/compass.js", "pluginId": "cordova-plugin-device-orientation", "clobbers": ["navigator.compass"]}, {"id": "cordova-plugin-file.DirectoryEntry", "file": "plugins/cordova-plugin-file/www/DirectoryEntry.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.DirectoryEntry"]}, {"id": "cordova-plugin-file.DirectoryReader", "file": "plugins/cordova-plugin-file/www/DirectoryReader.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.DirectoryReader"]}, {"id": "cordova-plugin-file.Entry", "file": "plugins/cordova-plugin-file/www/Entry.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.Entry"]}, {"id": "cordova-plugin-file.File", "file": "plugins/cordova-plugin-file/www/File.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.File"]}, {"id": "cordova-plugin-file.FileEntry", "file": "plugins/cordova-plugin-file/www/FileEntry.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileEntry"]}, {"id": "cordova-plugin-file.FileError", "file": "plugins/cordova-plugin-file/www/FileError.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileError"]}, {"id": "cordova-plugin-file.FileReader", "file": "plugins/cordova-plugin-file/www/FileReader.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileReader"]}, {"id": "cordova-plugin-file.FileSystem", "file": "plugins/cordova-plugin-file/www/FileSystem.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileSystem"]}, {"id": "cordova-plugin-file.FileUploadOptions", "file": "plugins/cordova-plugin-file/www/FileUploadOptions.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileUploadOptions"]}, {"id": "cordova-plugin-file.FileUploadResult", "file": "plugins/cordova-plugin-file/www/FileUploadResult.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileUploadResult"]}, {"id": "cordova-plugin-file.FileWriter", "file": "plugins/cordova-plugin-file/www/FileWriter.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileWriter"]}, {"id": "cordova-plugin-file.Flags", "file": "plugins/cordova-plugin-file/www/Flags.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.Flags"]}, {"id": "cordova-plugin-file.LocalFileSystem", "file": "plugins/cordova-plugin-file/www/LocalFileSystem.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.LocalFileSystem"], "merges": ["window"]}, {"id": "cordova-plugin-file.Metadata", "file": "plugins/cordova-plugin-file/www/Metadata.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.Metadata"]}, {"id": "cordova-plugin-file.ProgressEvent", "file": "plugins/cordova-plugin-file/www/ProgressEvent.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.ProgressEvent"]}, {"id": "cordova-plugin-file.fileSystems", "file": "plugins/cordova-plugin-file/www/fileSystems.js", "pluginId": "cordova-plugin-file"}, {"id": "cordova-plugin-file.requestFileSystem", "file": "plugins/cordova-plugin-file/www/requestFileSystem.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.requestFileSystem"]}, {"id": "cordova-plugin-file.resolveLocalFileSystemURI", "file": "plugins/cordova-plugin-file/www/resolveLocalFileSystemURI.js", "pluginId": "cordova-plugin-file", "merges": ["window"]}, {"id": "cordova-plugin-file.isChrome", "file": "plugins/cordova-plugin-file/www/browser/isChrome.js", "pluginId": "cordova-plugin-file", "runs": true}, {"id": "cordova-plugin-file.androidFileSystem", "file": "plugins/cordova-plugin-file/www/android/FileSystem.js", "pluginId": "cordova-plugin-file", "merges": ["FileSystem"]}, {"id": "cordova-plugin-file.fileSystems-roots", "file": "plugins/cordova-plugin-file/www/fileSystems-roots.js", "pluginId": "cordova-plugin-file", "runs": true}, {"id": "cordova-plugin-file.fileSystemPaths", "file": "plugins/cordova-plugin-file/www/fileSystemPaths.js", "pluginId": "cordova-plugin-file", "merges": ["<PERSON><PERSON>"], "runs": true}, {"id": "cordova-plugin-file-transfer.FileTransferError", "file": "plugins/cordova-plugin-file-transfer/www/FileTransferError.js", "pluginId": "cordova-plugin-file-transfer", "clobbers": ["window.FileTransferError"]}, {"id": "cordova-plugin-file-transfer.FileTransfer", "file": "plugins/cordova-plugin-file-transfer/www/FileTransfer.js", "pluginId": "cordova-plugin-file-transfer", "clobbers": ["window.FileTransfer"]}, {"id": "cordova-plugin-geolocation.geolocation", "file": "plugins/cordova-plugin-geolocation/www/android/geolocation.js", "pluginId": "cordova-plugin-geolocation", "clobbers": ["navigator.geolocation"]}, {"id": "cordova-plugin-geolocation.PositionError", "file": "plugins/cordova-plugin-geolocation/www/PositionError.js", "pluginId": "cordova-plugin-geolocation", "runs": true}, {"id": "cordova-plugin-zip.Zip", "file": "plugins/cordova-plugin-zip/zip.js", "pluginId": "cordova-plugin-zip", "clobbers": ["zip"]}, {"id": "phonegap-plugin-barcodescanner.BarcodeScanner", "file": "plugins/phonegap-plugin-barcodescanner/www/barcodescanner.js", "pluginId": "phonegap-plugin-barcodescanner", "clobbers": ["cordova.plugins.barcodeScanner"]}, {"id": "phonegap-plugin-push.PushNotification", "file": "plugins/phonegap-plugin-push/www/push.js", "pluginId": "phonegap-plugin-push", "clobbers": ["PushNotification"]}], "plugin_metadata": {"code-push": "2.0.6", "com.jjdltc.cordova.plugin.zip": "0.0.4", "cordova-plugin-appinfo": "2.0.3", "cordova-plugin-bluetooth-serial": "0.4.7", "cordova-plugin-compat": "1.0.0", "cordova-plugin-camera": "2.2.0", "cordova-plugin-dialogs": "1.2.1", "cordova-plugin-device": "1.1.2", "cordova-plugin-code-push": "1.11.16", "cordova-plugin-device-orientation": "1.0.7", "cordova-plugin-file": "4.2.0", "cordova-plugin-file-transfer": "1.5.1", "cordova-plugin-geolocation": "2.2.0", "cordova-plugin-whitelist": "1.2.2", "cordova-plugin-zip": "3.1.0", "phonegap-plugin-barcodescanner": "6.0.8", "phonegap-plugin-push": "1.8.0"}}
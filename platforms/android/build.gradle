/* Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
*/

// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    apply from: 'CordovaLib/cordova.gradle'
    apply from: 'repositories.gradle'
    repositories repos
    dependencies {
        classpath "com.android.tools.build:gradle:${cordovaConfig.AGP_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${cordovaConfig.KOTLIN_VERSION}"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }

    cdvHelpers.verifyCordovaConfigForBuild()
}

allprojects {
    def hasRepositoriesGradle = file('repositories.gradle').exists()
    if (hasRepositoriesGradle) {
        apply from: 'repositories.gradle'
    } else {
        apply from: "${project.rootDir}/repositories.gradle"
    }

    repositories repos
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

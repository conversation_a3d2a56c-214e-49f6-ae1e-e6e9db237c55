// Needed to speed up asset listing: refer to https://github.com/apache/cordova-plugin-file/blob/76c8fd5e43598ea660c2db64eb08e2659b45a575/src/android/AssetFilesystem.java#L41
// Taken from https://raw.githubusercontent.com/apache/cordova-plugin-file/ed4cb4b999373eaa80b2fe1f38e3fc24db1b00ae/src/android/build-extras.gradle

/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
 */
ext.postBuildExtras = {
    
    def inAssetsDir = file("src/main/assets")
    if (!inAssetsDir.exists()) { // Add support for cordova-android < 7.0.0
        inAssetsDir = file("assets")
    }

    def outAssetsDir = inAssetsDir
    def outFile = new File(outAssetsDir, "cdvasset.manifest")

    def newTask = task("cdvCreateAssetManifest") << {
        def contents = new HashMap()
        def sizes = new HashMap()
        contents[""] = inAssetsDir.list()
        def tree = fileTree(dir: inAssetsDir)
        tree.visit { fileDetails ->
            if (fileDetails.isDirectory()) {
                contents[fileDetails.relativePath.toString()] = fileDetails.file.list()
            } else {
                sizes[fileDetails.relativePath.toString()] = fileDetails.file.length()
            }
        }

        outAssetsDir.mkdirs()
        outFile.withObjectOutputStream { oos ->
            oos.writeObject(contents)
            oos.writeObject(sizes)
        }
    }
    newTask.inputs.dir inAssetsDir
    newTask.outputs.file outFile
    def preBuildTask = tasks["preBuild"]
    preBuildTask.dependsOn(newTask)
}

android.buildTypes.each {
   // to prevent incorrect long value restoration from strings.xml we need to wrap it with double quotes
   // https://github.com/Microsoft/cordova-plugin-code-push/issues/264
   it.resValue "string", "CODE_PUSH_APK_BUILD_TIME", String.format("\"%d\"", System.currentTimeMillis())
}

dependencies {
    compile 'com.nimbusds:nimbus-jose-jwt:5.1'
}

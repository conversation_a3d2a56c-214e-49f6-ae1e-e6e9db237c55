/* Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
*/

ext {
    apply from: 'cordova.gradle'
    cdvCompileSdkVersion = privateHelpers.getProjectTarget()
    cdvBuildToolsVersion = privateHelpers.findLatestInstalledBuildTools()
    if (project.hasProperty('cdvMinSdkVersion') && cdvMinSdkVersion.isInteger()) {
        cdvMinSdkVersion = cdvMinSdkVersion as int
        println '[Cordova] cdvMinSdkVersion is overridden, try it at your own risk.'
    } else {
        cdvMinSdkVersion = 22; // current Cordova's default
    }
}

buildscript {
    apply from: 'repositories.gradle'
    repositories repos

    dependencies {
        // The gradle plugin and the maven plugin have to be updated after each version of Android
        // studio comes out
        classpath 'com.android.tools.build:gradle:4.0.0'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.7.3'
    }
}

allprojects {
    apply from: 'repositories.gradle'
    repositories repos
}

apply plugin: 'com.android.library'
apply plugin: 'com.github.dcendents.android-maven'
apply plugin: 'com.jfrog.bintray'

group = 'org.apache.cordova'
version = '9.1.0'

android {
    compileSdkVersion cdvCompileSdkVersion
    buildToolsVersion cdvBuildToolsVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // For the Android Cordova Lib, we allow changing the minSdkVersion, but it is at the users own risk
    defaultConfig {
        minSdkVersion cdvMinSdkVersion
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src']
            resources.srcDirs = ['src']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
        }
    }

    packagingOptions {
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
    }
}

install {
    repositories.mavenInstaller {
        pom {
            project {
                packaging 'aar'
                name 'Cordova'
                url 'https://cordova.apache.org'
                licenses {
                    license {
                        name 'The Apache Software License, Version 2.0'
                        url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }
                developers {
                    developer {
                        id 'stevengill'
                        name 'Steve Gill'
                    }
                }
                scm {
                    connection 'scm:git:https://github.com/apache/cordova-android.git'
                    developerConnection 'scm:git:**************:apache/cordova-android.git'
                    url 'https://github.com/apache/cordova-android'

                }
            }
        }
    }
}

task sourcesJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier = 'sources'
}

artifacts {
    archives sourcesJar
}

bintray {
    user = System.getenv('BINTRAY_USER')
    key = System.getenv('BINTRAY_KEY')
    configurations = ['archives']
    pkg {
        repo = 'maven'
        name = 'cordova-android'
        userOrg = 'cordova'
        licenses = ['Apache-2.0']
        vcsUrl = 'https://github.com/apache/cordova-android'
        websiteUrl = 'https://cordova.apache.org'
        issueTrackerUrl = 'https://github.com/apache/cordova-android/issues'
        publicDownloadNumbers = true
        licenses = ['Apache-2.0']
        labels = ['android', 'cordova', 'phonegap']
        version {
            name = '9.1.0'
            released = new Date()
            vcsTag = '9.1.0'
        }
    }
}

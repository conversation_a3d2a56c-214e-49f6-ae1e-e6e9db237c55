/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/

import java.util.regex.Pattern
import io.github.g00fy2.versioncompare.Version

String doEnsureValueExists(filePath, props, key) {
    if (props.get(key) == null) {
        throw new GradleException(filePath + ': Missing key required "' + key + '"')
    }
    return props.get(key)
}

String doGetProjectTarget() {
    def props = new Properties()
    def propertiesFile = 'project.properties';
    if(!(file(propertiesFile).exists())) {
      propertiesFile = '../project.properties';
    }
    file(propertiesFile).withReader { reader ->
        props.load(reader)
    }
    return doEnsureValueExists('project.properties', props, 'target')
}

Boolean isVersionValid(version) {
    return !(new Version(version)).isEqual('0.0.0')
}

String doFindLatestInstalledBuildTools(String minBuildToolsVersionString) {
    def buildToolsDirContents
    try {
        def buildToolsDir = new File(getAndroidSdkDir(), "build-tools")
        buildToolsDirContents = buildToolsDir.list()
    } catch (e) {
        println "An exception occurred while trying to find the Android build tools."
        throw e
    }

    def minBuildToolsVersion = new Version(minBuildToolsVersionString)
    def maxVersion = new Version((minBuildToolsVersion.getMajor() + 1) + ".0.0")

    def highestBuildToolsVersion = buildToolsDirContents
        .collect { new Version(it) }
        // Invalid inputs will be handled as 0.0.0
        .findAll { it.isHigherThan('0.0.0') && it.isLowerThan(maxVersion) }
        .max()

    if (highestBuildToolsVersion == null) {
        throw new RuntimeException("""
            No installed build tools found. Please install the Android build tools
            version ${minBuildToolsVersionString}.
        """.replaceAll(/\s+/, ' ').trim())
    }

    if (highestBuildToolsVersion.isLowerThan(minBuildToolsVersionString)) {
        throw new RuntimeException("""
            No usable Android build tools found. Highest ${minBuildToolsVersion.getMajor()}.x installed version is
            ${highestBuildToolsVersion.getOriginalString()}; Recommended version
            is ${minBuildToolsVersionString}.
        """.replaceAll(/\s+/, ' ').trim())
    }

    highestBuildToolsVersion.getOriginalString()
}

String getAndroidSdkDir() {
    def rootDir = project.rootDir
    def androidSdkDir = null
    String envVar = System.getenv("ANDROID_HOME")
    if (envVar == null) {
        envVar = System.getenv("ANDROID_SDK_ROOT")
    }

    def localProperties = new File(rootDir, 'local.properties')
    String systemProperty = System.getProperty("android.home")
    if (envVar != null) {
        androidSdkDir = envVar
    } else if (localProperties.exists()) {
        Properties properties = new Properties()
        localProperties.withInputStream { instr ->
            properties.load(instr)
        }
        def sdkDirProp = properties.getProperty('sdk.dir')
        if (sdkDirProp != null) {
            androidSdkDir = sdkDirProp
        } else {
            sdkDirProp = properties.getProperty('android.dir')
            if (sdkDirProp != null) {
                androidSdkDir = (new File(rootDir, sdkDirProp)).getAbsolutePath()
            }
        }
    }
    if (androidSdkDir == null && systemProperty != null) {
        androidSdkDir = systemProperty
    }
    if (androidSdkDir == null) {
        throw new RuntimeException(
            "Unable to determine Android SDK directory.")
    }
    androidSdkDir
}

def doExtractIntFromManifest(name) {
    def manifestFile = file(android.sourceSets.main.manifest.srcFile)
    def pattern = Pattern.compile(name + "=\"(\\d+)\"")
    def matcher = pattern.matcher(manifestFile.getText())
    matcher.find()
    return new BigInteger(matcher.group(1))
}

def doGetConfigXml() {
    def xml = file("src/main/res/xml/config.xml").getText()
    // Disable namespace awareness since Cordova doesn't use them properly
    return new XmlParser(false, false).parseText(xml)
}

def doGetConfigPreference(name, defaultValue) {
    name = name.toLowerCase()
    def root = doGetConfigXml()

    def ret = defaultValue
    root.preference.each { it ->
        def attrName = it.attribute("name")
        if (attrName && attrName.toLowerCase() == name) {
            ret = it.attribute("value")
        }
    }
    return ret
}

def doApplyCordovaConfigCustomization() {
    // Apply user overide properties that comes from the "--gradleArg=-P" parameters
    if (project.hasProperty('cdvMinSdkVersion')) {
        cordovaConfig.MIN_SDK_VERSION = Integer.parseInt('' + cdvMinSdkVersion)
    }
    if (project.hasProperty('cdvSdkVersion')) {
        cordovaConfig.SDK_VERSION = Integer.parseInt('' + cdvSdkVersion)
    }
    if (project.hasProperty('cdvCompileSdkVersion')) {
        cordovaConfig.COMPILE_SDK_VERSION = Integer.parseInt('' + cdvCompileSdkVersion)
    }
    if (project.hasProperty('cdvMaxSdkVersion')) {
        cordovaConfig.MAX_SDK_VERSION = Integer.parseInt('' + cdvMaxSdkVersion)
    }
    if (project.hasProperty('cdvBuildToolsVersion')) {
        cordovaConfig.BUILD_TOOLS_VERSION = cdvBuildToolsVersion
    }
    if (project.hasProperty('cdvAndroidXAppCompatVersion')) {
        cordovaConfig.ANDROIDX_APP_COMPAT_VERSION = cdvAndroidXAppCompatVersion
    }
    if (project.hasProperty('cdvAndroidXWebKitVersion')) {
        cordovaConfig.ANDROIDX_WEBKIT_VERSION = cdvAndroidXWebKitVersion
    }

    if (!cordovaConfig.BUILD_TOOLS_VERSION) {
        cordovaConfig.BUILD_TOOLS_VERSION = doFindLatestInstalledBuildTools(
            cordovaConfig.MIN_BUILD_TOOLS_VERSION
        )
    }

    // Ensure the configured build tools version is at least our declared minimum
    def buildToolsVersion = new Version(cordovaConfig.BUILD_TOOLS_VERSION)
    if (buildToolsVersion.isLowerThan(cordovaConfig.MIN_BUILD_TOOLS_VERSION)) {
        throw new RuntimeException("""
            Expected Android Build Tools version >= ${cordovaConfig.MIN_BUILD_TOOLS_VERSION},
            but got Android Build Tools version ${cordovaConfig.BUILD_TOOLS_VERSION}. Please use version ${cordovaConfig.MIN_BUILD_TOOLS_VERSION} or later.
        """.replaceAll(/\s+/, ' ').trim())
    }
}

def doVerifyCordovaConfigForBuild() {
    if (cordovaConfig.COMPILE_SDK_VERSION < cordovaConfig.SDK_VERSION) {
        println "The \"compileSdkVersion\" (${cordovaConfig.COMPILE_SDK_VERSION}) should be greater than or equal to the the \"targetSdkVersion\" (${cordovaConfig.SDK_VERSION})."
    }
}

// Properties exported here are visible to all plugins.
ext {
    def defaultsFilePath = './cdv-gradle-config-defaults.json'
    def projectConfigFilePath = "$rootDir/cdv-gradle-config.json"
    def targetConfigFilePath = null

    /**
     * Check if the project config file path exists. This file will exist if coming from CLI project.
     * If this file does not exist, falls back onto the default file.
     * This scenario can occur if building the framework's AAR package for publishing.
     */
    if(file(projectConfigFilePath).exists()) {
        targetConfigFilePath = projectConfigFilePath
    } else {
        targetConfigFilePath = defaultsFilePath
    }

    def jsonFile = new File(targetConfigFilePath)
    cordovaConfig = new groovy.json.JsonSlurper().parseText(jsonFile.text)

    if (cordovaConfig.COMPILE_SDK_VERSION == null) {
        cordovaConfig.COMPILE_SDK_VERSION = cordovaConfig.SDK_VERSION
    }

    // Apply Gradle Properties
    doApplyCordovaConfigCustomization()

    // These helpers are shared, but are not guaranteed to be stable / unchanged.
    privateHelpers = {}
    privateHelpers.getProjectTarget = { doGetProjectTarget() }
    privateHelpers.applyCordovaConfigCustomization = { doApplyCordovaConfigCustomization() }
    privateHelpers.extractIntFromManifest = { name -> doExtractIntFromManifest(name) }
    privateHelpers.ensureValueExists = { filePath, props, key -> doEnsureValueExists(filePath, props, key) }

    // These helpers can be used by plugins / projects and will not change.
    cdvHelpers = {}
    // Returns a XmlParser for the config.xml. Added in 4.1.0.
    cdvHelpers.getConfigXml = { doGetConfigXml() }
    // Returns the value for the desired <preference>. Added in 4.1.0.
    cdvHelpers.getConfigPreference = { name, defaultValue -> doGetConfigPreference(name, defaultValue) }
    // Display warnings if any cordova config is not proper for build.
    cdvHelpers.verifyCordovaConfigForBuild = { doVerifyCordovaConfigForBuild() }
}

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'io.github.g00fy2:versioncompare:1.4.1@jar'
    }
}

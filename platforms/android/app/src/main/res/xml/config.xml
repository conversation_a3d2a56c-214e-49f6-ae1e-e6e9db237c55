<?xml version='1.0' encoding='utf-8'?>
<widget id="com.agrobalance" version="1.6.3" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <feature name="JJzip">
        <param name="android-package" value="com.jjdltc.cordova.plugin.zip.JJzip" />
    </feature>
    <feature name="AppInfo">
        <param name="android-package" value="org.scriptotek.appinfo.AppInfo" />
    </feature>
    <feature name="Camera">
        <param name="android-package" value="org.apache.cordova.camera.CameraLauncher" />
    </feature>
    <feature name="Notification">
        <param name="android-package" value="org.apache.cordova.dialogs.Notification" />
    </feature>
    <feature name="Device">
        <param name="android-package" value="org.apache.cordova.device.Device" />
    </feature>
    <feature name="Compass">
        <param name="android-package" value="org.apache.cordova.deviceorientation.CompassListener" />
    </feature>
    <feature name="File">
        <param name="android-package" value="org.apache.cordova.file.FileUtils" />
        <param name="onload" value="true" />
    </feature>
    <feature name="FileTransfer">
        <param name="android-package" value="org.apache.cordova.filetransfer.FileTransfer" />
    </feature>
    <feature name="Geolocation">
        <param name="android-package" value="org.apache.cordova.geolocation.Geolocation" />
    </feature>
    <feature name="Whitelist">
        <param name="android-package" value="org.apache.cordova.whitelist.WhitelistPlugin" />
        <param name="onload" value="true" />
    </feature>
    <feature name="BarcodeScanner">
        <param name="android-package" value="com.phonegap.plugins.barcodescanner.BarcodeScanner" />
    </feature>
    <feature name="PushNotification">
        <param name="android-package" value="com.adobe.phonegap.push.PushPlugin" />
    </feature>
    <feature name="BluetoothSerial">
        <param name="android-package" value="com.megster.cordova.BluetoothSerial" />
    </feature>
    <feature name="Zip">
        <param name="android-package" value="org.apache.cordova.Zip" />
    </feature>
    <feature name="CodePush">
        <param name="android-package" value="com.microsoft.cordova.CodePush" />
        <param name="onload" value="true" />
    </feature>
    <name>Agrobalance</name>
    <description>
        A sample Apache Cordova application that responds to the deviceready event.
    </description>
    <author email="<EMAIL>" href="http://cordova.io">
        Apache Cordova Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <allow-intent href="market:*" />
    <icon density="ldpi" src="assets/ldpi.png" />
    <icon density="mdpi" src="assets/mdpi.png" />
    <icon density="hdpi" src="assets/hdpi.png" />
    <preference name="loglevel" value="DEBUG" />
    <preference name="AndroidPersistentFileLocation" value="Compatibility" />
    <preference name="android-minSdkVersion" value="22" />
    <preference name="android-compileSdkVersion" value="33" />
    <preference name="android-targetSdkVersion" value="33" />
    <preference name="GradlePluginVersion" value="7.2.1" />
    <preference name="AndroidGradlePluginVersion" value="7.2.1" />
    <preference name="CodePushDeploymentKey" value="BTu3CbwamHfWdSvJWlxXHVnsUjWzee06d68c-4c51-463e-8a3a-e56596a9ecaa" />
</widget>

package com.adobe.phonegap.push;

public interface PushConstants {
    public static final String COM_ADOBE_PHONEGAP_PUSH = "com.adobe.phonegap.push";
    public static final String REGISTRATION_ID = "registrationId";
    public static final String FOREGROUND = "foreground";
    public static final String TITLE = "title";
    public static final String NOT_ID = "notId";
    public static final String PUSH_BUNDLE = "pushBundle";
    public static final String ICON = "icon";
    public static final String ICON_COLOR = "iconColor";
    public static final String SOUND = "sound";
    public static final String SOUND_DEFAULT = "default";
    public static final String SOUND_RINGTONE = "ringtone";
    public static final String VIBRATE = "vibrate";
    public static final String ACTIONS = "actions";
    public static final String CALLBACK = "callback";
    public static final String ACTION_CALLBACK = "actionCallback";
    public static final String DRAWABLE = "drawable";
    public static final String MSGCNT = "msgcnt";
    public static final String VIBRATION_PATTERN = "vibrationPattern";
    public static final String STYLE = "style";
    public static final String SUMMARY_TEXT = "summaryText";
    public static final String PICTURE = "picture";
    public static final String GCM_N = "gcm.n.";
    public static final String GCM_NOTIFICATION = "gcm.notification";
    public static final String GCM_NOTIFICATION_BODY = "gcm.notification.body";
    public static final String UA_PREFIX = "com.urbanairship.push";
    public static final String PARSE_COM_DATA = "data";
    public static final String ALERT = "alert";
    public static final String MESSAGE = "message";
    public static final String BODY = "body";
    public static final String SOUNDNAME = "soundname";
    public static final String LED_COLOR = "ledColor";
    public static final String PRIORITY = "priority";
    public static final String IMAGE = "image";
    public static final String STYLE_INBOX = "inbox";
    public static final String STYLE_PICTURE = "picture";
    public static final String STYLE_TEXT = "text";
    public static final String BADGE = "badge";
    public static final String INITIALIZE = "init";
    public static final String UNREGISTER = "unregister";
    public static final String EXIT = "exit";
    public static final String FINISH = "finish";
    public static final String HAS_PERMISSION = "hasPermission";
    public static final String ANDROID = "android";
    public static final String SENDER_ID = "senderID";
    public static final String CLEAR_BADGE = "clearBadge";
    public static final String CLEAR_NOTIFICATIONS = "clearNotifications";
    public static final String COLDSTART = "coldstart";
    public static final String ADDITIONAL_DATA = "additionalData";
    public static final String COUNT = "count";
    public static final String FROM = "from";
    public static final String COLLAPSE_KEY = "collapse_key";
    public static final String FORCE_SHOW = "forceShow";
    public static final String GCM = "GCM";
    public static final String CONTENT_AVAILABLE = "content-available";
    public static final String TOPICS = "topics";
    public static final String SET_APPLICATION_ICON_BADGE_NUMBER = "setApplicationIconBadgeNumber";
    public static final String CLEAR_ALL_NOTIFICATIONS = "clearAllNotifications";
    public static final String VISIBILITY = "visibility";
    public static final String INLINE_REPLY = "inlineReply";
}

// Filename: app.js
define([
    'jquery',
    'underscore',
    'backbone',
    'router', // Request router.js
    'models/SessionModel',
    'jquerymobile',
    'fileStorage',
    'slick'
], function ($, _, Backbone, Router, SessionModel) {
    var initialize = function () {
        //logout if user was logged and token is expired
        $(document).ajaxError(function(event, request, options) {
            if(localStorage.getItem('isLogged') == 'true' && (request.status == 401 ||request.status == 403)) {
                    localStorage.removeItem('isLogged');
                    localStorage.removeItem('authData');
                    Backbone.history.navigate('login', {trigger: true});
            }
        });

        proj4.defs("EPSG:32635", "+proj=utm +zone=35 +ellps=WGS84 +datum=WGS84 +units=m +no_defs");

        $.mobile.defaultPageTransition = 'slide';
        $.mobile.page.prototype.options.domCache = true;

        // Prevents all anchor click handling
        $.mobile.linkBindingEnabled = false;

        // Disabling this will prevent jQuery Mobile from handling hash changes
        $.mobile.hashListeningEnabled = false;

        //disable default jquery mobile navigation
        $(document).on('pagecontainerbeforeload', function (e) {
            e.preventDefault();
            e.stopPropagation();
        });
        
        //Set language
        if (!localStorage.getItem('locale')) {
            localStorage.setItem('locale', Constants.default_launguage);
        }
        
        //Set locale for dates
        moment.locale(localStorage.getItem('locale'));

        //Set map
        if (!localStorage.getItem('map_type')) {
            localStorage.setItem('map_type', 'geoscan');
        }

        // Pass in our Router module and call it's initialize function
        Router.initialize();
    };

    return {
        initialize: initialize
    };
});

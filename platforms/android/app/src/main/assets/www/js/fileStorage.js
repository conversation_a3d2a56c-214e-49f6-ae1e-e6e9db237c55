fileStorage = (function () {

    var publicMethods = {};
    var MAX_SIZE = 3145738;

    publicMethods.getItem = function (key, callback) {
        var value = localStorage.getItem(key);

        if(value !== 'saved_in_file') {
            callback(JSON.parse(value));
            return;
        }

        window.resolveLocalFileSystemURL(cordova.file.dataDirectory, function (dir) {
            dir.getFile(key + '.txt', {create: true}, function (file) {
                file.file(function (file) {
                    var reader = new FileReader();

                    reader.onloadend = function (e) {
                        callback(JSON.parse(this.result));
                    };

                    reader.onerror = function (e) {
                        console.error('Read failed: ' + e.toString());
                    };

                    reader.readAsText(file);
                }, function (e) {
                    console.error(e);
                });
            }, function (e) {
                console.error(e);
            });
        }, function (e) {
            console.error(e);
        });
    };

    publicMethods.setItem = function (key, value, callback) {
        var valueStr = JSON.stringify(value);

        try {
            if(valueStr.length > MAX_SIZE) {
                throw 'Too bug';
            }

            localStorage.setItem(key, valueStr);
            if(callback) {
                callback();
            }
        }
        catch(err) {
            localStorage.setItem(key, 'saved_in_file');

            window.resolveLocalFileSystemURL(cordova.file.dataDirectory, function (dir) {
                dir.getFile(key + '.txt', {create: true}, function (file) {
                    file.createWriter(function (fileWriter) {

                        fileWriter.onwriteend = function (e) {
                            if(callback) {
                                callback();
                            }
                        };

                        fileWriter.onerror = function (e) {
                            console.error('Write failed: ' + e.toString());
                        };

                        var blob;
                        try {
                            blob = new Blob([valueStr], {type: 'text/plain'});
                        } catch (e) {
                            // TypeError old chrome and FF
                            window.BlobBuilder = window.BlobBuilder ||
                                    window.WebKitBlobBuilder ||
                                    window.MozBlobBuilder ||
                                    window.MSBlobBuilder;
                            if (e.name == 'TypeError' && window.BlobBuilder) {
                                var bb = new BlobBuilder();
                                bb.append([valueStr]);
                                blob = bb.getBlob("text/plain");
                            } else if (e.name == "InvalidStateError") {
                                // InvalidStateError (tested on FF13 WinXP)
                                blob = new Blob([valueStr], {type: "text/plain"});
                            } else {
                                // We're screwed, blob constructor unsupported entirely
                                console.error('Blob constructor unsupported entirely : ' + e.toString());
                            }
                        }

                        fileWriter.write(blob);

                    }, function (e) {
                        console.error(e);
                    });
                }, function (e) {
                    console.error(e);
                });
            }, function (e) {
                console.error(e);
            });
        }


        return;
    };

    return publicMethods;
})();
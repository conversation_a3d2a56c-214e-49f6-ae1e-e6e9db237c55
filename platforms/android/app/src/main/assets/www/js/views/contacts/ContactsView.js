define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/contacts/contactsTemplate.html'
], function($, _, Backbone, Localization, contactsTemplate){

	var ContactsView = Backbone.View.extend({
		el: $("#page"),
		initialize: function() {
			
		},
		render: function(){
			var data = {'Localization':Localization};
			if(typeof contactsTemplate == 'string') {
				contactsTemplate = _.template(contactsTemplate);
			}
			this.$el.append(contactsTemplate(data)).trigger('create');
			$('h1.menu-title').text(Localization.contacts);
		}
	});
	return new ContactsView();
});

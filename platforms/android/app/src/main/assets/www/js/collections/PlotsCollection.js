define([
    'jquery',
    'backbone',
    'router'
], function ($, Backbone) {

    var PlotsCollection = Backbone.Collection.extend({
        url: '',
        fileTransfer: undefined,
        initialize: function (options) {
            var authData = JSON.parse(localStorage.getItem('authData'));

            this.url = Constants.servers[authData.server].ajax_url + options.url;
            this.syncAll = options.syncAll;

            try
            {
                this.fileTransfer = new FileTransfer();
            } catch (err)
            {
                console.log(err);
            }
        },
        getPlots: function (options) {
            var self = this;
            var authData = JSON.parse(localStorage.getItem('authData'));

            $.ajax({
                url: this.url,
                headers: {
                    'Authorization': 'Bearer ' + authData.access_token,
                },
                data: options.data,
                type: options.type,
                success: function (response) {
                    var plotsData = response;
                    var tiledates = {};

                    var images = [];
                    var chartImages = [];
                    var soilImages = [];
// @stub
                    var stubbed_plot = fileStorage.getStubbedPlot();
                    plotsData.push(stubbed_plot);
// @end_stub
                    plotsData.forEach(function (plot) {
                        if (plot.type === 'index') {
                            images = images.concat(plot.images_data);
                            chartImages = chartImages.concat(plot.stats_data);
                        }
                        if (plot.type === 'soil') {
                            soilImages = soilImages.concat(plot.images_data);
                        }
                    });

                    self.remoteImages = images.slice();

                    self.remoteImages = self.remoteImages.concat(soilImages.slice());

                    self.remoteImages = self.remoteImages.concat(chartImages.slice());

                    images.forEach(function (image) {
                        if (cordova.file) {
                            image.fileURL = cordova.file.dataDirectory + authData.server + '/' + authData.user_id + '/' + image.date + image.url.substring(image.url.lastIndexOf('/'));
                        } else {
                            image.fileURL = image.url;
                        }

                        //generate array with all available tile dates
                        tiledates[image.date] = image.date;
                    });
                    
                    var oldTiledates = JSON.parse(localStorage.getItem('tiledates_' + localStorage.getItem('user_id')));
                    
                    var newTiledates = _.extend({}, oldTiledates, tiledates);
                    localStorage.setItem('tiledates_' + localStorage.getItem('user_id'), JSON.stringify(newTiledates));

                    chartImages.forEach(function (image) {
                        if (cordova.file) {
                            image.fileURL = cordova.file.dataDirectory + authData.server + '/' + authData.user_id + '/' + image.date + image.url.substring(image.url.lastIndexOf('/'));
                        } else {
                            image.fileURL = image.url;
                        }
                    });

                    soilImages.forEach(function (image) {
                        if (cordova.file) {
                            image.fileURL = cordova.file.dataDirectory + authData.server + '/' + authData.user_id + '/' + image.date + image.url.substring(image.url.lastIndexOf('/'));
                        } else {
                            image.fileURL = image.url;
                        }
                    });
                    
                    fileStorage.getItem('plots_data_' + localStorage.getItem('user_id'), function(oldPlotData) {
                        var filteredPlots = _.filter(oldPlotData, function (plot) {
                            return plot.year !== options.data.year;
                        });
                        
                        var newPlotData = [].concat(filteredPlots, plotsData);
                        
                        fileStorage.setItem('plots_data_' + localStorage.getItem('user_id'), newPlotData);
                    
                        if (cordova.file) {
                            Backbone.trigger('gs:startImagesDownloading', self.remoteImages.length);
                            self.dowloadImages();
                        }
                        else {
                            Backbone.trigger('gs:endImagesDownloading');
                        }
                    });
                },
                error: function (response) {
                    if (response.status != 401 && response.statusText != 'Unauthorized') {
                        Backbone.trigger('gs:syncPlotsFail');
                    }
                }
            });
        },
        dowloadImages: function () {
            var self = this;
            // No files left, stop downloading
            if (this.remoteImages.length == 0) {
                Backbone.trigger('gs:endImagesDownloading');
                return;
            }

            var authData = JSON.parse(localStorage.getItem('authData'));
            var image = this.remoteImages.pop();

            image.fileURL = cordova.file.dataDirectory + authData.server + '/' + authData.user_id + '/' + image.date + image.url.substring(image.url.lastIndexOf('/'));

            if (this.syncAll) {
                self.fileTransfer.download(
                        encodeURI(image.url),
                        image.fileURL,
                        function (entry) {
                            Backbone.trigger('gs:imageDownloadProgress');
                            self.dowloadImages();
                        },
                        function (error) {
                            self.dowloadImages();
                        }
                );
            } else {
                window.resolveLocalFileSystemURL(image.fileURL,
                        function () {
                            Backbone.trigger('gs:imageDownloadProgress');
                            self.dowloadImages();
                        },
                        function () {
                            self.fileTransfer.download(
                                    encodeURI(image.url),
                                    image.fileURL,
                                    function (entry) {
                                        Backbone.trigger('gs:imageDownloadProgress');
                                        self.dowloadImages();
                                    },
                                    function (error) {
                                        self.dowloadImages();
                                    }
                            );
                        }
                );
            }
        }
    });

    return PlotsCollection;
});
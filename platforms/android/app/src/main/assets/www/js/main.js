// Filename: main.js

// Require.js allows us to configure shortcut alias
// There usage will become more apparent further along in the tutorial.
require.config({
    locale: localStorage.getItem('locale') ? localStorage.getItem('locale') : Constants.default_launguage,
    paths: {
        jquery: 'libs/jquery/jquery-2.1.3.min',
        jquerymobile: 'libs/jquery/jquery.mobile-1.4.5',
        underscore: 'libs/underscore/underscore',
        backbone: 'libs/backbone/backbone',
        i18n: 'libs/plugins/i18n',
        text: 'libs/plugins/text',
        slick: "libs/slick/slick.min",
        templates: '../templates',
        proj4: 'libs/openlayers/proj4',
        ol: 'libs/openlayers/ol-debug',
        constants: 'constants',
        fileStorage: 'fileStorage',
        noty: 'libs/noty/jquery.noty.packaged.min',
        jqueryuidatepicker: 'libs/jquery/jquery.ui.datepicker',
        jquerymobiledatepicker: 'libs/jquery/jquery.mobile.datepicker',
        moment: 'libs/moment/moment-with-locales',
        base64binary: 'libs/plugins/base64-binary'
    }
});

require([
    'app'
],
        function (App) {
            // The "app" dependency is passed in as "App"
            document.addEventListener("deviceready", onDeviceReady, false);

            function onDeviceReady() {
                codePush.sync();
                App.initialize();
            }
        });
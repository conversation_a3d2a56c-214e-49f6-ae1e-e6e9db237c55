define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/task_plots/taskPlotsTemplate.html',
    'views/map/Map',
    'views/widgets/MapToolBarWidget',
    'collections/ExternalLocationProviderCollection',
    'collections/NotificationsCollection'
], function ($, _, Backbone, Localization, taskPlotsTemplate, Map, MapToolBarWidget, ExternalLocationProvider, NotificationsCollection) {

    var TaskPlotsView = Backbone.View.extend({
        events: {
        },
        el: $("#page"),
        user_id: localStorage.getItem('user_id'),
        taskData: null,
        tasksData: [],
        plotsData: undefined,
        selectedPlotId: undefined,
        isLocating: false,
        map: undefined,
        locationProvider: undefined,
        autoZoom: false,
        autoPan: false,
        isIntervalSet: false,
        viewport: {},
        initialize: function () {

            this.undelegateEvents();
            var self = this;

            Backbone.on('gs:displayPlotData', function(gid) {
                this.openPlotDataWindow(gid);
            }, self);

            Backbone.on('gs:taskPlotaGeoLocate', function() {
                this.geoLocate();
            }, self);

            Backbone.on('gs:zoomToTaskPlotsExtent', function() {
                this.zoomToPlotsExtent();
            }, self);

            this.viewport = {
                width: $(window).width(),
                height: $(window).height() - 108
            };
        },
        render: function (task_id) {
            if (typeof taskPlotsTemplate == 'string') {
                taskPlotsTemplate = _.template(taskPlotsTemplate);
            }

            var toolbarData = {
                'isMapPage': false
            }
            MapToolBarWidget.render(toolbarData);
            this.geoLocateBtn = $('#geoLocate');
            var data = {
                'viewport': this.viewport,
                'Localization': Localization,
                'treatment_types': Localization.treatment_types,
            };

            this.$el.append(taskPlotsTemplate(data)).trigger('create').trigger('resize');

            $('h1.menu-title').text(Localization.plots);

            var self = this;

            self.mapObj = new Map();
            self.mapObj.setMapType();

            fileStorage.getItem('tasks_data_' + this.user_id, function(tasks) {
                self.taskData = tasks.find(task => task.id == task_id);

                if(self.taskData){
                    self.plotsData = self.taskData.plots;
                    self.loadPlots();
                }
            });

            $(window).on('resize', function() {
                $('#map').css({
                    width: $(window).width(),
                    height: $(window).height() - 108
                });

                self.mapObj.olMap.updateSize();
            });

            $('#start-work-btn').off('click').on('click', $.proxy(this.startWorkRedirect, this));

            this.$el.trigger('create').trigger('resize');

            this.initDisplayMarkerOptions();
            this.initLocationProvider();
        },
        renderAllPlots: function () {
            if (typeof taskPlotsTemplate == 'string') {
                taskPlotsTemplate = _.template(taskPlotsTemplate);
            }

            var toolbarData = {
                'isMapPage': false
            }
            MapToolBarWidget.render(toolbarData);
            this.geoLocateBtn = $('#geoLocate');
            var data = {
                'viewport': this.viewport,
                'Localization': Localization,
                'treatment_types': Localization.treatment_types,
            };

            this.$el.append(taskPlotsTemplate(data)).trigger('create').trigger('resize');

            $('h1.menu-title').text(Localization.plots);

            var self = this;

            self.mapObj = new Map();
            self.mapObj.setMapType();

            fileStorage.getItem('tasks_data_' + this.user_id, function(tasks) {
                self.tasksData = tasks;
                var plots = [];

                tasks.forEach(function (task) {
                    plots = [...plots, ...task.plots];
                });

                self.plotsData = plots
                self.loadPlots();
            });

            $(window).on('resize', function() {
                $('#map').css({
                    width: $(window).width(),
                    height: $(window).height() - 108
                });

                self.mapObj.olMap.updateSize();
            });

            $('#start-work-btn').off('click').on('click', $.proxy(this.startWorkRedirect, this));

            this.$el.trigger('create').trigger('resize');

            this.initDisplayMarkerOptions();
            this.initLocationProvider();
        },
        loadPlots: function() {

            this.mapObj.addPlots(this.plotsData);

            this.zoomToPlotsExtent();
        },
        zoomToPlotsExtent: function () {
            var extent = this.mapObj.plotsVectorLayer.getDataExtent();
            if(!extent) {
                extent = JSON.parse(localStorage.getItem('ORGANIZATION_EXTENT'));
            }
            this.mapObj.olMap.zoomToExtent(extent);
        },
        openPlotDataWindow: function(gid) {
            var page_name = localStorage.getItem('page_name');
            if(page_name == 'task_plots') {
                selectedPlot = this.plotsData.find(plot => plot.plot_gid == gid);
                this.selectedPlotId = gid;

                var plot_sample_types_array = selectedPlot.sample_type_json;
                var treatment_type_label = plot_sample_types_array.map(type => type.short_name).join('; ');
                var plotName = selectedPlot.plot_name || Localization.no_name;

                if(this.tasksData.length) {
                    this.taskData = this.tasksData.find(task => task.plots.some(plot  => plot.plot_gid == gid));
                }

                var contractTy = selectedPlot.comment ? selectedPlot.comment : ' - ';
                var ekatte = selectedPlot.ekatte ? selectedPlot.ekatte : ' - ';
                var demo = selectedPlot.demo_sampling ? Localization.yes : Localization.no;
                var cellsCount = selectedPlot.sample_points.features.length ?? ' - ';
                $('#plotInfoAdvanced .ui-block-b > ul').empty();
                $("#plotInfoAdvanced .ui-block-b > ul").append('<li>' + this.taskData.order_name + '</li>');
                $("#plotInfoAdvanced .ui-block-b > ul").append('<li>' + plotName +'</li>');
                $("#plotInfoAdvanced .ui-block-b > ul").append('<li>' + ekatte + '</li>');
                $("#plotInfoAdvanced .ui-block-b > ul").append('<li>' + contractTy + '</li>');
                $("#plotInfoAdvanced .ui-block-b > ul").append('<li>' + treatment_type_label + '</li>');
                $("#plotInfoAdvanced .ui-block-b > ul").append('<li>' + cellsCount +'</li>');
                $("#plotInfoAdvanced .ui-block-b > ul").append('<li>' + demo+'</li>');
                $('#plotInfoAdvanced .ui-block-b > ul').listview("refresh").trigger('create').trigger('resize');

                var features = this.mapObj.olMap.getLayersByName('plots_vector_layer')[0].getFeaturesByAttribute('id', parseInt(gid));
                var centroid = features[0].geometry.getCentroid();

                var point = new OpenLayers.Geometry.Point(centroid.x, centroid.y).transform('EPSG:3857', 'EPSG:4326');

                $('#task-plots-google-maps-route-btn')[0].setAttribute('href', "http://maps.google.com/?q=" + point.y + "N," + point.x + "E");

                $('#plotInfoAdvanced').show();
                $('#plotInfoAdvanced').popup('open');
            }
        },
        startWorkRedirect: function(){
            $('#plotInfoAdvanced').hide();
            $('#plotInfoAdvanced').popup('close');
            if(this.isLocating) {
                this.geoLocate();
            }
            window.location.href="#map/task_id/" + this.taskData.id + '/plot_id/' + this.selectedPlotId;
        },
        geoLocate: function(){

            if(this.isLocating) {
                 this.isLocating = false;
                 this.clearWatch();
                 this.stopGPSMeasure();
                 return;
            }

            if(this.GPSMeasureID || (this.drawingType && this.drawingType === 'gps')) {
                return;
            }
 
            if(window.watchPositionID) {
                this.clearWatch();
                return;
            }

            this.isLocating = true;
            this.pulseGPSIcon('on');

            var self = this;


            var onSuccess = function (position) {
                self.isLocating = false;
                self.pulseGPSIcon('off');
                self.mapObj.showLocationMarker(position, true, self.autoZoom, self.autoPan);

                try {
                    navigator.notification.confirm(
                        Localization.location_tracking_info,
                        function (button) {
 
                            if (button == "2") {
                                self.geoLocateBtn.css('border-color', '#22A84C');
                                self.watchPosition();
                                self.isLocating = true;
                            }
                        },
                        Localization.location_tracking,
                        [Localization.no, Localization.yes]
                    );
                }
                catch(err) {
                    console.log(err);
                }
            };

             function onError(error) {
                self.isLocating = false;
                self.pulseGPSIcon('off');
                errorMessage = Localization.location_error;
                if(error.message == 'no data in state') {
                    errorMessage = Localization.bluetooth_connection_fail;
                }
                navigator.notification.alert(
                    errorMessage,    // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            }


            this.locationProvider.getCurrentPosition(onSuccess, onError, {enableHighAccuracy: true, timeout:60000});
        },
        watchPosition: function() {
            this.pulseGPSIcon('on');
            var self = this;
            // onSuccess Callback
            //   This method accepts a `Position` object, which contains
            //   the current GPS coordinates
            //
            function onSuccess(position) {
                self.mapObj.showLocationMarker(position, false, self.autoZoom, self.autoPan);

                self.prevPosition = self.currentPosition;
                self.currentPosition = new OpenLayers.LonLat(position.coords.longitude, position.coords.latitude);
            }

            // onError Callback receives a PositionError object
            //
            function onError(error) {
                console.log('code: ' + error.code + '\n' + 'message: ' + error.message + '\n');
            }

            // Options: throw an error if no update is received every 30 seconds.
            //
            window.watchPositionID = self.locationProvider.watchPosition(onSuccess, onError, {timeout: 60000, enableHighAccuracy: true});
        },
        clearWatch: function() {
            this.pulseGPSIcon('off');
            this.geoLocateBtn.css('border-color', '#c5c5c5');
            this.locationProvider.clearWatch(window.watchPositionID);
            window.watchPositionID = undefined;
        },
        stopGPSMeasure: function(keepMarker, keepFeatures) {

            this.GPSDrawingEnabled = false;

            $('#watchPositionInfo').hide();
            $('#watchPositionText').html('');

            if(this.GPSMeasureID) {
                this.pulseGPSIcon('off');
                this.locationProvider.clearWatch(this.GPSMeasureID);
                this.GPSMeasureID = undefined;
            }

            if (!keepMarker) {
                this.mapObj.locationMarker.clearMarkers();
            }
            if (!keepFeatures) {
                this.mapObj.locationAccuracy.removeAllFeatures();
                this.clearGPSMeasure();
            }
        },
        clearGPSMeasure: function() {
            this.mapObj.measureControl.GPS.locationTrack.removeAllFeatures();
            this.mapObj.measureControl.GPS.locationTrack.redraw(true);
        },
        initLocationProvider: function() {
            var self = this;
            $('#location-provider input[type=radio]').on('change', function () {
                var val = 'false';
                var currentVal = localStorage.getItem('usingExternalAntenna');
                if ($("input:radio[name ='location-provider-type']:checked").val() == 'on') {
                    val = 'true';
                    if(currentVal != val) {
                        self.locationProvider = ExternalLocationProvider;
                        bluetoothSerial.isConnected(
                            function(){console.log('already connected');},
                            function(){console.log('not connected');
                            self.locationProvider.connectToDevice();
                        });
                    }
                }else if ($("input:radio[name ='location-provider-type']:checked").val() == 'off'){
                     if(currentVal != val) {
                        self.locationProvider = navigator.geolocation;
                     }
                }
                localStorage.setItem('usingExternalAntenna', val);
            });

            var usingExternal = localStorage.getItem('usingExternalAntenna');
            if(usingExternal == 'false') {
                self.locationProvider = navigator.geolocation;
            }else {
                self.locationProvider = ExternalLocationProvider;
                bluetoothSerial.isConnected(
                    function(){console.log('already connected');},
                    function(){console.log('not connected');
                    self.locationProvider.connectToDevice();
                });
            }
        },
        pulseGPSIcon: function(toggle) {
            var self = this;
            if(toggle === 'on' && self.isIntervalSet === false) {
                this.geoLocateBtn.css('color', '#c5c5c5');
                var isBlue = false;
                self.isIntervalSet = true;
                window.GPSinterval = setInterval(function() {

                    if(isBlue) {
                        self.geoLocateBtn.css('color', '#c5c5c5');
                        isBlue = false;
                    }
                    else {
                        self.geoLocateBtn.css('color', '#22A84C');
                        isBlue = true;
                    }
                }, 500);
            }
            else if(toggle === 'off' && self.isIntervalSet === true) {
                self.isIntervalSet = false;
                clearInterval(window.GPSinterval);
                this.geoLocateBtn.css('color', '#5c5c5c');
            }
        },
        initDisplayMarkerOptions: function() {
            var self = this;

            $('#checkbox-pan').on('change', function () {
                if ($("input:checkbox[name ='checkbox-pan']:checked").val() == 'on') {
                    val = 'true';
                    self.autoPan = true;
                }else {
                    val = 'false';
                    self.autoPan = false;
                }
                localStorage.setItem('autoPan', val);
            });

            $('#checkbox-zoom').on('change', function () {
                if ($("input:checkbox[name ='checkbox-zoom']:checked").val() == 'on') {
                    val = 'true';
                    self.autoZoom = true;
                    $("#checkbox-pan").prop( "checked", true ).checkboxradio( "refresh" );
                    $("#checkbox-pan").checkboxradio( "disable" );
                    localStorage.setItem('autoZoom', val);
                }else {
                    val = 'false';
                    self.autoZoom = false;
                    $("#checkbox-pan").checkboxradio( "enable" );
                }
                localStorage.setItem('autoZoom', val);
            });

            if(localStorage.getItem('autoZoom') == 'false') {
                self.autoZoom = false;
            } else {
                self.autoZoom = true;
            }

            if(localStorage.getItem('autoPan') == 'false') {
                self.autoPan = false;
            } else {
                self.autoPan = true;
            }
        }
    });

    return new TaskPlotsView();
});

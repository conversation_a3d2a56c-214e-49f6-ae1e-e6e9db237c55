define([
    'jquery',
    'backbone',
    'collections/PinsCollection',
    'router'
], function ($, Backbone, PinsCollection) {

    var TasksCollection = Backbone.Collection.extend({
        url: '',
        fileTransfer: undefined,
        initialize: function () {
            try
            {
                this.fileTransfer = new FileTransfer();
            } catch (err)
            {
                console.log(err);
            }

            this.createImages = [];

            this.totalImages = 0;
            this.fail = false;
        },
        sendSampledData: function (options) {
            var self = this;
            var authData = JSON.parse(localStorage.getItem('authData'));
            this.writeLog('SSD:UID:' + authData.user_id);
            $.ajax({
                url: Constants.servers[authData.server].ajax_url + '/api/soil-samples',
                headers: options.headers,
                data: options.data,
                type: options.type,
                dataType: options.dataType,
                success: function (response) {
                    $("#loading-popup").popup('close');
                    $.mobile.loading('hide');
                    self.writeLog('SSD:200');
                    self.extractImagesToBeUploaded(options.data);
                    self.uploadImages(self['createImages'], options);
                    Backbone.trigger('gs:syncTasksSuccess');
                },
                error: function (response) {
                    self.writeLog('SSD:ERR-' + response.status);
                    if (response.status != 401 && response.statusText != 'Unauthorized') {
                        Backbone.trigger('gs:syncTasksFail');
                    }
                }
            });
        },
        getNewSoilOrdesData: function() {
            var self = this;
            var authData = JSON.parse(localStorage.getItem('authData'));

            var orders_on_device = _.map(JSON.parse(localStorage.getItem('tasks_data_' + authData.user_id)) || [], function(data){ if(data) return data.id ; });
            this.writeLog('NOD:UID:' + authData.user_id + '&OOD:' + JSON.stringify(orders_on_device));
            $.ajax({
                url: Constants.servers[authData.server].ajax_url + '/api/new-soil-sample-orders-data',
                headers: {
                    'Authorization': 'Bearer ' + authData.access_token,
                },
                data: {
                    server: authData.server
                },
                type: 'POST',
                success: function (response) {
                    self.writeLog('NOD:200');
                    var newTasksData = response;

                    var oldTasksData = JSON.parse(localStorage.getItem('tasks_data_' + localStorage.getItem('user_id'))) || [];
                    var date = new Date();
                    localStorage.setItem('noty_sync_' + localStorage.getItem('user_id'), date.getTime());

                    if(newTasksData.orders && newTasksData.orders.length == 0) {
                        Backbone.trigger('gs:onGetNewSoilOrdesDataSuccessNoOrders');
                    }else{
                        localStorage.setItem('tasks_data_' + localStorage.getItem('user_id'), JSON.stringify(newTasksData.orders));

                        Backbone.trigger('gs:onGetNewSoilOrdesDataSuccess');
                    }

                    if (newTasksData.sample_pins.length != 0) {

                        oldPins = PinsCollection.getUnsyncedPins();
                        //mahame pinovete, koito sa sinhronizirani na sarvara (isNew = false && isSynced = true ot oldPins)// use the pinscollectionMethod
                        pinsData = _.union(newTasksData.sample_pins, oldPins);

                        PinsCollection.setPinsData(pinsData);
                    }
                },
                error: function (response) {
                    self.writeLog('NOD:ERR-' + response.status);
                    if (response.status != 401 && response.statusText != 'Unauthorized') {
                        Backbone.trigger('gs:syncTasksFail');
                    }
                }
            });
        },
        extractImagesToBeUploaded: function (data) {
            var self = this;
            var images = [];
            var sampled_orders = JSON.parse(data.sampled_orders);

            _.each(sampled_orders, function (item) {
                _.each(item.sample_data, function (sampleCell) {
                    for( i in sampleCell.samples) {
                        images = images.concat(sampleCell.samples[i].images);
                    }
                });
            });

            this.createImages = images.slice();

            this.totalImages += this.createImages.length;
        },
        uploadImages: function (imagesArray, options) {
            var self = this;
            // No files left, stop uploading
            if (imagesArray.length == 0) {
                return;
            }

            var image = imagesArray.pop();
            var authData = JSON.parse(localStorage.getItem('authData'));
            image.url = Constants.servers[authData.server].ajax_url + '/api/soil-samples/uploadImages';

            self.fileTransfer.upload(
                    image.filePath + image.name,
                    encodeURI(image.url),
                    function (entry) {
                        Backbone.trigger('gs:syncPinsProgress', self.totalImages);
                        self.uploadImages(imagesArray, options);
                    },
                    function (error) {
                        Backbone.trigger('gs:syncPinsProgress', self.totalImages);
                        self.uploadImages(imagesArray, options);

                        self.fail = true;
                    },
                    {
                        fileName: image.name,
                        params: options.data
                    }
            );
        },
        writeLog: function(str) {
            if(!logOb) return;
            var log = " [" + (new Date()) + "] " + str + '\n';

            logOb.createWriter(function(fileWriter) {

                fileWriter.seek(fileWriter.length);

                var blob = new Blob([log], {type:'text/plain'});
                fileWriter.write(blob);
            }, function(){});
        },
        removeOldSynchedTasks: function() {
            var authData = JSON.parse(localStorage.getItem('authData'));
            var synchedTasks = JSON.parse(localStorage.getItem('synchedTasks' + authData.user_id)) || [];
            //all synchronisations before the limitDate are removed
            var limitDate = new Date();
            limitDate.setMonth(limitDate.getMonth() - 6);

            synchedTasks = _.filter(synchedTasks, function(obj){
                return new Date(obj.date) > limitDate;
            });
            localStorage.setItem('synchedTasks'+ authData.user_id, JSON.stringify(synchedTasks));

        },
        sendReport: function (options) {
            var self = this;
            var authData = JSON.parse(localStorage.getItem('authData'));

            $.ajax({
                url: Constants.servers[authData.server].ajax_url + '/api/send-email-report',
                headers: options.headers,
                data: options.data,
                type: options.type,
                success: function (response) {
                    Backbone.trigger('gs:sendReportSuccess');
                },
                error: function (response) {
                    if (response.status != 401 && response.statusText != 'Unauthorized') {
                        Backbone.trigger('gs:sendReportFail');
                    }
                }
            });
        },
    });

    return new TasksCollection();
});
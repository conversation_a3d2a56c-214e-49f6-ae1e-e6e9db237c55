define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/widgets/workflowInputWidget.html'
], function ($, _, Backbone, Localization, workflowInputTemplate) {

    var WorkflowInputWidget = Backbone.View.extend({
        events: {
        },
        el: $("#page"),
        initialize: function () {
        },
        render: function (data) {

            data.Localization = Localization;
            if (typeof workflowInputTemplate === 'string') {
                workflowInputTemplate = _.template(workflowInputTemplate);
            }
            $(document.body).find('#popup-workflow-stop-popup').remove();
            this.$el.html(workflowInputTemplate(data));
        }
    });

    return new WorkflowInputWidget();
});

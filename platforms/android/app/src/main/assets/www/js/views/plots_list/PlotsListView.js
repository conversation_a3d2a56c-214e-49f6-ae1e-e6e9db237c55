define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'collections/NotificationsCollection',
    'text!templates/plots_list/plotsListTemplate.html',
    'text!templates/plots_list/plotsListItemTemplate.html',    
], function ($, _, Backbone, Localization, NotificationsCollection, plotsListTemplate, plotsListItemTemplate) {

    var PlotsListView = Backbone.View.extend({
        events: {
        },
        el: $("#page"),
        user_id: localStorage.getItem('user_id'),
        taskData: undefined,
        processedImages: 0,
        currentPercent: 0,
        initialize: function () {
            this.undelegateEvents();
           
            window.resolveLocalFileSystemURL(cordova.file.dataDirectory, function(dir) {
                //NOTICE in dataDirectory the file is private; externalDataDirectory can be used
                dir.getFile("log.txt", {create:true}, function(file) {
                    logOb = file;
                });
            });
        },
        render: function (task_id) {
            if (typeof plotsListTemplate == 'string') {
                plotsListTemplate = _.template(plotsListTemplate);
            }
            var self = this;
            fileStorage.getItem('tasks_data_' + this.user_id, function(value) {
                taskData = _.where(value, {id: parseInt(task_id)}) || [];

                self.taskData = taskData;
                if(taskData.length !== 0){
                    self.plotsData = taskData[0].plots;
                } else{
                    self.plotsData = taskData;
                }

            });

            var data = {
                'Localization': Localization
            };
            this.$el.append(plotsListTemplate(data)).trigger('create').trigger('resize');

            $('h1.menu-title').text(taskData[0].id + ' ' + taskData[0].order_name);

            self.loadPlots();
        },
        loadPlots: function() {

            var authData = JSON.parse(localStorage.getItem('authData'));
            var finishedPlots = JSON.parse(localStorage.getItem('finishedPlots' + this.user_id)) || [];
            var unfinishedTasks = JSON.parse(localStorage.getItem('unfinishedTasks' + this.user_id)) || [];
            var task = this.taskData[0];
            $("#plots-list").html('');


            $("#plots-list").append('<li><a href="#taskPlots/task_id/'+task.id + '" ><i class="fa fa-map fa-2x"></i></a></li>');
            
            var tmpRowTemplate = _.template(plotsListItemTemplate);

            task.plots.forEach(function (row, index) {
                var formattedDataRow = row;
                
                var treatment_type_label = row.sample_type_json.map(function (type) {
                    return type.short_name
                }).join(', ');
                
                formattedDataRow.task_id = task.id
                formattedDataRow.treatment_type_label = treatment_type_label;
                formattedDataRow.area_unit = Constants.servers[authData.server].area_unit;
                formattedDataRow.cells_for_sampling = row.sample_grid.features.filter((feature) => {
                    return feature.properties.for_sampling;
                }).map(feature => {
                    return feature.properties.sample_id;
                }).sort().join(', ');
                if (unfinishedTasks.indexOf(formattedDataRow.task_id.toString()) != -1) {
                    if (finishedPlots.indexOf(formattedDataRow.plot_gid) != -1) {
                        formattedDataRow.styleClass = "ui-group-theme-b";
                    } else{
                        formattedDataRow.styleClass = "ui-group-theme-c";
                    }
                } else {
                    formattedDataRow.styleClass = "";
                }

                var htmlForRow = tmpRowTemplate({row: formattedDataRow, Localization: Localization});
                $("#plots-list").append(htmlForRow);
            });

            $("#plots-list").listview("refresh").trigger('create').trigger('resize');
        },
    });

    return new PlotsListView();
});
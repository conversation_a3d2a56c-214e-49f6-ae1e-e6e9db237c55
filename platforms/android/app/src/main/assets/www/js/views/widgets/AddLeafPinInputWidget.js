define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/widgets/addLeafPinInputWidget.html'
], function ($, _, Backbone, Localization, addLeafPinInputTemplate) {

    var AddLeafPinInputWidget = Backbone.View.extend({
        events: {
        },
        el: $("#page"),
        initialize: function () {
            var self = this;
            Backbone.on('gs:openNewLeafPinPopup', function() {
                this.openNewLeafPinPopup();
            }, self);

            Backbone.on('gs:closeNewLeafPinPopup', function() {
                this.closeNewLeafPinPopup();
            }, self);
        },
        render: function (data) {

            data.Localization = Localization;
            if (typeof addLeafPinInputTemplate === 'string') {
                addLeafPinInputTemplate = _.template(addLeafPinInputTemplate);
            }

            this.$el.html(addLeafPinInputTemplate(data));

            $('#popup-add-leaf-pin-ok').off('click').on('click', $.proxy(this.saveAddLeafPin, this));
            $('#popup-add-leaf-pin-cancel').off('click').on('click', $.proxy(this.closeNewLeafPinPopup, this));
        },
        openNewLeafPinPopup: function() {

            $('#leaf-pin-title').val('');
            $('#leaf-pin-comment').val('');

            $("#popup-add-leaf-pin").popup("open");
        },
        closeNewLeafPinPopup:function() {
            $("#popup-add-leaf-pin").popup('close');
        },
        saveAddLeafPin: function() {

            var leafPinTitle = $('#leaf-pin-title').val().trim();
            var leafPinComment = $('#leaf-pin-comment').val().trim();

            if(leafPinTitle == undefined || leafPinTitle == '' ) {
                navigator.notification.alert(
                    Localization.missing_pin_title,
                    function() {},                         // callback
                    Localization.error,                    // title
                    Localization.ok                        // buttonName
                );
            } else {
                var leafPinParams = {
                    'title': leafPinTitle,
                    'comment': leafPinComment
                };
                Backbone.trigger('gs:leafPinAdd', leafPinParams);
            }
        }
    });

    return new AddLeafPinInputWidget();
});

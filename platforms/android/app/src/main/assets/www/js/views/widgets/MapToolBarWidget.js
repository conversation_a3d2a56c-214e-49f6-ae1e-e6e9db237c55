define([
    'jquery',
    'underscore',
    'backbone',
    'text!templates/widgets/mapToolbarWidget.html'
], function ($, _, Backbone, mapToolbarWidget) {

    var MapToolBarWidget = Backbone.View.extend({
        isMapPage:false,
        events: {
            'click #measureTools': 'showMeasureTools',
            'click #pinTools': 'showPinTools',
            'click #pause': 'pauseTrack',
            'click #geoLocate': 'geoLocate',
            'click #zoomToExtent': 'zoomToPlotsExtent',
            'click #newLeafPin': 'openNewLeafPinPopup'
        },
        el: $("#page"),
        initialize: function () {
			
        },
        render: function (data) {
            this.isMapPage = data.isMapPage;

            if (typeof mapToolbarWidget === 'string') {
                mapToolbarWidget = _.template(mapToolbarWidget);
            }

            this.$el.append(mapToolbarWidget(data)).trigger('create');


            $('#measureToolsPopup').on( "popupbeforeposition", function(e, data) {
                delete data.x;
                delete data.y;
                data.positionTo = '#measureTools';
            });
        },
        showMeasureTools: function() {
            $('#measureToolsPopup').popup('open', {
                transition: 'flip',
                positionTo: $('#measureTools')
            });
        },
        geoLocate: function() {
            if(this.isMapPage === true) {
                Backbone.trigger('gs:geoLocate');
            } else {
                Backbone.trigger('gs:taskPlotaGeoLocate');
            }
        },
        zoomToPlotsExtent: function() {

            if(this.isMapPage === true) {
                Backbone.trigger('gs:zoomToPlotsExtent');
            } else {
                Backbone.trigger('gs:zoomToTaskPlotsExtent');
            }
        },
        openNewLeafPinPopup: function() {
            Backbone.trigger('gs:openNewLeafPinPopup');
        }
    });

    return new MapToolBarWidget();
});

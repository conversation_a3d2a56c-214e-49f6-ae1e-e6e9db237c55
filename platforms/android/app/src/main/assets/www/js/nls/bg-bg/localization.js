define({
    map: "Карта",
    plots: "Парцели",
    new_images: "Нови снимки",
    contacts: "Кон<PERSON>акт<PERSON>",
    exit: "Изход",
    login: "Вход",
    maptype: "Вид карта",
    error: "Грешка",
    enter_user: "Въведете потребител",
    enter_password: "Въведете парола",
    contacts_phone: "Телефон за контакт",
    contacts_connect: "СВЪРЖЕТЕ СЕ С НАС",
    choose_server: "Изберете сървър",
    server_login: "Login",
    server_login2: "Login2",
    server_login3: "Login3",
    server_techno: "Techno",
    auth_error: "Грешно потребителско име или парола.",
    network_error: "Моля проверете дали имате връзка с интернет.",
    sync: "Синхронизи<PERSON><PERSON><PERSON>",
    update: "Обнов<PERSON>",
    search: "Търси",
    sync_plots_error: "Синхронизацията е неуспешна!",
    confirm_exit: "Сигурни ли сте, че искате да излете от приложението?",
    notifications: "Известия",
    plots_sync_advice: "В момента няма синхронизирани парцели. За да го направите изберете парцели от менюто в горния ляв ъгъл.",
    no_name: "Без име",
    choose_date: "Изберете дата",
    by_date: "по дата",
    yes: "Да",
    no: "Не",
    location_tracking: "Следене на местоположението",
    location_tracking_info: "Режим следене на местоположението ще следи автоматично Вашата позиция и ще я изобразява на картата. Желаете ли да го включите?",
    measure_type: "Начин на измерване",
    measure_type_info: "При ръчно измерване можете да очертаете желаната от Вас фигура, а при GPS измерване очертаването ще се извършва автоматично от промяната на местоположението Ви.",
    manual: "Ръчно",
    gps: "GPS",
    pin: "Маркер",
    pins: "Маркери",
    add_pin: "Добавяне на маркер",
    add_pin_info: "При ръчно добаване можете да поставите маркер, докосвайки по картата, а при GPS добавяне маркера ще бъде поставен на текущото Ви местоположение.",
    delete: "Изтриване",
    delete_confirm: "Сигурни ли сте, че искате да продължите с изтриването?",
    language: "Език",
    'bg-bg': "Български",
    'ro-ro': "Румънски",
    'en-us': "Английски",
    'it-it': "Италиански",
    'ua-ua': "Украински",
    'bg-bg-short': "BG",
    'ro-ro-short': "RO",
    'en-us-short': "EN",
    'it-it-short': "IT",
    'ua-ua-short': "UA",
    cancel: "Отмени",
    save: "Запази",
    camera: "Камера",
    gallery: "Галерия",
    images: "Снимки",
    data: "Данни",
    name: "Име",
    information: "Информация",
    adding: "Добавяне",
    editing: "Редактиране",
    back: "Назад",
    edit: "Редактиране",
    last_sync: "Последна синхронизация на",
    date: "Дата",
    on: "Вкл",
    off: "Изкл",
    username: "Потребител",
    password: "Парола",
    location_error: "Позиционирането неуспешно. Моля уверете се, че сте активирали GPS на устройството.",
    ordered_plots_layer: "Слой поръчани парцели",
    relative: "относително",
    absolute: "абсолютно",
    satellite_imaging: "Сателитни изображения",
    soil_samples: "Почвени карти",
    yr: "г.",
    dka: "дка",
    ha: "ха",
    backup: "Резервно копие",
    start: "Старт",
    stop: "Стоп",
    tasks: "Задачи",
    new_tasks: "Нови задачи",
    unfinished_tasks: "Недовършени задачи",
    finished_tasks: "Приключени задачи",
    no_tasks: "Няма задачи в списъка",
    sync_tasks_error: "Синхронизацията е неуспешна!",
    download_tasks_data: "Свали данни за нови задачи",
    continue: "Продължи",
    end: "Край",
    finished_sampling: "Блокът е готов. Всички клетки са обработени",
    unfinished_sampling: "Блокът не е готов. Има клетки за дообработване",
    sample: "Проба",
    end_sampling: "Приклюване на пробовземането",
    scan_barcode:"Сканирай баркод",
    set_number: "Номериране",
    ok: "ОК",
    comment: "Коментар",
    sample_type: "Тип пробовземане",
    sample_depth: "Дълбочина",
    ekatte: "ЕКАТТЕ",
    sample_start_positioning_error: "Моля позиционирайте се в клетката в която искате да взимате проба.",
    sample_stop_positioning_error: "Пробовземането трябва да започва и да свършва в една и съща клетка.",
    missing_sample_number: "Липсва номер на проба.",
    duplicate_sample_number: "Номера на пробата вече съществува.",
    required_sample_number_length: "Номера на пробата трябва да съдържа 7 цифри",
    sample_number: "Номер на проба",
    upload_images: "Качване на снимки:",
    no_completely_sampled_plots: "Липсват изцяло обработени блокове.",
    no_new_tasks: "Нямате нови възложени задачи.",
    new_tasks_data_successfully_retrieved: "Информация за новите Ви задачи бе успешно свалена на устройството.",
    client_name: "Име на клиент",
    plot_name: "Име на парцел",
    useExternalAntenna: "Използвай външна антена",
    bluetooth_no_paired_devices: "Няма свързани устройства чрез bluetooth!",
    bluetooth_connection_to_paired: "Вземане на данни от външното устройство с име: ",
    bluetooth_connection_success: "Успешно вземане на данни от външното устройство.",
    bluetooth_connection_fail: "Неуспешно вземане на данни от външното устройство.",
    road_to_plot:"Път до там",
    no_map_pad: "Без подложка",
    zoom: "Мащабиране",
    pan: "Местене на картата",
    show_position: "Показване на позиция:",
    treatment_types : [{
            value: 0,
            name: 'Проби 0-30 cm.',
            short_name: '0-30',
        }, {
            value: 1,
            name: 'Проби 30-60 cm.',
            short_name: '30-60',
        }, {
            value: 2,
            name: 'Листни проби',
            short_name: 'Листни',
        }],

    cell_count: 'Брой клетки',
    start_work: 'Започни работа',
    pause: 'Пауза',
    fileSaved: 'Запазен файл ',
    missing_pin_title: "Липсва име на маркера.",
    sync_pins_error: "Неуспешна синхронизация на маркери!",
    synchronizing_samples: "Синхронизиране на проби.",
    synchronizing_pins: "Синхронизиране на маркери.",
    sendReport: "Изпращане на отчет за задача",
    successfullySentReport: "Отчетът е изпратен!",
    sentReportFail: "Неуспешно изпращане на отчет!",
    sendReportConfirm: "Сигурни ли сте, че искате да изпратите репорт за задача ",
    cells: 'Клетки',
    forDemoSampling: 'ДЕМО',
    forDemoSamplingShort: 'За демонстрация',
    type: 'Тип',
    ekatte: 'EKATTE',
    plotArea: 'Площ',
    dateAssigned: 'Възлагане',
});
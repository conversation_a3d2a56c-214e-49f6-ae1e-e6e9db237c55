define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/menu/menuTemplate.html',
    'jquerymobiledatepicker'
], function ($, _, Backbone, Localization, menuTemplate) {

    var MenuView = Backbone.View.extend({
        el: $("#page"),
        user_id: localStorage.getItem('user_id'),
        initialize: function () {

        },
        events: {
            'click #exportData': 'exportData'
        },
        render: function () {
            var self = this;
            //Set language in menu
            var map_type = {};
            map_type.code = (localStorage.getItem('map_type')) ? localStorage.getItem('map_type') : 'geoscan';
            var usingExternalAntenna = (localStorage.getItem('usingExternalAntenna')) ? localStorage.getItem('usingExternalAntenna') : 'false';
            var locale = (localStorage.getItem('locale')) ? localStorage.getItem('locale') : Constants.default_launguage;
            var authData = JSON.parse(localStorage.getItem('authData'));

            var autoZoom = (localStorage.getItem('autoZoom')) ? localStorage.getItem('autoZoom') : 'true';
            var autoPan = (localStorage.getItem('autoPan')) ? localStorage.getItem('autoPan') : 'true';

            var data = {
                Localization: Localization,
                map_type: map_type,
                exit_text: Localization.exit + ' (' + authData.username + ')',
                showPins: localStorage.getItem('showPins_' + this.user_id),
                locale: locale,
                languages: Constants.languages,
                page_name: localStorage.getItem('page_name')
            };

            if (typeof menuTemplate == 'string') {
                menuTemplate = _.template(menuTemplate);
            }

            this.$el.html(menuTemplate(data)).trigger('create');

            if(usingExternalAntenna == 'false') {
                $('#internal').prop( "checked", true ).checkboxradio( "refresh" );
                localStorage.setItem('usingExternalAntenna', 'false');
            } else
            {
                $('#external').prop( "checked", true ).checkboxradio( "refresh" );
                localStorage.setItem('usingExternalAntenna', 'true');
            }

            if(autoZoom == 'false') {
                $('#checkbox-zoom').prop( "checked", false ).checkboxradio( "refresh" );
                $("#checkbox-pan").checkboxradio("enable");
                localStorage.setItem('autoZoom', 'false');
            } else
            {
                $('#checkbox-zoom').prop( "checked", true ).checkboxradio( "refresh" );
                autoPan = true;
                $("#checkbox-pan").checkboxradio("disable");
                localStorage.setItem('autoZoom', 'true');
            }

            if(autoPan == 'false') {
                $('#checkbox-pan').prop( "checked", false ).checkboxradio( "refresh" );
                localStorage.setItem('autoPan', 'false');
            } else
            {
                $('#checkbox-pan').prop( "checked", true ).checkboxradio( "refresh" );
                localStorage.setItem('autoPan', 'true');
            }

            $('#change-map-type input[type=radio]').on('click', function () {
                localStorage.setItem('map_type', $(this).val());
                Backbone.trigger('gs:mapChange');
                $('#nav-settings').panel('close');

                ga('send', 'event', 'mapType', $(this).val());
            });

            $('#change-locale input[type=radio]').on('click', function () {
                $.mobile.loading('show');

                localStorage.setItem('locale', $(this).val());
                location.assign('');

                ga('send', 'event', 'locale', $(this).val());
            });

            $('#pins-flip').on('change', function(event) {
                localStorage.setItem('showPins_' + self.user_id, event.currentTarget.checked);
                Backbone.trigger('gs:togglePins', event.currentTarget.checked);
            });

            this.$el.trigger('create').trigger('resize');
        },
        exportData: function() {
            $("#loading-popup").popup('open');
            $.mobile.loading('show');

            var userId = localStorage.getItem('user_id');

            var authData = JSON.parse(localStorage.getItem('authData'));

            fileStorage.getItem('sampledTasks' + userId, function(sampledData) {

                var m = moment().format("DD-MM-YYYY-HH-mm-ss");
                var fileName = authData.username + '_sampledData' + m;

                window.resolveLocalFileSystemURL(cordova.file.externalApplicationStorageDirectory + '/files', function (fs) {

                    fs.getFile(fileName + ".txt", { create: true, exclusive: false }, function (fileEntry) {
                        fileEntry.name == fileName + '.txt';
                        dataObj = new Blob([JSON.stringify(sampledData)], { type: 'text/plain' });
                        writeFile(fileEntry, dataObj);

                    }, function(error){console.log(error);});

                }, function(error){console.log(error);});


                function writeFile(fileEntry, dataObj) {
                    // Create a FileWriter object for our FileEntry (log.txt).
                    fileEntry.createWriter(function (fileWriter) {

                        fileWriter.onwriteend = function() {
                            $("#loading-popup").popup('close');
                            $.mobile.loading('hide');
                            navigator.notification.alert(
                                'Android/data/com.agrobalance/files/' +  fileName,                   // message
                                function() {},                  // callback
                                Localization.fileSaved,             // title
                                Localization.ok                 // buttonName
                            );
                        };

                        fileWriter.onerror = function (e) {

                            $("#loading-popup").popup('close');
                            $.mobile.loading('hide');

                            navigator.notification.alert(
                                Localization.error,                   // message
                                function() {},                  // callback
                                Localization.error,             // title
                                Localization.ok                 // buttonName
                            );
                        };

                        // If data object is not passed in,
                        // create a new Blob instead.
                        if (!dataObj) {
                            dataObj = new Blob(['No data to write!'], { type: 'text/plain' });
                        }

                        fileWriter.write(dataObj);
                    });
                }




            });
            
            
        }
    });

    return new MenuView();
});

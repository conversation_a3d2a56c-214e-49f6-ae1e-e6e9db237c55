define({
    map: "<PERSON><PERSON> ",
    plots: "Sole",
    new_images: "Imagini noi",
    contacts: "<PERSON><PERSON>",
    exit: "<PERSON><PERSON><PERSON>",
    login: "<PERSON><PERSON><PERSON>",
    maptype: "Tip de hartă",
    error: "<PERSON><PERSON><PERSON>",
    enter_user: "Nume Utilizator",
    enter_password: "<PERSON><PERSON><PERSON>",
    contacts_phone: "Telefon de contact",
    contacts_connect: "CONTACTEAZĂ-NE",
    choose_server: "Alege serverul",
    server_login: "Logare",
    server_login2: "Logare2",
    server_login3: "Logare3",
    server_techno: "Techno",
    auth_error: "Nume utilizator sau parola greşită .",
    network_error: "Verifică conexiunea la reţea.",
    sync: "Sincronizare",
    update: "Actualizare",
    search: "Căutare",
    sync_plots_error: "Sincronizare nereusită!",
    confirm_exit: "Sunteţi sigur că doriţi să ieşiţi?",
    notifications: "Notific<PERSON>ri",
    plots_sync_advice: "Solele nu sunt sincronizate.Pentru sincronizarea solelor alegeţi meniul din colţul stânga sus.",
    no_name: "Fară nume",
    choose_date: "Alege dată",
    by_date: "După dată",
    yes: "Da",
    no: "Nu",
    location_tracking: "Urmărirea locaţiei",
    location_tracking_info: "Urmarirea locaţiei va urmării şi-ţi va arăta poziţia pe hartă. Doriţi să o activaţi?",
    measure_type: "Tipul de măsurare",
    measure_type_info: "Măsurarea manuală vă va lăsa să desenaţi caracteristici dar măsurarea cu GPS-ul va desena caracteristici  cu schimbarea poziţiei.",
    manual: "Manual",
    gps: "GPS",
    pin: "Marcaj",
    pins: "Marcaje",
    add_pin: "Adăugare marcaj",
    add_pin_info: "Adăugarea manuală a marcajelor  se face prin  apăsarea  pe hartă, dar adăugarea prin GPS vă arată şi poziţia.",
    delete: "Ştergere",
    delete_confirm: "Sunteţi sigur că vreşi să continuaţi?",
    language: "Limbă",
    'bg-bg': "Bulgară",
    'ro-ro': "Română",
    'en-us': "Engleză",
    'it-it': "Italiano",
    'ua-ua': "Ucrainean",
    'bg-bg-short': "BG",
    'ro-ro-short': "RO",
    'en-us-short': "EN",
    'it-it-short': "IT",
    'ua-ua-short': "UA",
    cancel: "Anulare",
    save: "Salvează",
    camera: "Cameră",
    gallery: "Galerie",
    images: "Imagini",
    data: "Data",
    name: "Nume",
    information: "Informaţii",
    adding: "Adăugare",
    editing: "Editare",
    back: "Înapoi",
    edit: "Editaţi",
    last_sync: "Ultima sincronizare",
    date: "Dată",
    on: "Pornit",
    off: "Oprit",
    username: "Utilizator",
    password: "Parolă",
    location_error: "Nu am putut obţine locaţia dumneavoastră. Vă rugăm asiguraţi-vă că GPS-ul este activat.",
    ordered_plots_layer: "Layer Ordered Plots",
    relative: "relativ",
    absolute: "absolut",
    satellite_imaging: "Imagini satelitare",
    soil_samples: "Probe de sol",
    yr: "yr.",
    dka: "dka",
    ha: "ha",
    backup: "Copie de siguranta",
    start: "Start",
    stop: "Stop",
    tasks: "Sarcină",
    new_tasks: "Sarcină nouă",
    unfinished_tasks: "Sarcină neterminată",
    finished_tasks: "Task-uri terminate",
    no_tasks: "Nici o sarcină în listă",
    sync_tasks_error: "Sincronizare nereuşită!",
    download_tasks_data: "Descarcă sarcini noi",
    continue: "Continua",
    end: "Finalizare",
    finished_sampling: "Terenul este gata. Toate celulele au fost prelucrate.",
    unfinished_sampling: "Terenul nu este gata. Există încă celule neprelucrate.",
    sample: "Probă",
    end_sampling: "Finalizare recoltare",
    scan_barcode:"Scanează cod de bare",
    set_number: "Numerotare",
    ok: "ОК",
    comment: "Comentarii",
    sample_type: "Tipul probei",
    sample_depth: "Adâncime",
    ekatte: "Numar de identificare",
    sample_start_positioning_error: "Mergi în celula de unde vrei să recoltezi probe de sol.",
    sample_stop_positioning_error: "Recoltatul probelor trebuie să înceapă şi să se termine în aceeaşi celulă.",
    missing_sample_number: "Lipseşte un număr de probă.",
    duplicate_sample_number: "Numărul probei există deja.",
    required_sample_number_length: "Numărul eșantionului trebuie să fie de 7 cifre",
    sample_number: "Număr de probă",
    upload_images: "Încarcă imagini:",
    no_completely_sampled_plots: "Nu sunt sole procesate complet.",
    no_new_tasks: "Nu aveţi sarcini noi.",
    new_tasks_data_successfully_retrieved: "Informaţiile despre noile sarcini au fost descarcate cu succes în dispozitiv.",
    client_name: "Nume client",
    plot_name: "Nume solă",
    useExternalAntenna: "Foloseşte antena externă",
    bluetooth_no_paired_devices: "Nici un receptor conectat prin bluetooth!",
    bluetooth_connection_to_paired: "Preluare de date din sursa externã numită: ",
    bluetooth_connection_success: "Preluarea datelor din sursa externã a fost cu succes.",
    bluetooth_connection_fail: "Preluarea datelor din sursa externã a fost fara succes.",
    road_to_plot:"Navigare",
    no_map_pad: "Fară imagine de fundal",
    zoom: "Apropiere la pozitie",
    pan: "Panoramare",
    show_position: "Arată poziţia:",
    treatment_types : [{
            value: 0,
            name: 'Probe 0-30 cm.',
            short_name: '0-30',
        }, {
            value: 1,
            name: 'Probe 30-60 cm.',
            short_name: '30-60',
        }, {
            value: 2,
            name: 'Probe de ţesut vegetal',
            short_name: 'ţesut vegetal',
        }],

    cell_count: 'Numărul de celule',
    start_work: 'Incepe munca',
    pause: 'Pauză',
    fileSaved: 'Salvați fișierul ',
    missing_pin_title: 'Lipsește titlul marcaj',
    sync_pins_error: "Sincronizare marcaje eșuată!",
    synchronizing_samples: "Sincronizarea probelor.",
    synchronizing_pins: "Sincronizarea marcaje.",
    sendReport: "Trimite raport pentru Task",
    successfullySentReport: "Raport trimis cu succes!",
    sentReportFail: "Trimitere raport eșuată!",
    sendReportConfirm: "Ești sigur ca vrei să trimiți raport pentru task ",
    cells: "Celule",
    forDemoSampling: 'Pentru eșantionarea demo',
    forDemoSamplingShort: 'DEMO',
    type: 'Tip',
    ekatte: 'EKATTE',
    plotArea: 'Zona',
    dateAssigned: 'Atribuit',
});
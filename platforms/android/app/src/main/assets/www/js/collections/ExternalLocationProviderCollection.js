define([
    'jquery',
    'backbone',
    'i18n!nls/localization',
    'router'
], function ($, Backbone, Localization) {

    var ExternalLocationProviderCollection = Backbone.Collection.extend({
        url: '',
        isConnected: false,
        NMEAParser: undefined,
        isWatchingPosition: [],
        watchPositionSuccess: [],
        watchPositionError: [],
        initialize: function () {
            this.NMEAParser = new GPS;
        },
        connectToDevice: function () {
            var self = this;
            bluetoothSerial.list(function(devices) {
                if(devices.length == 0) {
                    navigator.notification.alert(
                        Localization.bluetooth_no_paired_devices,
                        function() {},
                        Localization.error,
                        Localization.ok
                    );
                }
                devices.forEach(function(device) {
                    bluetoothSerial.isConnected(
                        function() {self.isConnected = true;},
                        function() {
                            self.isConnected = false;
                            navigator.notification.alert(
                            Localization.bluetooth_connection_to_paired + device.name,
                            function() {},
                            Localization.information,
                            Localization.ok
                        );

                        function connectSuccess(result){
                            navigator.notification.alert(
                                Localization.bluetooth_connection_success,
                                function() {},
                                Localization.information,
                                Localization.ok
                            );
                            self.isConnected = true;

                            self.subscribeToBluetoothData();
                        }

                        function connectFailure(error) {
                            navigator.notification.alert(
                                Localization.bluetooth_connection_fail,
                                function() {},
                                Localization.error,
                                Localization.ok
                            );
                            console.log(error);
                        }
                        bluetoothSerial.connect(device.address, connectSuccess, connectFailure);
                    })
                })
            }, function(error) {

                console.log('error connecting: ' + error);
                navigator.notification.alert(
                    Localization.bluetooth_connection_fail,
                    function() {},
                    Localization.error,
                    Localization.ok
                );

            });
        },
        update: function(data) {
            return this.NMEAParser.update(data);
        },
        updatePartial: function(data) {
            return this.NMEAParser.updatePartial(data);
        },
        getState: function() {
            return this.NMEAParser.state;
        },
        getCurrentPosition: function(successCallback, errorCallback, options) {
            if(this.NMEAParser.state.lon != null && this.NMEAParser.state.lat != null) {
                var position = {
                    'coords': {
                        'latitude': this.NMEAParser.state.lat,
                        'longitude': this.NMEAParser.state.lon,
                        'accuracy': this.NMEAParser.state.hdop * 2,
                        'altitude': this.NMEAParser.state.alt,
                        'altitudeAccuracy': null,
                        'heading': null,
                        'speed': null
                    },
                    'timestamp': new Date().getTime()
                };

                successCallback(position);
            } else {
                var error = {
                    'message': 'no data in state'
                };
                errorCallback(error);
            }
        },
        watchPosition: function(successCallback, errorCallback, options) {
            var self = this;
            var watchPositionID = new Date().getTime();


//add WP ids in array
            this.isWatchingPosition[watchPositionID] = true;
            this.watchPositionSuccess[watchPositionID] = successCallback;
            this.watchPositionError[watchPositionID] = errorCallback;

            return watchPositionID;
        },
        clearWatch: function(watchPositionID) {
            var self = this;

            this.isWatchingPosition.splice(watchPositionID, 1);
            this.watchPositionSuccess.splice(watchPositionID, 1);
            this.watchPositionError.splice(watchPositionID, 1);

        },
        subscribeToBluetoothData: function() {
            //препоръчително е да има само една функция събскрайбната към външното устройство
            var self = this;

            bluetoothSerial.subscribe('\n', function (data) {
                if(data != '') {
                    self.NMEAParser.update(data);
                }

                for (i in self.isWatchingPosition) {
                    if(self.NMEAParser.state.lon != null && self.NMEAParser.state.lat != null) {
                        var position = {
                            'coords': {
                                'latitude': self.NMEAParser.state.lat,
                                'longitude': self.NMEAParser.state.lon,
                                'accuracy': self.NMEAParser.state.hdop * 2,
                                'altitude': self.NMEAParser.state.alt,
                                'altitudeAccuracy': null,
                                'heading': null,
                                'speed': null
                            },
                            'timestamp': new Date().getTime()
                        };

                        self.watchPositionSuccess[i](position);
                    } else {
                        var error = {
                            'message': 'no data in state'
                        };
                        self.watchPositionError[i](error);
                    }
                }
            }, function(error){console.log(error);});
        }
    });

    return new ExternalLocationProviderCollection();
});
define([
    'i18n!nls/localization'
], function (Localization) {
    var measureControl = function(map) {
        var that = this;

        // style the sketch fancy
        var sketchSymbolizers = {
            "Point": {
                pointRadius: 6,
                graphicName: "circle",
                fillColor: "#0083c9",
                fillOpacity: 1,
                strokeWidth: 2,
                strokeOpacity: 1,
                strokeColor: "#ffffff"
            },
            "Line": {
                strokeWidth: 3,
                strokeOpacity: 1,
                strokeColor: "#0083c9",
                strokeDashstyle: "solid"
            },
            "Polygon": {
                strokeWidth: 3,
                strokeOpacity: 1,
                strokeColor: "#0083c9",
                fillColor: "#ffffff",
                fillOpacity: 0.3
            }
        };

        this.LineString = new OpenLayers.Control.DynamicMeasure(
                OpenLayers.Handler.Path, {
                    geodesic: true,
                    persist: true,
                    layerSegmentsOptions: null,
                    maxSegments: null,
                    styles: sketchSymbolizers
                }
        );
        this.Polygon = new OpenLayers.Control.DynamicMeasure(
                OpenLayers.Handler.Polygon, {
                    geodesic: true,
                    persist: true,
                    layerLengthOptions: null,
                    layerSegmentsOptions: null,
                    maxSegments: null,
                    styles: sketchSymbolizers
                }
        );

        map.addControl(this.LineString);
        map.addControl(this.Polygon);

        this.GPS = {};

        this.GPS.locationTrack = new OpenLayers.Layer.Vector('locationTrack');

        map.addLayer(this.GPS.locationTrack);

        this.GPS.startGPSMeasure = function() {
            that.GPS.locationTrack.removeAllFeatures();
            that.GPS.track = new OpenLayers.Geometry.LineString();
            that.GPS.trackPolygon = new OpenLayers.Geometry.LinearRing();

            var trackFeature = new OpenLayers.Feature.Vector(
                that.GPS.track,
                {},
                {
                    strokeWidth: 5,
                    strokeColor: '#f70202'
                }
            );

            var trackPolygonFeature = new OpenLayers.Feature.Vector(
                that.GPS.trackPolygon,
                {},
                {
                    fillColor: '#fff',
                    fillOpacity: 0.25,
                    stroke: false
                }
            );

            that.GPS.locationTrack.addFeatures([trackFeature, trackPolygonFeature]);
        };

        /**
        * format length output
        * @param {float} length
        * @return {string}
        */
        this.formatLength = function(length) {
            var output;
            if (length > 1000) {
                output = (Math.round(length / 1000 * 100) / 100) + ' km';
            } else {
                output = (Math.round(length * 100) / 100) + ' m';
            }
            return output;
        };

        /**
         * format length output
         * @param {float} area
         * @return {string}
         */
        this.formatArea = function(area) {
            var output;
            output = (Math.abs(area) / 1000 * Constants['dka_' + Constants.servers[authData.server].area_unit]).toFixed(3) + ' ' + Localization[Constants.servers[authData.server].area_unit];
            return output;
        };
    };

    return measureControl;
});

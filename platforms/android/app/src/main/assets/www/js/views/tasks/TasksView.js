define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'collections/NotificationsCollection',
    'collections/TasksCollection',
    'collections/PinsCollection',
    'text!templates/tasks/tasksTemplate.html',
], function ($, _, Backbone, Localization, NotificationsCollection, TasksCollection, PinsCollection, tasksTemplate) {

    var tasksView = Backbone.View.extend({
        events: {
            'click #sync-samples': 'onSyncTask'
        },
        el: $("#page"),
        user_id: localStorage.getItem('user_id'),
        processedImages: 0,
        currentPercent: 0,
        initialize: function () {
            this.undelegateEvents();

            Backbone.on('gs:syncTasksSuccess', this.onSyncTasksSuccess, this);
            Backbone.on('gs:syncTasksFail', this.onSyncTasksFail, this);
            Backbone.on('gs:syncPinsFail', this.onSyncPinsFail, this);
            Backbone.on('gs:getNewTasksDataSuccess', this.onGetNewTasksDataSuccess, this);
            Backbone.on('gs:onGetNewSoilOrdesDataSuccess', this.onGetNewSoilOrdesDataSuccess, this);
            Backbone.on('gs:onGetNewSoilOrdesDataSuccessNoOrders', this.onGetNewSoilOrdesDataSuccessNoOrders, this);
            Backbone.on('gs:syncPinsProgress', function (total) {
                this.processedImages++;
                var percent = Math.round(this.processedImages / total * 100);
                percent = Math.max(this.currentPercent, percent);
                percent = Math.min(percent, 100);
                
                this.currentPercent = percent;
                $.mobile.loading("show", {
                    text: percent + '%',
                    textVisible: true
                });
                if (percent == 100) {
                   $.mobile.loading("hide");
                }
            }, this);

            window.resolveLocalFileSystemURL(cordova.file.dataDirectory, function(dir) {
                //NOTICE in dataDirectory the file is private; externalDataDirectory can be used
                dir.getFile("log.txt", {create:true}, function(file) {
                    logOb = file;
                });
            });
        },
        render: function () {
            if (typeof tasksTemplate == 'string') {
                tasksTemplate = _.template(tasksTemplate);
            }
            var data = {
                'Localization': Localization
            };
            this.$el.append(tasksTemplate(data)).trigger('create').trigger('resize');

            $('h1.menu-title').text(Localization.tasks);

            $("#tasks-footer").append('<button id="sync-tasks" data-action="start" class="ui-btn ui-corner-all ui-btn-b ui-btn-icon-left ui-icon-arrow-u">' + Localization.sync + '</button>');
            $('#sync-tasks').off('click').on('click', $.proxy(this.syncTasks, this));

            $(window).on('resize', function() {
                $('#tasks-list').css({
                    width: 0,
                    height: 0
                });
            });

            this.loadTaskCategories();
        },
        syncTasks: function() {
            $("#loading-popup").popup('open');
            $.mobile.loading("show", {
                text: Localization.synchronizing_samples,
                textVisible: true,
            });

            var authData = JSON.parse(localStorage.getItem('authData'));

            var allTasksData = JSON.parse(localStorage.getItem('tasks_data_' + this.user_id)) || [];
            var samplesData = JSON.parse(localStorage.getItem('sampledTasks' + authData.user_id));
            var unfinishedTasksData = JSON.parse(localStorage.getItem('unfinishedTasks' + authData.user_id)) || [];
            var finishedPlots = JSON.parse(localStorage.getItem('finishedPlots' + authData.user_id)) || [];
            var filteredData = [];
            var samplesData2d = {};
            var geoJSON = new OpenLayers.Format.GeoJSON();

            var unsyncedPins = PinsCollection.getUnsyncedPins();

            for(i = 0; i < unfinishedTasksData.length; i++) {

                if(samplesData[unfinishedTasksData[i]] == undefined) {
                    continue;
                }
                plot_ids = Object.getOwnPropertyNames(samplesData[unfinishedTasksData[i]]);
                task = _.find(allTasksData, function(data){ return data.id == unfinishedTasksData[i]; });
                for(j=0; j< plot_ids.length; j++) {
                    if(finishedPlots.indexOf(+plot_ids[j]) != -1) {
                        samplesData2d = _.map(samplesData[unfinishedTasksData[i]][plot_ids[j]], function(obj, key){ obj.track=obj.track.d3;return obj; });
                        filteredData.push({
                           'order_id': unfinishedTasksData[i],
                           'plot_id': plot_ids[j],
                           'sopr_id': _.find(task.plots, function(data){return data.plot_gid == plot_ids[j];}).sopr_id,
                           'sample_data': samplesData2d
                        });
                    }
                }
            }
            if (filteredData.length == 0 && unsyncedPins.length == 0) {
                $("#loading-popup").popup('close');
                $.mobile.loading('hide');

                navigator.notification.alert(
                    Localization.no_completely_sampled_plots,    // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            } else {
                localStorage.setItem('sentFinishedData' + authData.user_id, JSON.stringify(filteredData));
                TasksCollection.sendSampledData({
                    headers: {
                        'Authorization': 'Bearer ' + authData.access_token,
                    },
                    data: {
                        server: authData.server,
                        sampled_orders: JSON.stringify(filteredData)
                    },
                    type: 'POST',
                    dataType: 'json'
                });
            }
        },
        renderNewTasks: function () {
            if (typeof tasksTemplate == 'string') {
                tasksTemplate = _.template(tasksTemplate);
            }
            var data = {
                'Localization': Localization
            };

            this.$el.append(tasksTemplate(data)).trigger('create').trigger('resize');

            $('h1.menu-title').text(Localization.new_tasks);
            this.loadTasks(true);
        },
        renderUnfinishedTasks: function() {
            unfinishedTasksData = JSON.parse(localStorage.getItem('unfinishedTasks' + this.user_id)) || [];
            if (typeof tasksTemplate == 'string') {
                tasksTemplate = _.template(tasksTemplate);
            }
            var data = {
                'Localization': Localization
            };
            this.$el.append(tasksTemplate(data)).trigger('create').trigger('resize');

            $('h1.menu-title').text(Localization.unfinished_tasks);

            this.loadTasks(false);
        },
        loadTaskCategories: function () {
            var unfinishedTasksData = JSON.parse(localStorage.getItem('unfinishedTasks' + this.user_id)) || [];
            var allTasksData = JSON.parse(localStorage.getItem('tasks_data_' + this.user_id)) || [];

            var tasksData = [
                {
                    'isRead':false,
                    'title': Localization.unfinished_tasks,
                    'date': '',
                    'route': 'unfinishedTasks',
                    'tasksCount': unfinishedTasksData.length
                },
                {
                    'isRead':false,
                    'title': Localization.new_tasks,
                    'date': this.getLastSyncDate(),
                    'route': 'newTasks',
                    'tasksCount': allTasksData.length - unfinishedTasksData.length
                },
            ];

            $("#tasks-list").html('');

            $("#tasks-list").listview("refresh").trigger('create').trigger('resize');

            this.$el.trigger('resize');
        },
        loadTasks: function (arePlotsNew) {
            var authData = JSON.parse(localStorage.getItem('authData'));
            newTasksData = JSON.parse(localStorage.getItem('tasks_data_' + this.user_id)) || [];
            var unfinisedTasks = JSON.parse(localStorage.getItem('unfinishedTasks' + this.user_id)) || [];
            var finishedPlots = JSON.parse(localStorage.getItem('finishedPlots' + this.user_id)) || [];
            $('#task-categories').hide();
            $("#tasks-list").html('');

            $(window).on('resize', function() {
                $('#tasks-list').css({
                    width: $(window).width(),
                    height: $(window).height() - 122
                });
            });

            if(newTasksData.length === 0) {
                $("#loading-popup").popup('close');
                $.mobile.loading('hide');

                navigator.notification.alert(
                    Localization.no_tasks,    // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            }

            if(arePlotsNew){
                $("#tasks-list").append('<li><a href="#tasksPlots"><i class="fa fa-map fa-2x"></i></a></li>');
            }

            for(var i in newTasksData) {
                var newTask = newTasksData[i];
                if(newTask === null) {
                    continue;
                }

                if(arePlotsNew){
                    if(unfinisedTasks.indexOf(newTask.id.toString()) != -1) {
                        continue;
                    }
                }else{
                    if(unfinisedTasks.indexOf(newTask.id.toString()) == -1) {
                        continue;
                    }
                }

                var [firstPlot] = newTask.plots;
                var type = firstPlot.comment ? firstPlot.comment : ' ';

                $("#tasks-list").append('<li id="task-' + newTask.id + '" >' +
                                                '<a href="#plotsList/task_id/'+newTask.id + '" >' + 
                                                '<h4>' + newTask.id + ' ' + newTask.order_name + ' ' +
                                                '<span style="float:right;">' + 
                                                ((newTask.order_area != undefined) ? (newTask.order_area + ' ' + Localization[Constants.servers[authData.server].area_unit]) : '') +
                                                '</span>' + 
                                                '<div>' + Localization.type + ": " + type + '</div>' +
                                                '</h4>'+
                                                '</a>'+
                                            '</li>');
                $("#tasks-list").listview("refresh").trigger('create').trigger('resize');


                $("#task-plots-list-"+newTask.id).listview("refresh").trigger('create').trigger('resize');
            }

            $("#tasks-list").listview("refresh").trigger('create').trigger('resize');

            if(arePlotsNew && $("#get-new-tasks").length === 0){
                $("#tasks-footer").append('<button id="get-new-tasks" data-action="start" class="ui-btn ui-corner-all ui-btn-b ui-btn-icon-left ui-icon-arrow-d">' + Localization.download_tasks_data + '</button>');
                $('#get-new-tasks').off('click').on('click', $.proxy(this.getNewTasks, this));
            }
            this.$el.trigger('resize');
        },
        onSyncTasksSuccess: function () {
            var user_id = localStorage.getItem('user_id');

            finishedData = JSON.parse(localStorage.getItem('sentFinishedData' + user_id)) || [];

            var allTasksData = JSON.parse(localStorage.getItem('tasks_data_' + user_id)) || [];
            var samplesData = JSON.parse(localStorage.getItem('sampledTasks' + user_id));
            var unfinishedTasksData = JSON.parse(localStorage.getItem('unfinishedTasks' + user_id)) || [];
            var finishedPlots = JSON.parse(localStorage.getItem('finishedPlots' + user_id)) || [];

            var synchedTasks = JSON.parse(localStorage.getItem('synchedTasks' + user_id)) || [];
            //syncDate is in yyyy-mm-dd format
            var syncDate = new Date().toISOString().slice(0,10);

            for (var i = 0; i < finishedData.length; i++) {
                for(var j in allTasksData) {
                    if(allTasksData[j].id == finishedData[i].order_id) {
                        task_id = allTasksData[j].id;
                        plots = allTasksData[j].plots;
                        for (var k in plots){
                            if(finishedPlots.indexOf(plots[k].plot_gid) != -1){
                                allTasksData[j]['plots'].splice(k, 1);
                            }
                        }
                        if(allTasksData[j].plots.length === 0){
                            allTasksData.splice(j, 1);
                            unfinishedTasksData = _.filter(unfinishedTasksData, function(order_id){ return order_id != task_id; });
                        }

                        synchronisation = {
                            'order_id': task_id,
                            'date': syncDate
                        };

                        if (_.where(synchedTasks, synchronisation).length == 0) {
                            synchedTasks.push(synchronisation);
                        }
                    }
                }
                delete samplesData[finishedData[i].order_id][finishedData[i].plot_id];
                if(samplesData[finishedData[i].order_id].length === 0) {
                    delete samplesData[finishedData[i].order_id];
                }
            }

            localStorage.removeItem('sentFinishedData' + user_id);
            localStorage.removeItem('finishedPlots' + user_id);
            localStorage.setItem('tasks_data_' + user_id, JSON.stringify(allTasksData));
            localStorage.setItem('sampledTasks' + user_id, JSON.stringify(samplesData));
            localStorage.setItem('unfinishedTasks' + user_id, JSON.stringify(unfinishedTasksData));
            localStorage.setItem('synchedTasks'+ user_id, JSON.stringify(synchedTasks));

            PinsCollection.uploadPins();

            this.loadTaskCategories();
        },
        onSyncTasksFail: function () {
            $.mobile.loading('hide');
            $("#loading-popup").popup('close');
            $('#msg_popup #msg_content').text(Localization.sync_tasks_error);
            $('#msg_popup h1').text(Localization.error);
            $("#msg_popup").popup('open');
        },
        onSyncPinsFail: function () {
            $.mobile.loading('hide');
            $("#loading-popup").popup('close');
            $('#msg_popup #msg_content').text(Localization.sync_pins_error);
            $('#msg_popup h1').text(Localization.error);
            $("#msg_popup").popup('open');
        },
        getLastSyncDate: function () {
            var user_id = localStorage.getItem('user_id');
            var dateString = '-';

            if (localStorage.getItem('noty_sync_' + user_id)) {
                var lastSync = new Date(parseInt(localStorage.getItem('noty_sync_' + user_id)));
                dateString = moment(lastSync).format('L LTS');
            }

            return dateString;
        },
        getNewTasks: function() {
            TasksCollection.getNewSoilOrdesData();
        },
        onGetNewTasksDataSuccess: function () {
            $("#loading-popup").popup('close');
            $.mobile.loading('hide');
            this.loadTasks(true);
        },
        onGetNewSoilOrdesDataSuccess:function () {
            navigator.notification.alert(
                Localization.new_tasks_data_successfully_retrieved,    // message
                function() {},                  // callback
                '',                             // title
                Localization.ok                 // buttonName
            );
            this.loadTasks(true);
        },
        onGetNewSoilOrdesDataSuccessNoOrders:function(){
            navigator.notification.alert(
                Localization.no_new_tasks,    // message
                function() {},                  // callback
                Localization.error,                             // title
                Localization.ok                 // buttonName
            );
        }
    });

    return new tasksView();
});
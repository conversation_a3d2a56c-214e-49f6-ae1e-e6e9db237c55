/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/

apply plugin: 'com.android.application'

if (cdvHelpers.getConfigPreference('GradlePluginKotlinEnabled', 'false').toBoolean()) {
    apply plugin: 'kotlin-android'
    apply plugin: 'kotlin-android-extensions'
}

buildscript {
    apply from: '../CordovaLib/cordova.gradle'

    if(cdvHelpers.getConfigPreference('GradlePluginKotlinEnabled', 'false').toBoolean()) {
        String defaultGradlePluginKotlinVersion = kotlin_version

        /**
         * Fetches the user's defined Kotlin Version from config.xml.
         * If the version is not set or invalid, it will default to the ${defaultGradlePluginKotlinVersion}
         */
        String gradlePluginKotlinVersion = cdvHelpers.getConfigPreference('GradlePluginKotlinVersion', defaultGradlePluginKotlinVersion)
        if(!cdvHelpers.isVersionValid(gradlePluginKotlinVersion)) {
            println("The defined Kotlin version (${gradlePluginKotlinVersion}) does not appear to be a valid version. Falling back to version: ${defaultGradlePluginKotlinVersion}.")
            gradlePluginKotlinVersion = defaultGradlePluginKotlinVersion
        }

        // Change the version to be used.
        ext.kotlin_version = gradlePluginKotlinVersion
    }

    apply from: 'repositories.gradle'
    repositories repos

    dependencies {
        apply from: '../CordovaLib/cordova.gradle'

        classpath 'com.android.tools.build:gradle:4.0.0'

        if (cdvHelpers.getConfigPreference('GradlePluginKotlinEnabled', 'false').toBoolean()) {
            classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        }

        if(cdvHelpers.getConfigPreference('GradlePluginGoogleServicesEnabled', 'false').toBoolean()) {
            String defaultGradlePluginGoogleServicesVersion = '4.2.0'

            /**
             * Fetches the user's defined Google Services Plugin Version from config.xml.
             * If the version is not set or invalid, it will default to the ${defaultGradlePluginGoogleServicesVersion}
             */
            String gradlePluginGoogleServicesVersion = cdvHelpers.getConfigPreference('GradlePluginGoogleServicesVersion', defaultGradlePluginGoogleServicesVersion)
            if(!cdvHelpers.isVersionValid(gradlePluginGoogleServicesVersion)) {
                println("The defined Google Services plugin version (${gradlePluginGoogleServicesVersion}) does not appear to be a valid version. Falling back to version: ${defaultGradlePluginGoogleServicesVersion}.")
                gradlePluginGoogleServicesVersion = defaultGradlePluginGoogleServicesVersion
            }

            // Create the Google Services classpath and set it.
            String gradlePluginGoogleServicesClassPath = "com.google.gms:google-services:${gradlePluginGoogleServicesVersion}"
            println "Adding classpath: ${gradlePluginGoogleServicesClassPath}"
            classpath gradlePluginGoogleServicesClassPath
        }
    }
}

// Allow plugins to declare Maven dependencies via build-extras.gradle.
allprojects {
    apply from: 'repositories.gradle'
    repositories repos
}

task wrapper(type: Wrapper) {
    gradleVersion = '6.5'
}

// Configuration properties. Set these via environment variables, build-extras.gradle, or gradle.properties.
// Refer to: http://www.gradle.org/docs/current/userguide/tutorial_this_and_that.html
ext {
    apply from: '../CordovaLib/cordova.gradle'

    // The value for android.compileSdkVersion.
    if (!project.hasProperty('cdvCompileSdkVersion')) {
        cdvCompileSdkVersion = null;
    }
    // The value for android.buildToolsVersion.
    if (!project.hasProperty('cdvBuildToolsVersion')) {
        cdvBuildToolsVersion = null;
    }
    // Sets the versionCode to the given value.
    if (!project.hasProperty('cdvVersionCode')) {
        cdvVersionCode = null
    }
    // Sets the minSdkVersion to the given value.
    if (!project.hasProperty('cdvMinSdkVersion')) {
        cdvMinSdkVersion = null
    }
    // Sets the maxSdkVersion to the given value.
    if (!project.hasProperty('cdvMaxSdkVersion')) {
        cdvMaxSdkVersion = null
    }
    // The value for android.targetSdkVersion.
    if (!project.hasProperty('cdvTargetSdkVersion')) {
        cdvTargetSdkVersion = null;
    }
    // Whether to build architecture-specific APKs.
    if (!project.hasProperty('cdvBuildMultipleApks')) {
        cdvBuildMultipleApks = null
    }
    // Whether to append a 0 "abi digit" to versionCode when only a single APK is build
    if (!project.hasProperty('cdvVersionCodeForceAbiDigit')) {
        cdvVersionCodeForceAbiDigit = null
    }
    // .properties files to use for release signing.
    if (!project.hasProperty('cdvReleaseSigningPropertiesFile')) {
        cdvReleaseSigningPropertiesFile = null
    }
    // .properties files to use for debug signing.
    if (!project.hasProperty('cdvDebugSigningPropertiesFile')) {
        cdvDebugSigningPropertiesFile = null
    }
    // Set by build.js script.
    if (!project.hasProperty('cdvBuildArch')) {
        cdvBuildArch = null
    }

    // Plugin gradle extensions can append to this to have code run at the end.
    cdvPluginPostBuildExtras = []
}

// PLUGIN GRADLE EXTENSIONS START
apply from: "../cordova-plugin-code-push/agrobalance-build-extras.gradle"
apply from: "../phonegap-plugin-barcodescanner/agrobalance-barcodescanner.gradle"
apply from: "../phonegap-plugin-push/agrobalance-push.gradle"
// PLUGIN GRADLE EXTENSIONS END

def hasBuildExtras1 = file('build-extras.gradle').exists()
if (hasBuildExtras1) {
    apply from: 'build-extras.gradle'
}

def hasBuildExtras2 = file('../build-extras.gradle').exists()
if (hasBuildExtras2) {
    apply from: '../build-extras.gradle'
}

// Set property defaults after extension .gradle files.
ext.cdvCompileSdkVersion = cdvCompileSdkVersion == null ? (
    defaultCompileSdkVersion == null
        ? privateHelpers.getProjectTarget()
        : defaultCompileSdkVersion
) : Integer.parseInt('' + cdvCompileSdkVersion);

if (ext.cdvBuildToolsVersion == null) {
    ext.cdvBuildToolsVersion = privateHelpers.findLatestInstalledBuildTools()
    //ext.cdvBuildToolsVersion = project.ext.defaultBuildToolsVersion
}
if (ext.cdvDebugSigningPropertiesFile == null && file('../debug-signing.properties').exists()) {
    ext.cdvDebugSigningPropertiesFile = '../debug-signing.properties'
}
if (ext.cdvReleaseSigningPropertiesFile == null && file('../release-signing.properties').exists()) {
    ext.cdvReleaseSigningPropertiesFile = '../release-signing.properties'
}

// Cast to appropriate types.
ext.cdvBuildMultipleApks = cdvBuildMultipleApks == null ? false : cdvBuildMultipleApks.toBoolean();
ext.cdvVersionCodeForceAbiDigit = cdvVersionCodeForceAbiDigit == null ? false : cdvVersionCodeForceAbiDigit.toBoolean();

// minSdkVersion, maxSdkVersion and targetSdkVersion
ext.cdvMinSdkVersion = cdvMinSdkVersion == null ? defaultMinSdkVersion : Integer.parseInt('' + cdvMinSdkVersion)
if (cdvMaxSdkVersion != null) {
    ext.cdvMaxSdkVersion = Integer.parseInt('' + cdvMaxSdkVersion)
}
ext.cdvTargetSdkVersion = cdvTargetSdkVersion == null ? defaultTargetSdkVersion  : Integer.parseInt('' + cdvTargetSdkVersion)

ext.cdvVersionCode = cdvVersionCode == null ? null : Integer.parseInt('' + cdvVersionCode)

def computeBuildTargetName(debugBuild) {
    def ret = 'assemble'
    if (cdvBuildMultipleApks && cdvBuildArch) {
        def arch = cdvBuildArch == 'arm' ? 'armv7' : cdvBuildArch
        ret += '' + arch.toUpperCase().charAt(0) + arch.substring(1);
    }
    return ret + (debugBuild ? 'Debug' : 'Release')
}

// Make cdvBuild a task that depends on the debug/arch-sepecific task.
task cdvBuildDebug
cdvBuildDebug.dependsOn {
    return computeBuildTargetName(true)
}

task cdvBuildRelease
cdvBuildRelease.dependsOn {
    return computeBuildTargetName(false)
}

task cdvPrintProps {
    doLast {
        println('cdvCompileSdkVersion=' + cdvCompileSdkVersion)
        println('cdvBuildToolsVersion=' + cdvBuildToolsVersion)
        println('cdvVersionCode=' + cdvVersionCode)
        println('cdvVersionCodeForceAbiDigit=' + cdvVersionCodeForceAbiDigit)
        println('cdvMinSdkVersion=' + cdvMinSdkVersion)
        println('cdvMaxSdkVersion=' + cdvMaxSdkVersion)
        println('cdvTargetSdkVersion=' + cdvTargetSdkVersion)
        println('cdvBuildMultipleApks=' + cdvBuildMultipleApks)
        println('cdvReleaseSigningPropertiesFile=' + cdvReleaseSigningPropertiesFile)
        println('cdvDebugSigningPropertiesFile=' + cdvDebugSigningPropertiesFile)
        println('cdvBuildArch=' + cdvBuildArch)
        println('computedVersionCode=' + android.defaultConfig.versionCode)
        android.productFlavors.each { flavor ->
            println('computed' + flavor.name.capitalize() + 'VersionCode=' + flavor.versionCode)
        }
    }
}

android {
    defaultConfig {
        versionCode cdvVersionCode ?: new BigInteger("" + privateHelpers.extractIntFromManifest("versionCode"))
        applicationId privateHelpers.extractStringFromManifest("package")

        if (cdvMinSdkVersion != null) {
            minSdkVersion cdvMinSdkVersion
        }

        if (cdvMaxSdkVersion != null) {
            maxSdkVersion cdvMaxSdkVersion
        }

        if(cdvTargetSdkVersion != null) {
            targetSdkVersion cdvTargetSdkVersion
        }
    }

    lintOptions {
      abortOnError false;
    }

    compileSdkVersion cdvCompileSdkVersion
    buildToolsVersion cdvBuildToolsVersion

    // This code exists for Crosswalk and other Native APIs.
    // By default, we multiply the existing version code in the
    // Android Manifest by 10 and add a number for each architecture.
    // If you are not using Crosswalk or SQLite, you can
    // ignore this chunk of code, and your version codes will be respected.

    if (Boolean.valueOf(cdvBuildMultipleApks)) {
        flavorDimensions "default"

        productFlavors {
            armeabi {
                versionCode defaultConfig.versionCode*10 + 1
                ndk {
                    abiFilters = ["armeabi"]
                }
            }
            armv7 {
                versionCode defaultConfig.versionCode*10 + 2
                ndk {
                    abiFilters = ["armeabi-v7a"]
                }
            }
            arm64 {
                versionCode defaultConfig.versionCode*10 + 3
                ndk {
                    abiFilters = ["arm64-v8a"]
                }
            }
            x86 {
                versionCode defaultConfig.versionCode*10 + 4
                ndk {
                    abiFilters = ["x86"]
                }
            }
            x86_64 {
                versionCode defaultConfig.versionCode*10 + 5
                ndk {
                    abiFilters = ["x86_64"]
                }
            }
        }
    } else if (Boolean.valueOf(cdvVersionCodeForceAbiDigit)) {
        // This provides compatibility to the default logic for versionCode before cordova-android 5.2.0
        defaultConfig {
            versionCode defaultConfig.versionCode*10
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    if (cdvReleaseSigningPropertiesFile) {
        signingConfigs {
            release {
                // These must be set or Gradle will complain (even if they are overridden).
                keyAlias = ""
                keyPassword = "__unset" // And these must be set to non-empty in order to have the signing step added to the task graph.
                storeFile = null
                storePassword = "__unset"
            }
        }
        buildTypes {
            release {
                signingConfig signingConfigs.release
            }
        }
        addSigningProps(cdvReleaseSigningPropertiesFile, signingConfigs.release)
    }

    if (cdvDebugSigningPropertiesFile) {
        addSigningProps(cdvDebugSigningPropertiesFile, signingConfigs.debug)
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
}

/*
 * WARNING: Cordova Lib and platform scripts do management inside of this code here,
 * if you are adding the dependencies manually, do so outside the comments, otherwise
 * the Cordova tools will overwrite them
 */


dependencies {
    implementation fileTree(dir: 'libs', include: '*.jar')

    if (cdvHelpers.getConfigPreference('GradlePluginKotlinEnabled', 'false').toBoolean()) {
        implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    }

    // SUB-PROJECT DEPENDENCIES START
    implementation(project(path: ":CordovaLib"))
    implementation "com.android.support:support-v13:23+"
    implementation "com.google.android.gms:play-services-gcm:9.0.2+"
    implementation "me.leolin:ShortcutBadger:1.1.4@aar"
    // SUB-PROJECT DEPENDENCIES END
}

def promptForReleaseKeyPassword() {
    if (!cdvReleaseSigningPropertiesFile) {
        return;
    }
    if ('__unset'.equals(android.signingConfigs.release.storePassword)) {
        android.signingConfigs.release.storePassword = privateHelpers.promptForPassword('Enter key store password: ')
    }
    if ('__unset'.equals(android.signingConfigs.release.keyPassword)) {
        android.signingConfigs.release.keyPassword = privateHelpers.promptForPassword('Enter key password: ');
    }
}

gradle.taskGraph.whenReady { taskGraph ->
    taskGraph.getAllTasks().each() { task ->
      if(['validateReleaseSigning', 'validateSigningRelease', 'validateSigningArmv7Release', 'validateSigningX76Release'].contains(task.name)) {
         promptForReleaseKeyPassword()
      }
    }
}

def addSigningProps(propsFilePath, signingConfig) {
    def propsFile = file(propsFilePath)
    def props = new Properties()
    propsFile.withReader { reader ->
        props.load(reader)
    }

    def storeFile = new File(props.get('key.store') ?: privateHelpers.ensureValueExists(propsFilePath, props, 'storeFile'))
    if (!storeFile.isAbsolute()) {
        storeFile = RelativePath.parse(true, storeFile.toString()).getFile(propsFile.getParentFile())
    }
    if (!storeFile.exists()) {
        throw new FileNotFoundException('Keystore file does not exist: ' + storeFile.getAbsolutePath())
    }
    signingConfig.keyAlias = props.get('key.alias') ?: privateHelpers.ensureValueExists(propsFilePath, props, 'keyAlias')
    signingConfig.keyPassword = props.get('keyPassword', props.get('key.alias.password', signingConfig.keyPassword))
    signingConfig.storeFile = storeFile
    signingConfig.storePassword = props.get('storePassword', props.get('key.store.password', signingConfig.storePassword))
    def storeType = props.get('storeType', props.get('key.store.type', ''))
    if (!storeType) {
        def filename = storeFile.getName().toLowerCase();
        if (filename.endsWith('.p12') || filename.endsWith('.pfx')) {
            storeType = 'pkcs12'
        } else {
            storeType = signingConfig.storeType // "jks"
        }
    }
    signingConfig.storeType = storeType
}

for (def func : cdvPluginPostBuildExtras) {
    func()
}

// This can be defined within build-extras.gradle as:
//     ext.postBuildExtras = { ... code here ... }
if (hasProperty('postBuildExtras')) {
    postBuildExtras()
}

if (cdvHelpers.getConfigPreference('GradlePluginGoogleServicesEnabled', 'false').toBoolean()) {
    apply plugin: 'com.google.gms.google-services'
}

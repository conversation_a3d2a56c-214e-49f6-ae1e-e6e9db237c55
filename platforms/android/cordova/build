#!/usr/bin/env node

/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/

var args = process.argv;
var Api = require('./Api');
var nopt = require('nopt');
var path = require('path');

// Support basic help commands
if (['--help', '/?', '-h', 'help', '-help', '/help'].indexOf(args[2]) >= 0) {
    require('./lib/build').help();
}

// Do some basic argument parsing
var buildOpts = nopt({
    verbose: Boolean,
    silent: <PERSON><PERSON><PERSON>,
    debug: <PERSON><PERSON><PERSON>,
    release: <PERSON><PERSON>an,
    nobuild: <PERSON>ole<PERSON>,
    buildConfig: path
}, { d: '--verbose' });

// Make buildOptions compatible with PlatformApi build method spec
buildOpts.argv = buildOpts.argv.original;

require('./loggingHelper').adjustLoggerLevel(buildOpts);

new Api().build(buildOpts)
    .catch(function (err) {
        console.error(err.stack);
        process.exit(2);
    });

#!/usr/bin/env node

/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/

var Api = require('./Api');
var nopt = require('nopt');
var path = require('path');

// Support basic help commands
if (['--help', '/?', '-h', 'help', '-help', '/help'].indexOf(process.argv[2]) >= 0) {
    require('./lib/run').help();
}

// Do some basic argument parsing
var runOpts = nopt({
    verbose: <PERSON><PERSON><PERSON>,
    silent: <PERSON><PERSON>an,
    debug: <PERSON><PERSON><PERSON>,
    release: Boolean,
    nobuild: <PERSON><PERSON><PERSON>,
    buildConfig: path,
    archs: String,
    device: <PERSON>olean,
    emulator: <PERSON><PERSON><PERSON>,
    target: String
}, { d: '--verbose' });

// Make runOptions compatible with PlatformApi run method spec
runOpts.argv = runOpts.argv.remain;

require('./loggingHelper').adjustLoggerLevel(runOpts);

new Api().run(runOpts)
    .catch(function (err) {
        console.error(err, err.stack);
        process.exit(2);
    });

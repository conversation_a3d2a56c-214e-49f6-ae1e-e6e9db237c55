{"name": "com.agrobalance", "version": "1.0.0", "displayName": "Agrobalance", "cordova": {"platforms": ["android"], "plugins": {"cordova-plugin-whitelist": {}, "cordova-zip-plugin": {}, "cordova-plugin-appinfo": {}, "cordova-plugin-camera": {}, "cordova-plugin-device": {}, "cordova-plugin-dialogs": {}, "cordova-plugin-file": {}, "cordova-plugin-file-transfer": {}, "cordova-plugin-geolocation": {}, "phonegap-plugin-push": {"SENDER_ID": "751733604932"}, "cordova-plugin-device-orientation": {}, "phonegap-plugin-barcodescanner": {}, "cordova-plugin-bluetooth-serial": {}, "code-push": {}, "cordova-plugin-zip": {}, "cordova-plugin-code-push": {}, "com.jjdltc.cordova.plugin.zip": {}}}, "dependencies": {"cordova-android": "^12.0.1", "cordova-plugin-whitelist": "^1.3.5"}, "devDependencies": {"code-push": "^2.0.6", "cordova-plugin-appinfo": "^2.0.3", "cordova-plugin-bluetooth-serial": "^0.4.7", "cordova-plugin-camera": "^4.1.0", "cordova-plugin-code-push": "^1.11.16", "cordova-plugin-device": "^2.1.0", "cordova-plugin-device-orientation": "^2.0.1", "cordova-plugin-dialogs": "^2.0.2", "cordova-plugin-file": "^6.0.2", "cordova-plugin-file-transfer": "^1.7.1", "cordova-plugin-geolocation": "^4.1.0", "cordova-plugin-zip": "^3.1.0", "cordova-zip-plugin": "^0.0.4", "phonegap-plugin-barcodescanner": "^8.1.0", "phonegap-plugin-push": "^2.3.0"}}
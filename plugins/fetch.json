{"cordova-plugin-whitelist": {"source": {"type": "registry", "id": "cordova-plugin-whitelist@1"}, "is_top_level": true, "variables": {}}, "com.jjdltc.cordova.plugin.zip": {"source": {"type": "registry", "id": "cordova-zip-plugin@^0.0.4"}, "is_top_level": true, "variables": {}}, "cordova-plugin-appinfo": {"source": {"type": "registry", "id": "cordova-plugin-appinfo"}, "is_top_level": true, "variables": {}}, "cordova-plugin-camera": {"source": {"type": "registry", "id": "cordova-plugin-camera"}, "is_top_level": true, "variables": {}}, "cordova-plugin-compat": {"source": {"type": "registry", "id": "cordova-plugin-compat"}, "is_top_level": false, "variables": {}}, "cordova-plugin-device": {"source": {"type": "registry", "id": "cordova-plugin-device"}, "is_top_level": true, "variables": {}}, "cordova-plugin-dialogs": {"source": {"type": "registry", "id": "cordova-plugin-dialogs"}, "is_top_level": true, "variables": {}}, "cordova-plugin-file": {"source": {"type": "registry", "id": "cordova-plugin-file"}, "is_top_level": true, "variables": {}}, "cordova-plugin-file-transfer": {"source": {"type": "registry", "id": "cordova-plugin-file-transfer"}, "is_top_level": true, "variables": {}}, "cordova-plugin-geolocation": {"source": {"type": "registry", "id": "cordova-plugin-geolocation"}, "is_top_level": true, "variables": {}}, "phonegap-plugin-push": {"source": {"type": "registry", "id": "phonegap-plugin-push"}, "is_top_level": true, "variables": {"SENDER_ID": "751733604932"}}, "cordova-plugin-bluetooth-serial": {"source": {"type": "registry", "id": "cordova-plugin-bluetooth-serial@~0.4.6"}, "is_top_level": true, "variables": {}}, "cordova-plugin-device-orientation": {"source": {"type": "registry", "id": "cordova-plugin-device-orientation@~1.0.4"}, "is_top_level": true, "variables": {}}, "phonegap-plugin-barcodescanner": {"source": {"type": "registry", "id": "phonegap-plugin-barcodescanner@~6.0.2"}, "is_top_level": true, "variables": {}}, "code-push": {"source": {"type": "registry", "id": "code-push"}, "is_top_level": true, "variables": {}}, "cordova-plugin-zip": {"source": {"type": "registry", "id": "cordova-plugin-zip"}, "is_top_level": true, "variables": {}}, "cordova-plugin-code-push": {"source": {"type": "registry", "id": "cordova-plugin-code-push"}, "is_top_level": true, "variables": {}}}
{"_from": "ast-types@0.x.x", "_id": "ast-types@0.11.7", "_inBundle": false, "_integrity": "sha512-2mP3TwtkY/aTv5X3ZsMpNAbOnyoC/aMJwJSoaELPkHId0nSQgFcnU4dRW3isxiz7+zBexk0ym3WNVjMiQBnJSw==", "_location": "/ast-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ast-types@0.x.x", "name": "ast-types", "escapedName": "ast-types", "rawSpec": "0.x.x", "saveSpec": null, "fetchSpec": "0.x.x"}, "_requiredBy": ["/degenerator"], "_resolved": "https://registry.npmjs.org/ast-types/-/ast-types-0.11.7.tgz", "_shasum": "f318bf44e339db6a320be0009ded64ec1471f46c", "_spec": "ast-types@0.x.x", "_where": "D:\\projects\\soil-samples-app\\node_modules\\degenerator", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/benjamn/ast-types/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Esprima-compatible implementation of the Mozilla JS Parser API", "devDependencies": {"babel-types": "^6.26.0", "babylon": "^7.0.0-beta.40", "espree": "^4.0.0", "esprima": "~4.0.0", "esprima-fb": "~14001.1.0-dev-harmony-fb", "flow-parser": "^0.83.0", "glob": "^7.1.2", "mocha": "^5.0.0", "reify": "^0.18.0"}, "engines": {"node": ">=4"}, "homepage": "http://github.com/benjamn/ast-types", "keywords": ["ast", "abstract syntax tree", "hierarchy", "mozilla", "spidermonkey", "parser api", "esprima", "types", "type system", "type checking", "dynamic types", "parsing", "transformation", "syntax"], "license": "MIT", "main": "main.js", "name": "ast-types", "repository": {"type": "git", "url": "git://github.com/benjamn/ast-types.git"}, "scripts": {"test": "test/run.sh"}, "version": "0.11.7"}
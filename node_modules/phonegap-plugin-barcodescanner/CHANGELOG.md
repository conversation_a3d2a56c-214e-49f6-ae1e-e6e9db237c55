# Change Log

## v8.1.0

- Don't use `dispatch_sync` if already in the main thread ([#797](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/797)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d45ffc32c03e489b6bb3da8a29d950e7a89f4812)
- Add missing versions to `CHANGELOG.md` ([#796](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/796)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/bb4e66c60ef0079277e2b9abda22d57679a02251)
- Merge pull request [#780](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/780) from erisu/cordova9-require-fix [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/baa45a679fd6c7af418dabd0d99aca506768cae8)
- Updated Travis CI: Test Node 6 to 10 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1903737253ee44b40777617dd9b98b4f8af756ba)
- fix: replace `requireCordovaModule` with require for Cordova 9 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4c4127665fdfaab60ce73f97e7cb2acd92aa28f7)
- Document `AZTEC` as supported ([#749](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/749)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/acfed444d5845e87b6ba85af7629cc70ef0810f0)
- Update supported types for iOS ([#748](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/748)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/18312c1d1e2a94e4f95add450763b36c1b1f2c57)

## v8.0.1

- Bump version ([#740](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/740)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c5dbca2770d7c0f22ccb23473cd38c5fad8f8fa9)
- Convert interface orientation to video orientation on iOS ([#71](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/71)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/26f07b01c41816dcba6becf2fefe69538ec73eda)
- [FIX] Localized strings ignored [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9c200e002e447e3b6d950f9fccccbf0538c44bc5)
- lengthen stale window [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2e4af7abf001b640e9ff225b559009dec0075929)
- Update `README.md` ([#715](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/715)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8f7a646cc79443b0f73ee5591d3426b81a2fc4a9)
- Fixed `PDF_417` barcode format name in the `README.md` ([#692](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/692)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a7c36291f02d7d3134f534b136a2e234911111cc)
- Create `stale.yml` [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/334a0be548eae120a9c72eee8bec8973f9de46fa)
- Create `lock.yml` [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ad1ff522e523d80ebf0c774926fef9c5318db94a)

## v8.0.0

- Bump version and set engines [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e2ba60ca6b7e36306b02a0f3c437fbee51bda2ed)
- Remove deprecated platforms from plugin.xml ([#675](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/675)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2a17b6b457c03ec56e8c8244079097596db8849c)
- Also update `README.md` [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/41545281dfd9412b8b4ea0430d4fdc44921a78d2)
- Remove deprecated platforms [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/39b729c4fe80faa92c2d22b765d79d20fea40db2)
- Make request code smaller [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/db447e03e6677c5b6b1020d5f8244291c5c8ab45)
- Bump version [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1019fd5a70f1336682cd20556fdf7b8549e3f053)


## v7.1.2

- Bump version ([#657](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/657)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5c915006d94f7efa43b9e621b21caa89eca5808e)
- Bump version [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1019fd5a70f1336682cd20556fdf7b8549e3f053)
- Document `ANDROID_SUPPORT_V4_VERSION` ([#656](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/656)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/93650ddf9759bfb1b7d66a59b5363221ad36b52b)
- Update `README.md` ([#557](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/557)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b781f5d95322fdf2ab550c823cbc167f229c2844)
- reset autoFocusRangeRestriction when bar code scan done ([#65](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/65)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4a1aa56656c1a8db7342695ff28df2582fff62f8)

## v7.1.1

- Bump version [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/0474ec1347b88b7a5c12f230254febd9d75a2fc0)
- Check if `NSCameraUsageDescription` is set and return error instead of crash ([#643](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/643)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e516a87818c5a535376b705503e255bbb4e78d90)
- add `ITF` (less than 14 digit) support ([#636](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/636)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a78262b449056034130ffde1caa780853bec49d9)
- Remove `ZXing` code and files [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/bc42890c99e7511f56001e5d2dbf0abf205cf2c5)


## v7.1.0

- Bump version and engines ([#627](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/627)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/aad4a91f858ae54f5c0b1b613b603ce87f910cda)
- Redo usage descriptions ([#626](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/626)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b9a32ba14e24aa28490d901d62a2cf1a4caff4ae)

## v7.0.4

- Merge pull request [#625](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/625) from jcesarmobile/bump-version [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7cdfcb669785d174ee32bffdfb16995863b06415)
- Bump version [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a5b62fc3738a9db8816e9f3727f696b04125693e)
- Merge pull request [#623](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/623) from jcesarmobile/revert-usage-description [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1b2aa76453021c2a0e5ba7bfc6b4dca581755489)
- Merge pull request [#624](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/624) from adebree/patch-1 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6e88cbbec9032513738d992dcd8cd22de4731b20)
- Align `previewLayer` orientation with device [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/17de6c5451ad86edddf438760ec4b51b993be850)
- Revert usage description changes [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/46b925cda5e53b7a56a3f0169a073bb6c07d0a08)


## v7.0.3

- 7.0.3 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b508cd36ca5e0e52ff3c23b09c64675f25eda084)
- :bookmark: Bumping plugin version to 7.0.3 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2cb4d1cff345c0a590034429d2d320e940e466e2)
- Move support from gradle to `plugin.xml` and make version configurable [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/16c97b7b6d0f845282d0633e65593ac263baaa58)
- Use safe areas and reposition elements on rotation ([#585](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/585)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/85faa874620f8e0598a4c050393378fae4ff54c1)
- Remove usage description from `plugin.xml` ([#598](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/598)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/add72c9014fe6b867c0dddb6caa3248df752a58e)

## v7.0.2

- 7.0.2 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/97ea85db70c6aa0fea849b0664a32e2e7e39fc96)
- :bookmark: Bumping plugin version to 7.0.2 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/cf89947f159d28dd062be1a7eaa72b6506f203ca)
- Change the `.aar` from `resource-file` to `lib-file` ([#592](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/592)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4dcf4ea1a2f484081fa7bb680a51cf241df9a23c)

## v7.0.1

- 7.0.1 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/54bfa7a2d2f6d035cbff91c3d6f78832889270e1)
- :bookmark: Bumping plugin version to 7.0.1 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/98f2929ca9d84cb591f4e69c3c630f0bfc8330e0)
- Removed min sdk version from grandle ([#450](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/450)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/36284b161eb721fead059de7760adc022c3e5d9d)

## v7.0.0

- 7.0.0 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d33fcd0117524f6a0ce8cb06a978c7c71c347d10)
- :bookmark: Bumping plugin version to 7.0.0 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1959622dc1a458d3907ebc35bcf1430699cadd72)
- :arrow_up: update `pluginpub` to 0.0.9 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/57c3f1387ccd22db8d5410841b15cbd5543d3f7f)
- :heavy_minus_sign: remove `cordova-plugin-compat` dependency [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f6fc30ac4b25e8a5d8ab306f4b6e574277fc2317)
- [readme] updating PhoneGap Build instructions [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9da5174cd6edd26fc022f081e1780f3f9a9b3d1c)
- [WinJS][Win10] Fixes "formats" in options passed to `BarcodeReader.scan()` being ignored ([#524](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/524)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1f016d9564a494cf0ecc86c41be89fabdfa9fa3f)
- :penguin: Issue [#508](https://github.com/phonegap/phonegap-plugin-barcodescanner/issues/508): Is there any way to disable the beep in android? [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f316c4e10cd01e83797d9f122666c85472bcbd67)
- :penguin: Issue [#512](https://github.com/phonegap/phonegap-plugin-barcodescanner/issues/512): Generated `.gradle` file invalid, `parseInt` takes wrong argument [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a0d8ac4e17cbcb695175915645a7fab7fcadf03c)
- Convert the handler functions for the `torch`, `flipCamera`, and `shutter` buttons to `IBAction` ([#511](https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/511)) [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f0a8664182688eb5db78373832a114bc5e717379)
- Merge pull request [#522](https://github.com/phonegap/phonegap-plugin-barcodescanner/issues/522) from josephma93/patch-2 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5f9461ebd9c23b22a4cea573ba8279b0b978243e)
- Update `README.md` [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fdf1fdaebfd713f53a7042a9ca74385a25cadca3)
- Merge pull request [#520](https://github.com/phonegap/phonegap-plugin-barcodescanner/issues/520) from aruis/fix_readme [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/55e844003499bac956bbdd907c76e30ae89e509f)
- Update `README.md` [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/682a7e54517691775c57b16086b9e27ea7720b27)
- Updating `CHANGELOG.md` [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/af3494b5ff7be71f8159a35d98804ec3cc23a95d)

## [v6.0.8](https://github.com/* remote origin
  Fetch URL: https://github.com//tree/v6.0.8) (2017-07-18)
[Full Changelog](https://github.com/* remote origin
  Fetch URL: https://github.com//compare/v6.0.7...v6.0.8)

- 6.0.8 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/b3e99476b768df3fb4da50ff3bca4aea6f863710)
- :bookmark: Bumping plugin version to 6.0.8 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/7cf0c179ca40c8fefaec4016ac2d759ae728cd7a)
- :bug: Issue #364: UPC_A codes now scans with extra 0 at the start [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/f311beeb945f80ac8790391f8238bd57d9fa0cd9)
- Merge pull request #505 from renanbandeira/patch-1 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/919a626daf6df124c79c792863b352ca0a87044d)
- Updates README.md with new saveHistory prop [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/e63ab0db5e278d9565b50ab1f5140dd75ecd1742)
- reverts 523501d [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/7b3c22aeb9cf0683937ae43cc30ccbff1dfbd83c)
- Gets saveHistory from preference defined in config [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/523501d8d7199928093c8dbebf67d9358476cc77)
- Adds saveHistory flag [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/132359c564750651a9b10a01caf4b8dc30862556)
- Updating CHANGELOG [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/20727f81f2add352433c0649a9e5e3e5093467ac)

## [v6.0.7](https://github.com/* remote origin
  Fetch URL: https://github.com//tree/v6.0.7) (2017-07-12)
[Full Changelog](https://github.com/* remote origin
  Fetch URL: https://github.com//compare/v6.0.6...v6.0.7)

- 6.0.7 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/78dbf1cccb30935b832dc659eb377541edc52786)
- :bookmark: Bumping plugin version to 6.0.7 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/d0a2b4c52169553a86fc3fb5e00c0caa489d097d)
- :wrench: update pluginpub version [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/fc7e3be16f3b1c811e5013f028bed934ebd7c9c1)
- :apple: Issue #238: ios callbacks not called [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/e33e64ef462d00a95a0c6f3258a087f61f0e15da)
- Merge pull request #431 from phonegap/ios-fix-426 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/c65ba2f987838331eaa8a569b739515cad7a91e0)
- :apple: Issue #340: iOS WebThreadLock crash on barcodeScanFailed [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/217d673644b6df96b175ce78ffa9cd8aee24135b)
- Merge pull request #472 from jcesarmobile/beep-fix [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/8091c92f5e1e68e50f22fd171754a77740a76859)
- :memo: Issue #470: PDF417 vs PDF_417, which one is valid [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/7ed5b212d25f2c0918e7ac0353cc3d59d8094a4c)
- :fire: Removing these tests [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/54a28dd8871b81db810aed7df0a9909d71937b8b)
- :wrench: Issue #473: Travis fails [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/68a2c5d9064bf927df0c5a862009d5b9812785b7)
- :white_check_mark: Issue #473: Travis fails [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/2e335b82b7bd3ad9b7efc2c87adcb441c0d88bda)
- Issue #330: Add a LICENSE file [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/a2f59f7adba728006c40dec431ecedf1c229f6e6)
- :memo: Issue #471: README.md fix [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/cce5f050b8861dd05e6e8e0a03fc4b301ed3788f)
- Fix beep [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/3cf20a6eb3397f3a2bb61a044c02f1e4810b5810)
- Merge pull request #429 from jcesarmobile/pgb-usage-description [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/b5a135c6788e1a9ecb9f06a087931701f0b7a321)
- Camera cannot focus due to wrong aspect ratio since plugin 6.0.5 #392 [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/04e0b3dadfe9aad8ec90e1d9480efe149f7e64f1)
- [ios] re-add support for formats option [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/75ab7396d0e86b78188369d5f27a5c4fea602921)
- Document usage description for phonegap build [view commit](http://github.com/* remote origin
  Fetch URL: https://github.com//commit/01a9f77961be74581fe25786c17e351026f1bbb5)

# Change Log

[6.0.5](https://github.com/phonegap/phonegap-plugin-barcodescanner/tree/6.0.5) (2016-12-22)
[Full Changelog](https://github.com/phonegap/phonegap-plugin-barcodescanner/compare/6.0.4...6.0.5)

- Flashlight default value #385, Turning off found plain text #259, Remove Result Prompt #234, Custom Result Messages #219 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fee66a7d1b4c3bca845beb3fa8c99365b4c15ce2)
- Flipping orientation on Android crashes app [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/72ae2c75d3d66df24c124a6da9c26e48f7ad36a0)
- Focus improvements [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/52a23b0f42d454d6a9f2e29a693b5e984c0bb13f)
- Crash on iOS on low memory devices [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6e3718c2db03fda3d8ea439a9471b3a08e121f21)
- Disable Animation [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f9960f9b81aef35bd85ecba856c63058bc109ac1)
- Flashlight option [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a7ed6890d4e5e2cb386c8754aca081fad559ba1a)
- Merge pull request #343 from cepm-nate/master [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/77622ca92f5607a8d22131f62f2dea720857a5ed)
- Merge pull request #334 from lucatorella/patch-1 [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/10f94bc81aff5329291272a4922837e04569a339)
- Merge pull request #346 from kunder-lab/master [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1cdbf871c9d290630e4535e8ed9db0804b413f74)
- Catch Class not registered exception in case of missing Media Player [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d43964f06c8eb57fc168d7b19027462e957ba23d)
- Adds promise scan .done particularly to handle "No cameras" error [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/68b6fd14a95f34b32ea2bb651f9715ff880e4ade)
- Merge pull request #358 from vladimir-kotikov/explicit_intent [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/75cffc3282f09f6973d078d8ce9b306bff210bd5)
- Use explicit intent to launch scan activity [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4c046898543dee838b259997a5ec360240e26354)
- Merge pull request #338 from jlowe234/master [view commit](https://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7f4e4daf19951692873a490c845446cfa0fbda68)
- 6.0.2 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/18f958796ee9587d5bc21e6229f40139dd4f0c8a)
- Bumping plugin version to 6.0.2 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8728015c06d6d201cbd421621384cd19857f6720)
- Update pluginpub [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ca4973214b3f99a135d3844d52f40a086022ddcb)
- Merge pull request #303 from vladimir-kotikov/memory_consumption_fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/631ab8fabe16266fb8ea6168d96250be11b8efd1)
- Merge pull request #318 from vladimir-kotikov/surface3_crash [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4fffc8d187ddd7ee8df90ba285a5ae6eaac980fe)
- Add contribution doc, issue and pull request templates [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/31d9badd9b9b3278a78650bcccd105a95a7b4c78)
- Add editorconfig [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c33c2ab4d63f476bc63c3dd8a5b18f0301baf30f)
- Setup CHANGELOG [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1c2491db0f3e15322e543e87def64159cc4b3bb9)
- Setup regression tests [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4d6ee25e8f1ca3b2589c979a23f987afa60bb701)
- Issue #270: OS 10 requires Camera permission reason [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/380a56a0b5236bf867bd11d10ceaa185351ea892)
- Merge pull request #302 from jcesarmobile/add-permission-check [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7dd0e8404394b1bcb14fef49cf0301f5b32503c4)
- Merge pull request #271 from EddyVerbruggen/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8004584a8f266fc4715ffc69fb7148a0a83724ae)
- Merge pull request #307 from timkim/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d775cc7995c4ef6935c127a02d2d8164243bb795)
- Avoid crash on Surface3 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1473daa0a8d2a4c9a0a7b5330be771acaf5a295a)
- Merge pull request #314 from ben-8409/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2af63247c8f379bedbfb323734995b7420005367)
- only set cdvMinSdkVersion if not set or smaller then 15. Fixes #275 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5513f5c99d9363582e5e77a77b0b6fd3ac8f85c9)
- Merge pull request #183 from stigok/readme-update [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/97a29911c49ad7aa4406ae99a9b74219212ee608)
- Require camera [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5e58c0a1b8dabb301836440d6765f2423b3a9482)
- [#277] - fix for crash on iOS switching cameras [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6c3d74dcbde681a464ba0abc29c5a3d084b09739)
- Crash fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7841f6deba953121bc6d74bbf60d3380a466d73f)
- Reduce memory consumption by using smaller video resolution [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/89a9c69f6d2ba2d0e3d5ae25d937d3772c48b67c)
- iOS: Added permission ckeck and fail if it's not permited [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/59301afe045509f53d44c2f17bc7cd9927c357c1)
- Updated version to 6.0.1 for release [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3cf975bda01cdddd71b03d74ce31c97595ae98af)
- Merge pull request #285 from daserge/visibilitychange [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9d4a96123030f71f2f85676c61a4bfb8b3846592)
- [Windows] Handled visibilitychange to avoid camera freeze on minimize [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/005c30309d3c0b371c5fc4ceceb2ab8f7f2ffb40)
- Merge pull request #284 from daserge/version-bump [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/11dd6b5ad2d3528df57a63f3b6ff093452e3bfb9)
- Updated version to 6.0.0 for release [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/0f534db7e2dcee9348a2e707c7f47ebd0df48b8c)
- https://github.com/phonegap/phonegap-plugin-barcodescanner/issues/270 iOS 10 requires Camera permission reason [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2895b3b85506dd7dd91c93a741e1cc13acc59f09)
- Merge pull request #263 from daserge/check-arch [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/dd85e2530d9638ecac2774c2a00a7b9ec2a573b3)
- Adds a windows pre compile/run hook to ensure an arch is specified [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/49e33a85a08cfe0b1c5559d24d7f6f42a60334b5)
- Merge pull request #269 from daserge/win8-deprecation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/865e1b678fd93b1f712084d65140f41f6ebc4a29)
- Merge pull request #256 from daserge/cleanup-on-events [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d3344f2d0a0666357a2e9bc4230902227cdd039d)
- Retargeted WinRTBarcodeReader component, deprecated windows8 platform support [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/eba7eb3a4cc9f558feed571ed589134fd3a7cfa4)
- Merge pull request #170 from jcesarmobile/callback-with-blocks [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ae9f6e2a2e14aa0b16d86c039ea50ae1183f7bbe)
- send callbacks on dismissViewControllerAnimated completion [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/21152cf42978623ae6c67488124fb6474342f9bc)
- Merge pull request #266 from daserge/multiple-focus-3 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d92af72bd1ce76eb2f40b6fbb0a32292b4fa8817)
- Merge pull request #267 from daserge/multiple-scan [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/78a53459d3c0b9abd6fa510571a422e279dfadde)
- [Windows] Focus fails on multiple taps [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f18ed1a78c3b720ee88497b84734bcd77699ea2d)
- Prevent multiple calls to scanner [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2aa5bdcda508bd4164326298da18638f955e0873)
- [Windows] Cleanup camera on app suspension Save/restore scanning state on suspend/resume [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/49489db0678e6e80a3b0744533d432681b9cfdb7)
- Merge pull request #260 from Telerik-Verified-Plugins/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/55e693843e3a556ee9ba9f2dab13fd9ca652396f)
- Stable plugin id [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/363335c13b96f7f2c5a728d99a8be137b6601e34)
- Stable plugin id - revert after PR [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/be3e490b95b99078740f473466b60cb0c7348796)
- Stable plugin id for PR [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/73c32f4e62467694ecf725e5d9d307d3ed94a3e1)
- Merge remote-tracking branch 'upstream/master' [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c0546f71e60991ff2dcbbde2d1e23443db5d19a7)
- https://github.com/phonegap/phonegap-plugin-barcodescanner/issues/40 https://github.com/phonegap/phonegap-plugin-barcodescanner/pull/171 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e9f1b1bfe18edfae2db087f91c9112b82909bd83)
- Scanning crashes on Windows phone on device rotation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c3bcd87e1c66dc0fca7c04a2bbbd405ea9fbed2f)
- Merge pull request #257 from daserge/multiple-focus-2 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/45ec98fb46e4f5d18bbc5fb4d6627065cccac248)
- Added missing CHECK_PLAYING_TIMEOUT Added a timeout before initial focus to prevent focusAsync hang on slow devices [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/eb31d2e3416f64bb3312ed639f2fa046b4551414)
- Merge pull request #250 from daserge/multipleFocus [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e65272ad4536bc49200d4ca8b23cd970d7a118db)
- [Windows] Focus fails on multiple taps, this closes #249 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b836297ed5139d495bd0f0b4aeabaaa9734e4154)
- Merge pull request #251 from daserge/close-button [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/128c80f63ae2aa490adbe0c27250598fdabdd722)
- Merge pull request #253 from daserge/win10-CameraStreamState [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5161af4c7f92d7b0edc71230f6b43127502e3568)
- Windows Mobile 10 - Close button does not work, this fixes #196 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/942d1ed199ec0ea7488bcf3d8ec0a9d6b711ec82)
- [Windows10] Fix The request is invalid in the current state [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2a3e4fd532fba820979046b85e573529742f3b62)
- Merge pull request #252 from daserge/css-fixes [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a3d4dfa18adeeb6ecb62a40ac0492d9825372458)
- [Windows] CSS fixes - no-select, no-zoom, no-scroll [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5364553b1e00d4515dfc5293f4baae3629d8c48b)
- Added a note about PGB [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b20c04648b4cfcd1170e098c257e535cd6762f72)
- Merge remote-tracking branch 'upstream/master' [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ce0cf94603c526a261cb80654be19652eae9d239)
- Merge remote-tracking branch 'upstream/master' [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d4b22bb0094fc216a7837f61ddfabb91b4f3450d)
- #221 updated version to 5.0.0 for release [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/27227e9d2b00174e816869bc6123071d470ade87)
- stable plugin id [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/762dea0958937ee2cc1b76831312733bb59a401f)
- Merge pull request #217 from Telerik-Verified-Plugins/merge-phonegap-official [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/bd6c295908a22a23d1a473f39db39956e6bc62bf)
- Bump plugin version to 4.2.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/0cda49bfe1229977d6bec855942c199d5357c7ee)
- Fix iOS issues after merge [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/06f4a7d04bec902ffa2109b79602f3955175a17a)
- Fixing Android after merge [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5641c8ea17d036f806fd2f15763ce16cc79ada3c)
- Re-added old platform files we removed after forking Wildabeast [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/172f164b68d620699ac14bcf6468a21a65a57ada)
- Merge remote-tracking branch 'upstream/master' into merge-phonegap-official [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6a7d7208b15000e004b894184860cc0f298b6e32)
- fix Android M permission [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/29d3308b7d46a94fbda10945d3ecb78a7d6d1c08)
- merge [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f0ad027425313f3edbfb9350ab81068ed4898894)
- #28 Plugin renaming my app [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/55b716ad8e6dd4bfad1921d2efa00eec45fd414b)
- #28 Plugin renaming my app [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1778bee6b028081ff4761c32748eca0c939b4368)
- #10 Warnings on IOS 8.4 when using library [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/57babcc2beb1f6f2d45b738b126e4588b1b1643e)
- #26 App crashes if the CordovaActivity is stopped during scanning - Moved `scan` to a background thread so it won't block the UI for about 50ms while initiating the scanner [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d0bf407951f13759484a8fd2a77e6c3d1fb8ecc1)
- Documented the new "orientation" feature for Android [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8dea274bb72d01039173c305cc4a276a9985a5c2)
- This fixes a few issues in our fork: Image upside down on tablet #20 Android UI doesn't support Portrait and is small #4 Prebuilt Zebra Crossing (xzing) Library #3 Forcing landscape on camera android #17 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/25e00cd9e2d3a6789663ef9c0b9210f5ebf6776b)
- Merge pull request #206 from sarangan12/Windows10Fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2a5e289085e8a9a11b4863d7ddbdfcea58b22510)
- Fix for the crash in Windows 10 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8e46fcb76d49c76f4598be195e489cd81fc29217)
- #25 iOS app scans randomly UPC and other EAN formats when scanning EAN-13 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/dde3c197026412811e719e016d2adad14330ad78)
- #14 Cannot read code_128 barcodes in iOS [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c0673038124e623e2c6f6558b87f75dd611d3171)
- Update README.md [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b8ea34e5ac4b4e7b34b991e4e1065ef8619f776b)
- Fixes Windows Mobile - Issue with Orientation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/383406708e265ef4eddea28a3c07163fc4787519)
- Merge pull request #168 from MSOpenTech/escape_fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d1dcfe0763cfdb24d0fc1f5c5811dcd05bfa74d8)
- Fix plugin installation failure in VS2015 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4c0dcf4b27e9c6c4851f0b642a9436ab1c85bde9)
- Merge pull request #135 from MSOpenTech/windows_refactor [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e22581cb56e9c6a147860ee6e6f4ce4991186493)
- Refactor windows implementation in promise manner [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/0d01bde3a6ce1e499099d051d3fb54111537cf9c)
- Merge pull request #134 from MSOpenTech/orientation_change [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/02c67a20d9f6c59388a1be8f71fa568358f984b2)
- Add support for orientation change from #62 and #97 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/51216ff3dd0375a694eb4f9352bb78046ae47505)
- Resolve merge conflicts and address feedback for #62 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/cabb67e6425e23366ebed47b58ddbcd93c6925ee)
- fixing issues for windows platform [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d68cdce6f920c3db0bf7a1345f60a6fdabedde95)
- Merge pull request #132 from MSOpenTech/windows_barcode_format_fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8b5739fc692ab30e807feb7530d618ec7ba7ac8e)
- Correct scan result to expose format name instead of format code. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/dc84cac7844d3fdf64d3bfbdf0765930bc0a6b9e)
- Add information about windows 10 support to README [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b25d71061693f716658f0769c9be789b58b2b65e)
- Removes intermediate winmd component from Win10 implementation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/521d427ed418d2288a1b31a11398b99f54fb1f5d)
- Add Windows 10 Support [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9c2da016e4ddbd973a596568c750b7bb48ea4409)
- Merge pull request #131 from MSOpenTech/backbutton-fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9dec84ced0050f7f7221bb49d0fad4f39ccb819b)
- Improve native look and feel for windows platform [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/23b4343483104b21ce501f481e0001827f7f994b)
- Merge pull request #128 from MSOpenTech/autofocus-fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4606ac6d2465e83742d436cf351902f2f09854b7)
- Rework autofocus logic for windows. This fixes #41 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a905251130a5a956d28ed849bef69a3d6df6dee9)
- Merge pull request #127 from timwindsor/barcodeEncodeBB [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f4d5d5c638085169c10d54dde887b6203332a6ec)
- Merge branch 'master' into barcodeEncodeBB [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d9f47951442cefe939a61d1f9c5ee72316d82863)
- comments [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2219f194a6c4b5d87a1fb7d5bec2e60b91f41c07)
- added data type check, and uri prepending for encode method [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/****************************************)
- error checking, data validation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d5e84c9f09480a0f5f2782e206c61b0cf92c8c37)
- qrcode encode working [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2830a6d85c8be6f07aa503931c702e5f7444c879)
- encode method or blackberry [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c662c85c9db8b43bc259e966a40203b74b8552f5)
- Use maven reference to android-support-v4 library [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fb0a4f80092ab173366c98c6a1555bbed0ad864f)
- bump [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/df1210276412c72d428d13d382141a200e811986)
- fixes #162 button bar not working in ios8 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1f51bb9561c92be3abf676dd56b82c86f207b204)
- #15 Android M (6) compatibility [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/82bd97ef65b4b609a8d7a8be5a8068a5961b7cac)
- #15 Android M (6) compatibility [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f028ec469a60031e31033fc2d44fd69358d95be0)
- 4.1.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6b5f7fb7626f24b45fb643dcdd841e3fa0e5361f)
- updated to version 4.1.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6452c9802f0e0a4b686e5b94f043cc23d2380acd)
- Ignore linting [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/64845f4cf4726f1b50610463daa703b7a3a7f649)
- Removing custom strings [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/607efeb158f12e651e8555eec9abdd8d9480010a)
- Merge pull request #78 from timwindsor/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/940e98babdcc27e8dea6120a42412a3b8161e9ca)
- Merge remote-tracking branch 'phonegap/master' [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/28174b118447b86bdf4acebbc24913775db3fc7c)
- revert obsolete workaround. Fixes delayed error. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b8f2fa5c4efcc72732ebadb8bb304cc46465e93e)
- Parses result so it's consistent with other platforms. Removes obsolete, unused file. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/0632e345a63b22e4a52b250cac427f9649ab9858)
- #13 Cordova-iOS 4 support [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/de5700cc233cc845453bb98356e7b2cce67b6d92)
- #11 PDF417 support on Android. Version bump. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/009175b0ac39f8eea71c928708af97226a636d44)
- #11 PDF417 support on Android [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e413345fa765c61d46c8c5437eb08d754b9c4dd1)
- updated version in plugin.xml [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/74f33d5d14107915c357753684e9276eb37a3f66)
- 4.0.2 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/07322cb29310ad1f5b19f43472de8b86c558d19f)
- Merge pull request #66 from olimortimer/patch-1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/346472ff22b94f94cef25dea9f6f7eb851974c43)
- Merge pull request #52 from nlsdg/patch-1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/001a1be747183e5b727d367236ae477dc9b49613)
- Fixed iPhone / iPod scanning issue [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c92bc8ac49e72a064093e83da4597aba2e68dab7)
- Add BlackBerry namespace [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7175796244403b9b3de42ac5c1df2ca576606678)
- Update README.md [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/49571fbb7b79742481c230daa7b146cc7b2d9e7c)
- 4.0.1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f72bc48f7508c84a52a2cbe9e31c31408063def9)
- #48 fixed version in plugin.xml [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7b7c4c6d8751005a0ac762d29f179244dbe68866)
- 4.0.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b005d77852b22073c1b618ddec0d713453f5b69a)
- Merge pull request #32 from timwindsor/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7bbc73c82eb3dfb41c285103771d11451a1ae666)
- Adds blackberry10 to package.json [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/0ecd01e1104b4a89efee38ec598f89a6a1b51cd8)
- Merge pull request #1 from timwindsor/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/30196b3dfc2ef0f36c30253f6765cfbde283127b)
- Completes merge of BB10 implementation into phonegap API [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/52b5adb8d40e6116c20df2416cf1a78ec24f4b7e)
- Unifying API for BlackBerry10 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/02c5ba86d5752df841359ba42df32db0b74ff10b)
- Restore location of BlackBerry repo [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8cf9df1c65d047d61b3dc6a59c30f1dbda4f7f54)
- Restore location of iOS and Android repositories [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d801b7687dcb7b02ce7114e166d0cddf82adf12f)
- Merge in BlackBerry 10 support [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6043674b179295489936ba81b8fd5ff1ff2fdfa8)
- indentation fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/cce09e379351065a303ad9af93dd463fc049da53)
- stopRead error handling [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/66e572e3eb3f0febfa52b5ac2b334bfde11f2557)
- Merges in BlackBerry 10 support [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7b320e1dfe3216937dc0bb77a4ba65d790169bf9)
- Android 'encode' crashes [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1feb36d34313077c2dd145c59d9009088542d347)
- bump [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c484e0a754d67e2136ade27b9a3e4a4d115253e6)
- Android 'encode' crashes [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3632edc0cd0aa85b6a04d3f6671005a6215f6a82)
- Ugh, AB build errors because of lint config [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/31a7ebfcc2159e5c5075668bba508e67bd00db30)
- Ugh, AB build errors because of lint config [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c5cf295c7207ea3433f21b7319f1e29d538911a2)
- V3.1.2 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/70ca00be45a675b1b1d6963ad6f72430e579788e)
- Merge pull request #7 from mlaponder/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3d96f565e450f50fe0d36fb6d119c4e0d0268147)
- Update version to 3.1.1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/405c00b616a41dd6041c5daf359795dd7e4831a4)
- 3.1.1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b58323f54c72c0e93089d1e1fb81592a708309b4)
- Merge pull request #6 from mlaponder/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9d12d19f9af5ae9379b8c11b658079e234b39d9d)
- Fix incorrect uri for wp8 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5f1fb8865264576b8be8b6bdce7a4e34d40ee8ce)
- 3.1.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/78cc3fbcdf6f21e35614578409494d767193786b)
- updated to version 3.1.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4647c48c2ee683437f36589a3ad27868698840e7)
- Merge pull request #4 from komola/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/41c6e8c475b5028883478f22ed6694502e1b6b9d)
- fixed issue with readme [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5fbee99fc7bf561f99f7fcbb90ad8b41669797a3)
- added travis-ci support [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/616a2700f60e3789505737330bc6446acc42c7c9)
- fixed tests, made compatible with cordova-test-framework and paramedic [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/652d50967a8754a623e3a9de04b8b9565b4aa06d)
- Merge pull request #5 from stevengill/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b17891c002a04dc31c24e66ca5a50f31254d0e98)
- updated ID to phonegap-plugin-barcodescanner, V3.0.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/50d3f0a23bb1313d128c00d9634f8ef6f4d7cc3d)
- removed cordova.define from js [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/729f2caddd96041dc671188cae95648927fcb860)
- Merge pull request #1 from trongrg/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/262ef2b03b906043c05ca4f2283300eaa0fdff8c)
- Updated src/android/com.google.zxing.client.android.captureactivity.jar [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fa13720cf7cf664f6cc7a6d5721038d4f0e92ad1)
- Remove unnecessary retain calls [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5fce7a4d78c767e2e32e1d6173743c138e230406)
- [#4] Android UI doesn't support Portrait and is small - support for tablets with default landscape orientation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/408adbf364c4336901d017d5176d27721271a439)
- [#4] Android UI doesn't support Portrait and is small - support for tablets with default landscape orientation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f412fbc0db37cba9d96df3b6502715150794a70d)
- [#4] Android UI doesn't support Portrait and is small - support for 320x240 screens [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6c120b984c2adb86cfd8c2cddc55cf2719e17731)
- [#4] Android UI doesn't support Portrait and is small - portrait scanning of non-QR codes didn't work [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e197d222cd6b6c879eb0fed79316ee960674736f)
- [#4] Android UI doesn't support Portrait and is small - support Android projects which have not set targetSDK (crashed without it) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f74da676341aaa9a9cfbcb23440408e35449fbd2)
- [#4] Android UI doesn't support Portrait and is small [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a250540bec7b179820af584f3b714cc7920b1eda)
- Restore MultiFormat Reader [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/bd459f4b9c5aea7e7d20b7e7aaf7d61847ab48bb)
- Merge pull request #1 from MSOpenTech/2.1-windows [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4e5af0ca1b782fe4c54f9f849495d5678edc95b8)
- Include winmd source as subproject instead of prebuilt winmd [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/21083d4974e471e9233f66ed8b84f53df74b6eea)
- Merge pull request #2 from MSOpenTech/2.0-wp8 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/47aa58873097ad763c932c2fa2d7e0e0aa442ae5)
- Check if the last n scans returned the same result  and only then accept the code [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1d264dae30895a3edb07938c95c9902ef0ad69c3)
- windows: fixes "class not registered" error [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/96284b34430dd8824c517a24eef8fd23046b6de2)
- #616 Use single proxy for both windows8 and windows [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/eabae9e10be2e21f8c5f917748bd769c9fe11b43)
- #616 Adds support for windows platform [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/aa80367059e1a6c68a0947d56a7f6d63e896177b)
- Windows Phone 8.0 improvements and bugfixes: [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d0b2b27a77e02af5afec0cfd6c3566d1a3f65d62)
- v2.2.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c74e37a40cbf5ecbec5be8f6677e3bf01f0920bf)
- added support for adding config parameter which is passed to barcode scanner activity [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7773e4dae0afbe85ee50e3681df9ffbe7611a04e)
- Restrict to EAN13 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f70288d7b27cbe96d134db68c5bf517f85ce8e92)
- v2.1.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7b3302282215b4d41e1168cbef8af9a3abf122a6)
- Played beep sound after scan succeeded. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b1bb4fa2fad9d27bf5d25b8aa2f7872c92ffcf2b)
- iOS update encode method return format [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f04a2a0e4c8bc9c09d76a725ee771423e042837e)
- Add QR code encoder implementation for IOS [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8b10b8ae33fb672018fa9230079d8c6f096c4b13)
- fixes #162 button bar not working in ios8 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a1f216d4413596ca18251c3fe543f862acf972a5)
- fixed size to int conversion and cordova 3.6 deprecation warnings for iOS [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c2dcd871d25b480dfaf65b2eab878bc5a4775746)
- Removed ARC code [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/22a23b171a89322709bb1020fbdc02e90a079c21)
- Version bump [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/30236cda293f6e4de564e3adfc7fb847e1dbc0ba)
- Merge pull request #9 from galexandrov/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/35ad6da69b10f8107ad90491b4afb25ca9f5d971)
- Remove unnecessary retain calls [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a44f7403649e7727b2b65a72585e631d703386e1)
- up version to 2.0.1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fb600c1710ff2f786a1c3a31cd135d69846817fe)
- [#4] Android UI doesn't support Portrait and is small - support for tablets with default landscape orientation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/044de265661e17abb0aefcdf8230d9e357b6cfc0)
- [#4] Android UI doesn't support Portrait and is small - support for tablets with default landscape orientation [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d0aa7d75ee35fc951d66b3b094deba00ef17db97)
- [#4] Android UI doesn't support Portrait and is small - support for 320x240 screens [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7e60c7c1e830113b34673784ce0764ae450e7dfd)
- Support browser. encode is NOT ready yet. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/116a3bb4e797b75292867900b626b8f2bae10fb9)
- [#4] Android UI doesn't support Portrait and is small - portrait scanning of non-QR codes didn't work [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fe7746456defcb7f6d16ce4b023712c515bdb748)
- [#4] Android UI doesn't support Portrait and is small - support Android projects which have not set targetSDK (crashed without it) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/20799f14ade1e2a1399428377fdea2f608c84b49)
- [#4] Android UI doesn't support Portrait and is small [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b163ab694f1e49ae1667a2e4635f43a1bd6de74d)
- Documented new features [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8b9d6a5835b5c1a7f487b9ba38a5cb4e4d323dd9)
- Merge pull request #2 from EddyVerbruggen/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5f37fb12c6081809aa237b7896a5e7b26b32eeca)
- Added option to select choose to show a 'flip camera' button (default: false) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9f194201327ed4cbe0eee21f67ed38496e2a5003)
- Added option to select frontcamera via JS api [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/80a828516c1512964a85e0f2fdd44e09d206e418)
- added repo + issue tags [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5268a02fd7f6c96aa2c2bc022ab777e89ae6cc23)
- Upped version to 2.0.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c3090dcf5347c1cc10caaeff225bb2c0a0deeede)
- [readme] fix encode example code [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3a0978076fab0c5180d4c2989a4a3844a980fbe2)
- Enabled ITF support [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/740aa3a33b55c5d51ae07dbb8bf40620bdd7b7b6)
- Added support for front face camera.  Signed-off-by: Emil Marashliev <<EMAIL>> [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a209c6d396a53c6304582f89f59300066ad56149)
- Updated link to Cordova plugin spec to fix 404 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5a7aa5c29d98679513209247e5c65559cd2e275d)
- Updated README links to the original iOS and Android repos to fix 404 errors [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a8c2a2609399cee2799c873863257b9ce870ac80)
- avoid calling other apps activity [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/84af3e9f72bd12f172853f6c091b08eb556572cf)
- [plugin.xml] updated description [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f766ba2536b93cae56875060bf25af55cf80c178)
- [plugin-xml] upped version to 1.2.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/552974245e156960bc48b991520f82d696d125b5)
- Adds documentation for Windows Phone 8 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/602dad12e506ec0f8601d144d1fde716d021814f)
- Adds support for Windows phone 8.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b94b4f67c417d431094da193f2a40da7876b498b)
- Adds documentation for Windows8. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/74f78db8ac7fb6b5aec25584a5a9518286a1de9d)
- Adds Windows8 implementation (scan method only) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3a7d7610ab8b2c0c94feecbaff854c564018ba9d)
- [1.1.0] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2a2fc654bcc2a68598d04d417a84c5a64fe3fb24)
- [js] plugin.xml asset backwards compat [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a6ed222163792190ce5b4b684b6ad746bfd7662d)
- Compiled jar [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c93d7569c4fb88baa8a2546e5c278249f7961887)
- Added a flip-camera button for Android - missing icon [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/332f12fe9c79a7a8210914f747ce8353368c1266)
- Added a flip-camera button for Android - missing icon [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/48be871057bac642724de63c1c3707c99f9976ab)
- Added a flip-camera button for Android [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1a2b8c6126f9d61ff229bbee2f9f8109a78e806d)
- Front camera scanning for iOS ([#108]) deserves a version bump [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/98f158aa4dde990c6d2bc89ff418039c77b6e15c)
- Added support for front face camera.  Signed-off-by: Emil Marashliev <<EMAIL>> [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d3c8ad2df92743bc8fd038ec37d89e0bce2053f9)
- avoid calling other apps activity [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ef1155d09d53856b4d8dfd6326c734e05e702885)
- [plugin.xml] updated description [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/cfa698d93773fa7d737fc1adf02d6a2e46538d09)
- Change plugin.xml to use framework element [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/619f6984c3db640c14fa2505d06b659913018137)
- Change barcode scanner to work similar to the iOS and Android versions. In plugin.xml added reference to ZXing.Net so it is refered as plugin is installed. Similar to other platforms' versions, there is a focus area - only this area is scanned for barcodes. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3a377ef883d0e5db138ed40f5e5f6838d1407f3a)
- removed unused reference [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/05a5a17eceed4e377faa9674b2f3564ca37b422e)
- [1.2.0] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/53ad095e86434adc7694b8a26bdf16d6c381238d)
- ignore Intellij module files [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1654970ac5b5b89c4e5008d71f93a755df71add0)
- Updated Plugin, refactored some library parts, added description to read me [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/719f19d3f003a19313d29f81c8d668030980db7e)
- added windows phone version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/039d1d0ea45d75902c748ba9f7f115a9b94887c7)
- window.plugins is not defined anymore, replaced it with cordova.plugins [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7d831d83ed3967ce438551bed1b8a86ec2f7fb84)
- Add missing iOS framework "QuartzCore" to plugin.xml [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ec4de624ae9f68a59557f21a1a5a4e18d3cfa425)
- [plugin.xml] corrected supported cordova versions [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/24acf29896fa39578238a0269c9b1a3d643c6a3b)
- delete plugin.xml.generate.php [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a310fda2d7d55e650a65b9d0327eff87356c0818)
- Use <js-module> in favour of <asset> for the JS [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8bf3cce0737f7435b979b55433c87657dab792a1)
- [readme] fixed encode syntax typo [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2d8701634f4d761f3c3cc961d8eb337b44de79e5)
- [android][0.7.2] remove busted menu items + fixes #11 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7880bf7b5c4a619acaff8e8b905e0710eb3f41b5)
- [0.7.1] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/12a31b76b015d3218874437a37d9a78fe710b161)
- [README] fix typo [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a2857ba5f8ed2a4ef7277fae8dc108239a2f15ee)
- Merge branch 'ios_warnings' of https://github.com/agrieve/BarcodeScanner into agrive-ios_warnings [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f046f651ceb06e35a6867e3e3df0cbb2c58a0f13)
- Use <js-module> in favour of <asset> for the JS. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6ad6dafc1169d62871663d013d49312df64acb2b)
- Fix plugin for iOS on Cordova 3.0 by disabling ARC and using newer plugin signature. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/14d63e35ac9d1333f178cb3ae95a88b530d73538)
- Fix some compile warnings on iOS [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d901977064078efec335f3309c30bc8038372447)
- [0.7.0] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7094cc6eda5ef8a225c550e463bc49907c36ac40)
- Cordova 2.8 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fc3dcd56d1558415043aaa538c724426b6caa726)
- minor [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/50a2f8bf5236e643635caf8cabba5e0d2df8ba69)
- Merge remote-tracking branch 'upstream/master' [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8966e5207fdf506f5c94a9dee9f9e25b847aa5c7)
- ident [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8fa142ca9d1d400ca91d651865b6eb5d9f79d204)
- Encode object access fix. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6fd905a8e60a0341fc4a4453f594222eb7cd0b8d)
- 3.0 fixed README.md [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/32880fc1fa78dd0911a66c86895303d7479a2be5)
- typo [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/903b02f142534057cb02d79772d874c37fdbd553)
- Fix for https://issues.apache.org/jira/browse/CB-4379 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6fdac151eb089043c6147e6822e46ebea4604375)
- Changes for crodova 3.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c84bf0e66d0ac14b44283261a2a3c9813e4750db)
- PG 3.0 changes [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e728d331554837a3c905e569a579f290125d4717)
- [readme] correct ios supported formats [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1242c551424e31270035929c024b4d7de55ae9ef)
- [readme] plugman not pluginstall [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3b1ad506b2a4e387405f62263ec3b5dd550775bc)
- [0.6.0] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fab895ab3002da39e3fdaf0f11eee2a3f8ad4e9d)
- app_name fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c5c60c88568b78898bd08a042b5e0087053cc9b1)
- Update README.md [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5b01acb999fcc9f5316212a8887fcd29ebbf78a5)
- minor refactoring ;-) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/db30964d5113d4ac4a63fe8997165d07e7bb9e20)
- plugin spec. update [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6e29489982aae3f761195647b18c8c1ccd2ff850)
- Update to 4.3.1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8c50a121e87425ffcf4c09f7c4a08669c7bb7a71)
- [plugin.xml] update license tag [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e2cef45cb8b2a85f6979eee17dc96a6ec55d01e5)
- [plugin.xml] license tag [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c24882311fc880f4881dc02bcb14f9816d1808cc)
- Merge branch 'master' of https://github.com/Eccenux/BarcodeScanner [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5833a1fa9e5adcd1f823e86b76a28800af1980ae)
- app_name fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9ed602fdacd4da7e8b083946089a4d04b51a46fd)
- Update README.md [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/06b05fc6c98fae2b7c45502d6bd1ee315a339265)
- minor refactoring ;-) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2f86c728e5953a35efe236e293a0eca48353be2a)
- plugin spec. update [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/01a74e4c6c198750dc9149c8e90bd363dc0670ff)
- [0.5.4] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/18f2f7a9fe67ac42a1deafb11992050009df2ea2)
- Update to 4.3.1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/cf2be813cb3fb28b61fd2fedf4803ed70d2d5c16)
- Merge branch 'master' of github.com:wildabeast/BarcodeScanner [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/10d66a4e67221957a8c5b02e7cd05b25539c4970)
- [readme] reference plugman not pluginstall [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/27d6d653fc51e6cb2eca1a7e610934d7f0a140a4)
- [plugin-xml] added engines [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4efcc86a24144c40fd60e372f0a4a6d02ce8578a)
- [plugin-xml] Add description [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4d38047b41fe4bed48b63249c1ce12b85f252089)
- [readme] reference plugman not pluginstall [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/af21cd20f6c14bbd803e83f904573c9af9de225d)
- [plugin-xml] added engines [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e38147f27f661137240b6ff673a08ca6e10c9c6d)
- [0.5.3] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b45ce29fec8f2c062661670a5651684234545b12)
- [spec] update specs to cordova.require/define [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/77d4a3dda27a10ec956c1e9e5f3281c76c54a25a)
- [plugman] plugman don't like spec/config.xml [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6f4e09be4f2e0fe17a3372bec5cb2309372395d7)
- [plugin-xml] Add description [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b0747cadcef11fd1fd90fd94d0ffc8729e8256d3)
- [0.5.3] upped version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3332885acd3072085dbbee8bc6389d83ff18dfcb)
- [spec] update specs to cordova.require/define [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2331218b5a597d1c5888e66461a2d4bd7358a8ae)
- [plugman] plugman don't like spec/config.xml [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7ec3d477c208d7476eede4959f8675c6be1cc5f3)
- [js] cordova.require instead of PhoneGap.require [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3ab188539c8529d4fa79497abb84f509d1411318)
- [readme] formatting [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/d9f9f2dc9bf5ede06df8b5d9f407456b8c938582)
- [js] cordova.require instead of PhoneGap.require [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ea1586b0ffbf9a6774bcedab1f988c4f3d8c7fcf)
- [readme] formatting [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/10f8e6b1164da3e108f3e8e52ca8953e7980c0c4)
- [spec] FIXED IT [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6cf71f0a3ea1bbfa06806ba1d447a96321eec02f)
- [spec] 2.5.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6d95b33987c1ee5d903ee51dd1ea496d8d786ae5)
- [spec] removing a maybe bunk spec [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ec5e1dc843cf6f9c4090d597efc03a33a50373b0)
- [spec] removing filsystem init [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/507c726215846a56c30cb85d309588166ab95ab9)
- [spec] more specs [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/fceafa235a14a3f4d783c02e9529a1f96ecd64c3)
- [spec] formatting for medic [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/666387e132148cf332d5dd7cb3e379a735bca23d)
- [spec] app id [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/eb557c48279ebcc5147e474e73c4f374c1b9ec63)
- [spec] Fixed spec [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/33b2e914a4420024fde9cb2206ac2f9e32bf7931)
- [spec] app id, title [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/657ec4512268e56d5478bcc93c8a018da026dd32)
- [spec] fixing spec app [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/52ce70de60d6ad83b05e9639777cf8fa1f9126cd)
- [spec] add barcodescanner.js [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/ab95b1342d0ccabcd006f5691c39875c54c68c19)
- [spec] Initial version of spec [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7e7c39dda69bd64359a8963b3cb5aa679b6047c9)
- Do not copy the scanned barcode to the clipboard. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5c46e8d93f0b711963e88248f71a7be6babe6b75)
- readme fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3bc65ebbaa3f0e7f96948b7a3484b9e4e9b3ea7b)
- Update for Cordova/PhoneGap 2.0 plugins (http://simonmacdonald.blogspot.ca/2012/08/so-you-wanna-write-phonegap-200-android.html) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5d6f0d52f9e495aa34f9d6fec9c77743b2ea6c9a)
- fix for a java ref [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f26cde76f54f3377d2e385c9fa97ebc65886d50c)
- full paths for android [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/5d452348dabed181cb3a8b970d94c9deead44be3)
- fully qualified paths [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4d8646e26d9ac7008b6b1ba75c24de40960b6ef8)
- Merge pull request #3 from filmaj/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b1f643f6f48c0dd7909c1e45da3807b35b2769f5)
- fix for a java ref [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9c533da17ed0554394fee2aa98f79b4caf56a574)
- full paths for android [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f568cc6d3e63837ecaa057b666d1913cad2b0e43)
- fully qualified paths [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/215e077510b26981007500af27e503cc3f6211ae)
- [spec] FIXED IT [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/cc0f7d82a1723616033be2b25ad554fc614b1fe6)
- [spec] 2.5.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f94c6129ae6c336660a240458b3371a1c501dfa9)
- [spec] removing a maybe bunk spec [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/4579d40dc05964521f1e97026810d439e8e7be58)
- [spec] removing filsystem init [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/40849d52d645ac5bda784ce44de2b18bb1cfac61)
- [spec] more specs [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/2f7d652587b234362cc86c5205fce9ac10814c13)
- [spec] formatting for medic [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c1d0b47929171a45df1bbe9ff095e128d4e7c74e)
- [spec] app id [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/381ee1c6ace4ff2919ea0d33cf770700f5e70c2f)
- [spec] Fixed spec [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/560cecc5b83c2257215398a9bee5ec12fdfea0b3)
- [spec] app id, title [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/a0373c766fb8d9fca0c01a9b5dd1c0bd3c1161a7)
- [spec] fixing spec app [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f9e3d0cffa88a09941aed82f515edeccd8773ad7)
- [spec] add barcodescanner.js [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/604ebdf14485736b7d543812017812a40cada2b6)
- [spec] Initial version of spec [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7c47973f78d6caad4b3c92709d0d82d59a4fb163)
- update BarcodeScanner plugin for 2.5.0+ [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1661cc4387e1e4110f88b4ef561453030d795182)
- Merge pull request #14 from jwark/do_not_copy_to_clipboard [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/15b5cb9442e1c43ce306fb91f8e2d07e42dd1244)
- update BarcodeScanner plugin for 2.5.0+ [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/58e773af70c59d04012d51ded8ba408037142f95)
- Do not copy the scanned barcode to the clipboard. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1d8ee5ca8d2e2878b82e4db95d7465c15c6fa5f4)
- Merge pull request #7 from JaysonRaymond/master [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3c73e21796118c3a97ba944e67d6a06c80ca08b4)
- [iOS][2.3.0] add plugin to config.xml for 2.3.0+ + upped version to 0.3.0 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/bb3812c0cb7b525065101c251b211e320d76c45a)
- readme fix [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c7158af58ee9aeb0e325ae733c4bbfd1db51e563)
- Update for Cordova/PhoneGap 2.0 plugins (http://simonmacdonald.blogspot.ca/2012/08/so-you-wanna-write-phonegap-200-android.html) [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3f166162a05c79782cbcf162154f16b94efa2609)
- [0.2.2] updated version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/029cb4de7d30953811690dba6939b88d9a9ceca7)
- [ios] Fixes AVCaptureSession leak, thanks @markus-olsson [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/1e2535ad010e94ee0319f727e0f444035b02742d)
- [android] syntax error, missing else [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/695f524777f9b0ea49dcfe3e980a5053da2e3e4a)
- [0.2.1][ios] lock scanner to portrait [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/084594d86c7cafb98f0985101629fa910141fa6a)
- [0.2.0] updated version [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/87ffa13f2c5e63e7388ef37bacf9b02ead8fc0d9)
- [ios] lock scanner orientation to portrait [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/413b0236978186d5c2e3b2675c72997867bcee6a)
- [0.1.2] version update [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/566c1f166a27108c995fd77d5fa20454c7311196)
- [ios] Integrated shazron's iOS6 orientation updates [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e0f7b9a33e647b9763934c09259be01edba88d5c)
- [version] updated to 0.1.1 [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/f1fb499e42f31e06d1805dcdf40bd3edcb0fd384)
- [android] Removed app_name string resource [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/b145a21012f1b997e0060f8d1c0e6fd25add1cca)
- [readme] corrected supported types for iOS [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/7119054ce63bffdb3bfb77d97aec12307536d795)
- [readme] [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9211290b7656697f7b7a87a05b77286d1f85b2c8)
- [readme] [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/89ed3727372815966fd83b2b925b51d22a18b001)
- [0.1.0] version updated [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/3e3093969f0544ede202e6646a655da6fb22f1ac)
- [android] Added translated resource files [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/47a8bdd29c277e69ab1750b94b3c4ac5c01ba19b)
- [android] Modified zxing client to access resources from main package [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/9cff772bfc4f404337830a974a9593883cb81d23)
- [android][plugin-xml] Added resource files [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/e3505aea3a65a967e7bb710f77f94d05fc556409)
- [android] Added Zxing Library project [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/8421719edb1f7005b3a2a46a24c62d2d69479d7a)
- [gitignore] [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/c432f127c593791aedc3008a83f7a1a818568700)
- [readme] license [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/274b122932517ccea144d5e279c38beb4ad991aa)
- [plugin-xml] fixed up plugin.xml [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/097548504dd6c192e26256e582882e6b5f12daec)
- [ios] removed CORDOVA_FRAMEWORK ref [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/cb2e830ee5223076c9f126d80e07ef614ca3cff1)
- Initial commit. Zeppelin rules. [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/92197289049bfd4aab63b6467dddb93449a8a7ef)
- Initial commit [view commit](http://github.com/phonegap/phonegap-plugin-barcodescanner/commit/6de30878ff7ada1b43a01d1037dcee6e419a8050)

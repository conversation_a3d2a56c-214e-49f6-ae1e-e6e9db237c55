"use strict";

/**
 * Module dependencies.
 */
// eslint-disable-next-line node/no-deprecated-api
var _require = require('url'),
    parse = _require.parse;

var _require2 = require('cookiejar'),
    CookieJar = _require2.CookieJar;

var _require3 = require('cookiejar'),
    CookieAccessInfo = _require3.CookieAccessInfo;

var methods = require('methods');

var request = require('../..');

var AgentBase = require('../agent-base');
/**
 * Expose `Agent`.
 */


module.exports = Agent;
/**
 * Initialize a new `Agent`.
 *
 * @api public
 */

function Agent(options) {
  if (!(this instanceof Agent)) {
    return new Agent(options);
  }

  AgentBase.call(this);
  this.jar = new CookieJar();

  if (options) {
    if (options.ca) {
      this.ca(options.ca);
    }

    if (options.key) {
      this.key(options.key);
    }

    if (options.pfx) {
      this.pfx(options.pfx);
    }

    if (options.cert) {
      this.cert(options.cert);
    }

    if (options.rejectUnauthorized === false) {
      this.disableTLSCerts();
    }
  }
}

Agent.prototype = Object.create(AgentBase.prototype);
/**
 * Save the cookies in the given `res` to
 * the agent's cookie jar for persistence.
 *
 * @param {Response} res
 * @api private
 */

Agent.prototype._saveCookies = function (res) {
  var cookies = res.headers['set-cookie'];
  if (cookies) this.jar.setCookies(cookies);
};
/**
 * Attach cookies when available to the given `req`.
 *
 * @param {Request} req
 * @api private
 */


Agent.prototype._attachCookies = function (req) {
  var url = parse(req.url);
  var access = new CookieAccessInfo(url.hostname, url.pathname, url.protocol === 'https:');
  var cookies = this.jar.getCookies(access).toValueString();
  req.cookies = cookies;
};

methods.forEach(function (name) {
  var method = name.toUpperCase();

  Agent.prototype[name] = function (url, fn) {
    var req = new request.Request(method, url);
    req.on('response', this._saveCookies.bind(this));
    req.on('redirect', this._saveCookies.bind(this));
    req.on('redirect', this._attachCookies.bind(this, req));

    this._setDefaults(req);

    this._attachCookies(req);

    if (fn) {
      req.end(fn);
    }

    return req;
  };
});
Agent.prototype.del = Agent.prototype.delete;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9ub2RlL2FnZW50LmpzIl0sIm5hbWVzIjpbInJlcXVpcmUiLCJwYXJzZSIsIkNvb2tpZUphciIsIkNvb2tpZUFjY2Vzc0luZm8iLCJtZXRob2RzIiwicmVxdWVzdCIsIkFnZW50QmFzZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJBZ2VudCIsIm9wdGlvbnMiLCJjYWxsIiwiamFyIiwiY2EiLCJrZXkiLCJwZngiLCJjZXJ0IiwicmVqZWN0VW5hdXRob3JpemVkIiwiZGlzYWJsZVRMU0NlcnRzIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiY3JlYXRlIiwiX3NhdmVDb29raWVzIiwicmVzIiwiY29va2llcyIsImhlYWRlcnMiLCJzZXRDb29raWVzIiwiX2F0dGFjaENvb2tpZXMiLCJyZXEiLCJ1cmwiLCJhY2Nlc3MiLCJob3N0bmFtZSIsInBhdGhuYW1lIiwicHJvdG9jb2wiLCJnZXRDb29raWVzIiwidG9WYWx1ZVN0cmluZyIsImZvckVhY2giLCJuYW1lIiwibWV0aG9kIiwidG9VcHBlckNhc2UiLCJmbiIsIlJlcXVlc3QiLCJvbiIsImJpbmQiLCJfc2V0RGVmYXVsdHMiLCJlbmQiLCJkZWwiLCJkZWxldGUiXSwibWFwcGluZ3MiOiI7O0FBQUE7OztBQUlBO2VBQ2tCQSxPQUFPLENBQUMsS0FBRCxDO0lBQWpCQyxLLFlBQUFBLEs7O2dCQUNjRCxPQUFPLENBQUMsV0FBRCxDO0lBQXJCRSxTLGFBQUFBLFM7O2dCQUNxQkYsT0FBTyxDQUFDLFdBQUQsQztJQUE1QkcsZ0IsYUFBQUEsZ0I7O0FBQ1IsSUFBTUMsT0FBTyxHQUFHSixPQUFPLENBQUMsU0FBRCxDQUF2Qjs7QUFDQSxJQUFNSyxPQUFPLEdBQUdMLE9BQU8sQ0FBQyxPQUFELENBQXZCOztBQUNBLElBQU1NLFNBQVMsR0FBR04sT0FBTyxDQUFDLGVBQUQsQ0FBekI7QUFFQTs7Ozs7QUFJQU8sTUFBTSxDQUFDQyxPQUFQLEdBQWlCQyxLQUFqQjtBQUVBOzs7Ozs7QUFNQSxTQUFTQSxLQUFULENBQWVDLE9BQWYsRUFBd0I7QUFDdEIsTUFBSSxFQUFFLGdCQUFnQkQsS0FBbEIsQ0FBSixFQUE4QjtBQUM1QixXQUFPLElBQUlBLEtBQUosQ0FBVUMsT0FBVixDQUFQO0FBQ0Q7O0FBRURKLEVBQUFBLFNBQVMsQ0FBQ0ssSUFBVixDQUFlLElBQWY7QUFDQSxPQUFLQyxHQUFMLEdBQVcsSUFBSVYsU0FBSixFQUFYOztBQUVBLE1BQUlRLE9BQUosRUFBYTtBQUNYLFFBQUlBLE9BQU8sQ0FBQ0csRUFBWixFQUFnQjtBQUNkLFdBQUtBLEVBQUwsQ0FBUUgsT0FBTyxDQUFDRyxFQUFoQjtBQUNEOztBQUVELFFBQUlILE9BQU8sQ0FBQ0ksR0FBWixFQUFpQjtBQUNmLFdBQUtBLEdBQUwsQ0FBU0osT0FBTyxDQUFDSSxHQUFqQjtBQUNEOztBQUVELFFBQUlKLE9BQU8sQ0FBQ0ssR0FBWixFQUFpQjtBQUNmLFdBQUtBLEdBQUwsQ0FBU0wsT0FBTyxDQUFDSyxHQUFqQjtBQUNEOztBQUVELFFBQUlMLE9BQU8sQ0FBQ00sSUFBWixFQUFrQjtBQUNoQixXQUFLQSxJQUFMLENBQVVOLE9BQU8sQ0FBQ00sSUFBbEI7QUFDRDs7QUFFRCxRQUFJTixPQUFPLENBQUNPLGtCQUFSLEtBQStCLEtBQW5DLEVBQTBDO0FBQ3hDLFdBQUtDLGVBQUw7QUFDRDtBQUNGO0FBQ0Y7O0FBRURULEtBQUssQ0FBQ1UsU0FBTixHQUFrQkMsTUFBTSxDQUFDQyxNQUFQLENBQWNmLFNBQVMsQ0FBQ2EsU0FBeEIsQ0FBbEI7QUFFQTs7Ozs7Ozs7QUFRQVYsS0FBSyxDQUFDVSxTQUFOLENBQWdCRyxZQUFoQixHQUErQixVQUFTQyxHQUFULEVBQWM7QUFDM0MsTUFBTUMsT0FBTyxHQUFHRCxHQUFHLENBQUNFLE9BQUosQ0FBWSxZQUFaLENBQWhCO0FBQ0EsTUFBSUQsT0FBSixFQUFhLEtBQUtaLEdBQUwsQ0FBU2MsVUFBVCxDQUFvQkYsT0FBcEI7QUFDZCxDQUhEO0FBS0E7Ozs7Ozs7O0FBT0FmLEtBQUssQ0FBQ1UsU0FBTixDQUFnQlEsY0FBaEIsR0FBaUMsVUFBU0MsR0FBVCxFQUFjO0FBQzdDLE1BQU1DLEdBQUcsR0FBRzVCLEtBQUssQ0FBQzJCLEdBQUcsQ0FBQ0MsR0FBTCxDQUFqQjtBQUNBLE1BQU1DLE1BQU0sR0FBRyxJQUFJM0IsZ0JBQUosQ0FDYjBCLEdBQUcsQ0FBQ0UsUUFEUyxFQUViRixHQUFHLENBQUNHLFFBRlMsRUFHYkgsR0FBRyxDQUFDSSxRQUFKLEtBQWlCLFFBSEosQ0FBZjtBQUtBLE1BQU1ULE9BQU8sR0FBRyxLQUFLWixHQUFMLENBQVNzQixVQUFULENBQW9CSixNQUFwQixFQUE0QkssYUFBNUIsRUFBaEI7QUFDQVAsRUFBQUEsR0FBRyxDQUFDSixPQUFKLEdBQWNBLE9BQWQ7QUFDRCxDQVREOztBQVdBcEIsT0FBTyxDQUFDZ0MsT0FBUixDQUFnQixVQUFBQyxJQUFJLEVBQUk7QUFDdEIsTUFBTUMsTUFBTSxHQUFHRCxJQUFJLENBQUNFLFdBQUwsRUFBZjs7QUFDQTlCLEVBQUFBLEtBQUssQ0FBQ1UsU0FBTixDQUFnQmtCLElBQWhCLElBQXdCLFVBQVNSLEdBQVQsRUFBY1csRUFBZCxFQUFrQjtBQUN4QyxRQUFNWixHQUFHLEdBQUcsSUFBSXZCLE9BQU8sQ0FBQ29DLE9BQVosQ0FBb0JILE1BQXBCLEVBQTRCVCxHQUE1QixDQUFaO0FBRUFELElBQUFBLEdBQUcsQ0FBQ2MsRUFBSixDQUFPLFVBQVAsRUFBbUIsS0FBS3BCLFlBQUwsQ0FBa0JxQixJQUFsQixDQUF1QixJQUF2QixDQUFuQjtBQUNBZixJQUFBQSxHQUFHLENBQUNjLEVBQUosQ0FBTyxVQUFQLEVBQW1CLEtBQUtwQixZQUFMLENBQWtCcUIsSUFBbEIsQ0FBdUIsSUFBdkIsQ0FBbkI7QUFDQWYsSUFBQUEsR0FBRyxDQUFDYyxFQUFKLENBQU8sVUFBUCxFQUFtQixLQUFLZixjQUFMLENBQW9CZ0IsSUFBcEIsQ0FBeUIsSUFBekIsRUFBK0JmLEdBQS9CLENBQW5COztBQUNBLFNBQUtnQixZQUFMLENBQWtCaEIsR0FBbEI7O0FBQ0EsU0FBS0QsY0FBTCxDQUFvQkMsR0FBcEI7O0FBRUEsUUFBSVksRUFBSixFQUFRO0FBQ05aLE1BQUFBLEdBQUcsQ0FBQ2lCLEdBQUosQ0FBUUwsRUFBUjtBQUNEOztBQUVELFdBQU9aLEdBQVA7QUFDRCxHQWREO0FBZUQsQ0FqQkQ7QUFtQkFuQixLQUFLLENBQUNVLFNBQU4sQ0FBZ0IyQixHQUFoQixHQUFzQnJDLEtBQUssQ0FBQ1UsU0FBTixDQUFnQjRCLE1BQXRDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNb2R1bGUgZGVwZW5kZW5jaWVzLlxuICovXG5cbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBub2RlL25vLWRlcHJlY2F0ZWQtYXBpXG5jb25zdCB7IHBhcnNlIH0gPSByZXF1aXJlKCd1cmwnKTtcbmNvbnN0IHsgQ29va2llSmFyIH0gPSByZXF1aXJlKCdjb29raWVqYXInKTtcbmNvbnN0IHsgQ29va2llQWNjZXNzSW5mbyB9ID0gcmVxdWlyZSgnY29va2llamFyJyk7XG5jb25zdCBtZXRob2RzID0gcmVxdWlyZSgnbWV0aG9kcycpO1xuY29uc3QgcmVxdWVzdCA9IHJlcXVpcmUoJy4uLy4uJyk7XG5jb25zdCBBZ2VudEJhc2UgPSByZXF1aXJlKCcuLi9hZ2VudC1iYXNlJyk7XG5cbi8qKlxuICogRXhwb3NlIGBBZ2VudGAuXG4gKi9cblxubW9kdWxlLmV4cG9ydHMgPSBBZ2VudDtcblxuLyoqXG4gKiBJbml0aWFsaXplIGEgbmV3IGBBZ2VudGAuXG4gKlxuICogQGFwaSBwdWJsaWNcbiAqL1xuXG5mdW5jdGlvbiBBZ2VudChvcHRpb25zKSB7XG4gIGlmICghKHRoaXMgaW5zdGFuY2VvZiBBZ2VudCkpIHtcbiAgICByZXR1cm4gbmV3IEFnZW50KG9wdGlvbnMpO1xuICB9XG5cbiAgQWdlbnRCYXNlLmNhbGwodGhpcyk7XG4gIHRoaXMuamFyID0gbmV3IENvb2tpZUphcigpO1xuXG4gIGlmIChvcHRpb25zKSB7XG4gICAgaWYgKG9wdGlvbnMuY2EpIHtcbiAgICAgIHRoaXMuY2Eob3B0aW9ucy5jYSk7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMua2V5KSB7XG4gICAgICB0aGlzLmtleShvcHRpb25zLmtleSk7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMucGZ4KSB7XG4gICAgICB0aGlzLnBmeChvcHRpb25zLnBmeCk7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMuY2VydCkge1xuICAgICAgdGhpcy5jZXJ0KG9wdGlvbnMuY2VydCk7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMucmVqZWN0VW5hdXRob3JpemVkID09PSBmYWxzZSkge1xuICAgICAgdGhpcy5kaXNhYmxlVExTQ2VydHMoKTtcbiAgICB9XG4gIH1cbn1cblxuQWdlbnQucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShBZ2VudEJhc2UucHJvdG90eXBlKTtcblxuLyoqXG4gKiBTYXZlIHRoZSBjb29raWVzIGluIHRoZSBnaXZlbiBgcmVzYCB0b1xuICogdGhlIGFnZW50J3MgY29va2llIGphciBmb3IgcGVyc2lzdGVuY2UuXG4gKlxuICogQHBhcmFtIHtSZXNwb25zZX0gcmVzXG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5BZ2VudC5wcm90b3R5cGUuX3NhdmVDb29raWVzID0gZnVuY3Rpb24ocmVzKSB7XG4gIGNvbnN0IGNvb2tpZXMgPSByZXMuaGVhZGVyc1snc2V0LWNvb2tpZSddO1xuICBpZiAoY29va2llcykgdGhpcy5qYXIuc2V0Q29va2llcyhjb29raWVzKTtcbn07XG5cbi8qKlxuICogQXR0YWNoIGNvb2tpZXMgd2hlbiBhdmFpbGFibGUgdG8gdGhlIGdpdmVuIGByZXFgLlxuICpcbiAqIEBwYXJhbSB7UmVxdWVzdH0gcmVxXG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5BZ2VudC5wcm90b3R5cGUuX2F0dGFjaENvb2tpZXMgPSBmdW5jdGlvbihyZXEpIHtcbiAgY29uc3QgdXJsID0gcGFyc2UocmVxLnVybCk7XG4gIGNvbnN0IGFjY2VzcyA9IG5ldyBDb29raWVBY2Nlc3NJbmZvKFxuICAgIHVybC5ob3N0bmFtZSxcbiAgICB1cmwucGF0aG5hbWUsXG4gICAgdXJsLnByb3RvY29sID09PSAnaHR0cHM6J1xuICApO1xuICBjb25zdCBjb29raWVzID0gdGhpcy5qYXIuZ2V0Q29va2llcyhhY2Nlc3MpLnRvVmFsdWVTdHJpbmcoKTtcbiAgcmVxLmNvb2tpZXMgPSBjb29raWVzO1xufTtcblxubWV0aG9kcy5mb3JFYWNoKG5hbWUgPT4ge1xuICBjb25zdCBtZXRob2QgPSBuYW1lLnRvVXBwZXJDYXNlKCk7XG4gIEFnZW50LnByb3RvdHlwZVtuYW1lXSA9IGZ1bmN0aW9uKHVybCwgZm4pIHtcbiAgICBjb25zdCByZXEgPSBuZXcgcmVxdWVzdC5SZXF1ZXN0KG1ldGhvZCwgdXJsKTtcblxuICAgIHJlcS5vbigncmVzcG9uc2UnLCB0aGlzLl9zYXZlQ29va2llcy5iaW5kKHRoaXMpKTtcbiAgICByZXEub24oJ3JlZGlyZWN0JywgdGhpcy5fc2F2ZUNvb2tpZXMuYmluZCh0aGlzKSk7XG4gICAgcmVxLm9uKCdyZWRpcmVjdCcsIHRoaXMuX2F0dGFjaENvb2tpZXMuYmluZCh0aGlzLCByZXEpKTtcbiAgICB0aGlzLl9zZXREZWZhdWx0cyhyZXEpO1xuICAgIHRoaXMuX2F0dGFjaENvb2tpZXMocmVxKTtcblxuICAgIGlmIChmbikge1xuICAgICAgcmVxLmVuZChmbik7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlcTtcbiAgfTtcbn0pO1xuXG5BZ2VudC5wcm90b3R5cGUuZGVsID0gQWdlbnQucHJvdG90eXBlLmRlbGV0ZTtcbiJdfQ==
"use strict";

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

var Stream = require('stream');

var util = require('util');

var net = require('net');

var tls = require('tls'); // eslint-disable-next-line node/no-deprecated-api


var _require = require('url'),
    parse = _require.parse;

var semver = require('semver');

var http2;
if (semver.gte(process.version, 'v10.10.0')) http2 = require('http2');else throw new Error('superagent: this version of Node.js does not support http2');
var _http2$constants = http2.constants,
    HTTP2_HEADER_PATH = _http2$constants.HTTP2_HEADER_PATH,
    HTTP2_HEADER_STATUS = _http2$constants.HTTP2_HEADER_STATUS,
    HTTP2_HEADER_METHOD = _http2$constants.HTTP2_HEADER_METHOD,
    HTTP2_HEADER_AUTHORITY = _http2$constants.HTTP2_HEADER_AUTHORITY,
    HTTP2_HEADER_HOST = _http2$constants.HTTP2_HEADER_HOST,
    HTTP2_HEADER_SET_COOKIE = _http2$constants.HTTP2_HEADER_SET_COOKIE,
    NGHTTP2_CANCEL = _http2$constants.NGHTTP2_CANCEL;

function setProtocol(protocol) {
  return {
    request: function request(options) {
      return new Request(protocol, options);
    }
  };
}

function Request(protocol, options) {
  var _this = this;

  Stream.call(this);
  var defaultPort = protocol === 'https:' ? 443 : 80;
  var defaultHost = 'localhost';
  var port = options.port || defaultPort;
  var host = options.host || defaultHost;
  delete options.port;
  delete options.host;
  this.method = options.method;
  this.path = options.path;
  this.protocol = protocol;
  this.host = host;
  delete options.method;
  delete options.path;

  var sessionOptions = _objectSpread({}, options);

  if (options.socketPath) {
    sessionOptions.socketPath = options.socketPath;
    sessionOptions.createConnection = this.createUnixConnection.bind(this);
  }

  this._headers = {};
  var session = http2.connect("".concat(protocol, "//").concat(host, ":").concat(port), sessionOptions);
  this.setHeader('host', "".concat(host, ":").concat(port));
  session.on('error', function (err) {
    return _this.emit('error', err);
  });
  this.session = session;
}
/**
 * Inherit from `Stream` (which inherits from `EventEmitter`).
 */


util.inherits(Request, Stream);

Request.prototype.createUnixConnection = function (authority, options) {
  switch (this.protocol) {
    case 'http:':
      return net.connect(options.socketPath);

    case 'https:':
      options.ALPNProtocols = ['h2'];
      options.servername = this.host;
      options.allowHalfOpen = true;
      return tls.connect(options.socketPath, options);

    default:
      throw new Error('Unsupported protocol', this.protocol);
  }
}; // eslint-disable-next-line no-unused-vars


Request.prototype.setNoDelay = function (bool) {// We can not use setNoDelay with HTTP/2.
  // Node 10 limits http2session.socket methods to ones safe to use with HTTP/2.
  // See also https://nodejs.org/api/http2.html#http2_http2session_socket
};

Request.prototype.getFrame = function () {
  var _method,
      _this2 = this;

  if (this.frame) {
    return this.frame;
  }

  var method = (_method = {}, _defineProperty(_method, HTTP2_HEADER_PATH, this.path), _defineProperty(_method, HTTP2_HEADER_METHOD, this.method), _method);
  var headers = this.mapToHttp2Header(this._headers);
  headers = Object.assign(headers, method);
  var frame = this.session.request(headers); // eslint-disable-next-line no-unused-vars

  frame.once('response', function (headers, flags) {
    headers = _this2.mapToHttpHeader(headers);
    frame.headers = headers;
    frame.statusCode = headers[HTTP2_HEADER_STATUS];
    frame.status = frame.statusCode;

    _this2.emit('response', frame);
  });
  this._headerSent = true;
  frame.once('drain', function () {
    return _this2.emit('drain');
  });
  frame.on('error', function (err) {
    return _this2.emit('error', err);
  });
  frame.on('close', function () {
    return _this2.session.close();
  });
  this.frame = frame;
  return frame;
};

Request.prototype.mapToHttpHeader = function (headers) {
  var keys = Object.keys(headers);
  var http2Headers = {};

  for (var _i = 0, _keys = keys; _i < _keys.length; _i++) {
    var key = _keys[_i];
    var value = headers[key];
    key = key.toLowerCase();

    switch (key) {
      case HTTP2_HEADER_SET_COOKIE:
        value = Array.isArray(value) ? value : [value];
        break;

      default:
        break;
    }

    http2Headers[key] = value;
  }

  return http2Headers;
};

Request.prototype.mapToHttp2Header = function (headers) {
  var keys = Object.keys(headers);
  var http2Headers = {};

  for (var _i2 = 0, _keys2 = keys; _i2 < _keys2.length; _i2++) {
    var key = _keys2[_i2];
    var value = headers[key];
    key = key.toLowerCase();

    switch (key) {
      case HTTP2_HEADER_HOST:
        key = HTTP2_HEADER_AUTHORITY;
        value = /^http:\/\/|^https:\/\//.test(value) ? parse(value).host : value;
        break;

      default:
        break;
    }

    http2Headers[key] = value;
  }

  return http2Headers;
};

Request.prototype.setHeader = function (name, value) {
  this._headers[name.toLowerCase()] = value;
};

Request.prototype.getHeader = function (name) {
  return this._headers[name.toLowerCase()];
};

Request.prototype.write = function (data, encoding) {
  var frame = this.getFrame();
  return frame.write(data, encoding);
};

Request.prototype.pipe = function (stream, options) {
  var frame = this.getFrame();
  return frame.pipe(stream, options);
};

Request.prototype.end = function (data) {
  var frame = this.getFrame();
  frame.end(data);
}; // eslint-disable-next-line no-unused-vars


Request.prototype.abort = function (data) {
  var frame = this.getFrame();
  frame.close(NGHTTP2_CANCEL);
  this.session.destroy();
};

exports.setProtocol = setProtocol;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
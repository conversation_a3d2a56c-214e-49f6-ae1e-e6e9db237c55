"use strict";

/**
 * Module dependencies.
 */
var _require = require('string_decoder'),
    StringDecoder = _require.StringDecoder;

var Stream = require('stream');

var zlib = require('zlib');
/**
 * Buffers response data events and re-emits when they're unzipped.
 *
 * @param {Request} req
 * @param {Response} res
 * @api private
 */


exports.unzip = function (req, res) {
  var unzip = zlib.createUnzip();
  var stream = new Stream();
  var decoder; // make node responseOnEnd() happy

  stream.req = req;
  unzip.on('error', function (err) {
    if (err && err.code === 'Z_BUF_ERROR') {
      // unexpected end of file is ignored by browsers and curl
      stream.emit('end');
      return;
    }

    stream.emit('error', err);
  }); // pipe to unzip

  res.pipe(unzip); // override `setEncoding` to capture encoding

  res.setEncoding = function (type) {
    decoder = new StringDecoder(type);
  }; // decode upon decompressing with captured encoding


  unzip.on('data', function (buf) {
    if (decoder) {
      var str = decoder.write(buf);
      if (str.length > 0) stream.emit('data', str);
    } else {
      stream.emit('data', buf);
    }
  });
  unzip.on('end', function () {
    stream.emit('end');
  }); // override `on` to capture data listeners

  var _on = res.on;

  res.on = function (type, fn) {
    if (type === 'data' || type === 'end') {
      stream.on(type, fn.bind(res));
    } else if (type === 'error') {
      stream.on(type, fn.bind(res));

      _on.call(res, type, fn);
    } else {
      _on.call(res, type, fn);
    }

    return this;
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).superagent=t()}}(function(){var t={exports:{}};function e(t){if(t)return function(t){for(var r in e.prototype)t[r]=e.prototype[r];return t}(t)}t.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,o=this._callbacks["$"+t];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<o.length;n++)if((r=o[n])===e||r.fn===e){o.splice(n,1);break}return 0===o.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),r=this._callbacks["$"+t],o=1;o<arguments.length;o++)e[o-1]=arguments[o];if(r){o=0;for(var n=(r=r.slice(0)).length;o<n;++o)r[o].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length},t=t.exports;var r;function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r=s,s.default=s,s.stable=u,s.stableStringify=u;var n=[],i=[];function s(t,e,r){var s;for(function t(e,r,s,a){var u;if("object"==o(e)&&null!==e){for(u=0;u<s.length;u++)if(s[u]===e){var p=Object.getOwnPropertyDescriptor(a,r);return void(void 0!==p.get?p.configurable?(Object.defineProperty(a,r,{value:"[Circular]"}),n.push([a,r,e,p])):i.push([e,r]):(a[r]="[Circular]",n.push([a,r,e])))}if(s.push(e),Array.isArray(e))for(u=0;u<e.length;u++)t(e[u],u,s,e);else{var h=Object.keys(e);for(u=0;u<h.length;u++){var c=h[u];t(e[c],c,s,e)}}s.pop()}}(t,"",[],void 0),s=0===i.length?JSON.stringify(t,e,r):JSON.stringify(t,p(e),r);0!==n.length;){var a=n.pop();4===a.length?Object.defineProperty(a[0],a[1],a[3]):a[0][a[1]]=a[2]}return s}function a(t,e){return t<e?-1:t>e?1:0}function u(t,e,r){var s,u=function t(e,r,s,u){var p;if("object"==o(e)&&null!==e){for(p=0;p<s.length;p++)if(s[p]===e){var h=Object.getOwnPropertyDescriptor(u,r);return void(void 0!==h.get?h.configurable?(Object.defineProperty(u,r,{value:"[Circular]"}),n.push([u,r,e,h])):i.push([e,r]):(u[r]="[Circular]",n.push([u,r,e])))}if("function"==typeof e.toJSON)return;if(s.push(e),Array.isArray(e))for(p=0;p<e.length;p++)t(e[p],p,s,e);else{var c={},l=Object.keys(e).sort(a);for(p=0;p<l.length;p++){var f=l[p];t(e[f],f,s,e),c[f]=e[f]}if(void 0===u)return c;n.push([u,r,e]),u[r]=c}s.pop()}}(t,"",[],void 0)||t;for(s=0===i.length?JSON.stringify(u,e,r):JSON.stringify(u,p(e),r);0!==n.length;){var h=n.pop();4===h.length?Object.defineProperty(h[0],h[1],h[3]):h[0][h[1]]=h[2]}return s}function p(t){return t=void 0!==t?t:function(t,e){return e},function(e,r){if(i.length>0)for(var o=0;o<i.length;o++){var n=i[o];if(n[1]===e&&n[0]===r){r="[Circular]",i.splice(o,1);break}}return t.call(this,e,r)}}function h(t){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var c=function(t){return null!==t&&"object"==h(t)},l={};function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t){if(t)return function(t){for(var e in d.prototype)Object.prototype.hasOwnProperty.call(d.prototype,e)&&(t[e]=d.prototype[e]);return t}(t)}l=d,d.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},d.prototype.parse=function(t){return this._parser=t,this},d.prototype.responseType=function(t){return this._responseType=t,this},d.prototype.serialize=function(t){return this._serializer=t,this},d.prototype.timeout=function(t){if(!t||"object"!=f(t))return this._timeout=t,this._responseTimeout=0,this._uploadTimeout=0,this;for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e))switch(e){case"deadline":this._timeout=t.deadline;break;case"response":this._responseTimeout=t.response;break;case"upload":this._uploadTimeout=t.upload;break;default:console.warn("Unknown timeout option",e)}return this},d.prototype.retry=function(t,e){return 0!==arguments.length&&!0!==t||(t=1),t<=0&&(t=0),this._maxRetries=t,this._retries=0,this._retryCallback=e,this};var y=["ECONNRESET","ETIMEDOUT","EADDRINFO","ESOCKETTIMEDOUT"];d.prototype._shouldRetry=function(t,e){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{var r=this._retryCallback(t,e);if(!0===r)return!0;if(!1===r)return!1}catch(o){console.error(o)}if(e&&e.status&&e.status>=500&&501!==e.status)return!0;if(t){if(t.code&&y.includes(t.code))return!0;if(t.timeout&&"ECONNABORTED"===t.code)return!0;if(t.crossDomain)return!0}return!1},d.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},d.prototype.then=function(t,e){var r=this;if(!this._fullfilledPromise){var o=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise(function(t,e){o.on("abort",function(){if(!(r._maxRetries&&r._maxRetries>r._retries))if(r.timedout&&r.timedoutError)e(r.timedoutError);else{var t=new Error("Aborted");t.code="ABORTED",t.status=r.status,t.method=r.method,t.url=r.url,e(t)}}),o.end(function(r,o){r?e(r):t(o)})})}return this._fullfilledPromise.then(t,e)},d.prototype.catch=function(t){return this.then(void 0,t)},d.prototype.use=function(t){return t(this),this},d.prototype.ok=function(t){if("function"!=typeof t)throw new Error("Callback required");return this._okCallback=t,this},d.prototype._isResponseOK=function(t){return!!t&&(this._okCallback?this._okCallback(t):t.status>=200&&t.status<300)},d.prototype.get=function(t){return this._header[t.toLowerCase()]},d.prototype.getHeader=d.prototype.get,d.prototype.set=function(t,e){if(c(t)){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&this.set(r,t[r]);return this}return this._header[t.toLowerCase()]=e,this.header[t]=e,this},d.prototype.unset=function(t){return delete this._header[t.toLowerCase()],delete this.header[t],this},d.prototype.field=function(t,e){if(null==t)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(c(t)){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&this.field(r,t[r]);return this}if(Array.isArray(e)){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&this.field(t,e[o]);return this}if(null==e)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof e&&(e=String(e)),this._getFormData().append(t,e),this},d.prototype.abort=function(){return this._aborted?this:(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort"),this)},d.prototype._auth=function(t,e,r,o){switch(r.type){case"basic":this.set("Authorization","Basic ".concat(o("".concat(t,":").concat(e))));break;case"auto":this.username=t,this.password=e;break;case"bearer":this.set("Authorization","Bearer ".concat(t))}return this},d.prototype.withCredentials=function(t){return void 0===t&&(t=!0),this._withCredentials=t,this},d.prototype.redirects=function(t){return this._maxRedirects=t,this},d.prototype.maxResponseSize=function(t){if("number"!=typeof t)throw new TypeError("Invalid argument");return this._maxResponseSize=t,this},d.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},d.prototype.send=function(t){var e=c(t),r=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(e&&!this._data)Array.isArray(t)?this._data=[]:this._isHost(t)||(this._data={});else if(t&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(e&&c(this._data))for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(this._data[o]=t[o]);else"string"==typeof t?(r||this.type("form"),r=this._header["content-type"],this._data="application/x-www-form-urlencoded"===r?this._data?"".concat(this._data,"&").concat(t):t:(this._data||"")+t):this._data=t;return!e||this._isHost(t)?this:(r||this.type("json"),this)},d.prototype.sortQuery=function(t){return this._sort=void 0===t||t,this},d.prototype._finalizeQueryString=function(){var t=this._query.join("&");if(t&&(this.url+=(this.url.includes("?")?"&":"?")+t),this._query.length=0,this._sort){var e=this.url.indexOf("?");if(e>=0){var r=this.url.slice(e+1).split("&");"function"==typeof this._sort?r.sort(this._sort):r.sort(),this.url=this.url.slice(0,e)+"?"+r.join("&")}}},d.prototype._appendQueryString=function(){console.warn("Unsupported")},d.prototype._timeoutError=function(t,e,r){if(!this._aborted){var o=new Error("".concat(t+e,"ms exceeded"));o.timeout=e,o.code="ECONNABORTED",o.errno=r,this.timedout=!0,this.timedoutError=o,this.abort(),this.callback(o)}},d.prototype._setTimeouts=function(){var t=this;this._timeout&&!this._timer&&(this._timer=setTimeout(function(){t._timeoutError("Timeout of ",t._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(function(){t._timeoutError("Response timeout of ",t._responseTimeout,"ETIMEDOUT")},this._responseTimeout))};var m={type:function(t){return t.split(/ *; */).shift()},params:function(t){return t.split(/ *; */).reduce(function(t,e){var r=e.split(/ *= */),o=r.shift(),n=r.shift();return o&&n&&(t[o]=n),t},{})},parseLinks:function(t){return t.split(/ *, */).reduce(function(t,e){var r=e.split(/ *; */),o=r[0].slice(1,-1);return t[r[1].split(/ *= */)[1].slice(1,-1)]=o,t},{})}},b={};function _(t){if(t)return function(t){for(var e in _.prototype)Object.prototype.hasOwnProperty.call(_.prototype,e)&&(t[e]=_.prototype[e]);return t}(t)}b=_,_.prototype.get=function(t){return this.header[t.toLowerCase()]},_.prototype._setHeaderProperties=function(t){var e=t["content-type"]||"";this.type=m.type(e);var r=m.params(e);for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(this[o]=r[o]);this.links={};try{t.link&&(this.links=m.parseLinks(t.link))}catch(n){}},_.prototype._setStatusProperties=function(t){var e=t/100|0;this.statusCode=t,this.status=this.statusCode,this.statusType=e,this.info=1===e,this.ok=2===e,this.redirect=3===e,this.clientError=4===e,this.serverError=5===e,this.error=(4===e||5===e)&&this.toError(),this.created=201===t,this.accepted=202===t,this.noContent=204===t,this.badRequest=400===t,this.unauthorized=401===t,this.notAcceptable=406===t,this.forbidden=403===t,this.notFound=404===t,this.unprocessableEntity=422===t};var w={};function v(t){return function(t){if(Array.isArray(t))return T(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return T(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return T(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=new Array(e);r<e;r++)o[r]=t[r];return o}function g(){this._defaults=[]}["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"].forEach(function(t){g.prototype[t]=function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return this._defaults.push({fn:t,args:r}),this}}),g.prototype._setDefaults=function(t){this._defaults.forEach(function(e){t[e.fn].apply(t,v(e.args))})},w=g;var E,O={};function x(t){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function k(){}"undefined"!=typeof window?E=window:"undefined"==typeof self?(console.warn("Using browser-only version of superagent in non-browser environment"),E=void 0):E=self;var S=O=O=function(t,e){return"function"==typeof e?new O.Request("GET",t).end(e):1===arguments.length?new O.Request("GET",t):new O.Request(t,e)};O.Request=D,S.getXHR=function(){if(E.XMLHttpRequest&&(!E.location||"file:"!==E.location.protocol||!E.ActiveXObject))return new XMLHttpRequest;try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(t){}try{return new ActiveXObject("Msxml2.XMLHTTP.6.0")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP.3.0")}catch(r){}try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(o){}throw new Error("Browser-only version of superagent could not find XHR")};var j="".trim?function(t){return t.trim()}:function(t){return t.replace(/(^\s*|\s*$)/g,"")};function C(t){if(!c(t))return t;var e=[];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&A(e,r,t[r]);return e.join("&")}function A(t,e,r){if(void 0!==r)if(null!==r)if(Array.isArray(r))r.forEach(function(r){A(t,e,r)});else if(c(r))for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&A(t,"".concat(e,"[").concat(o,"]"),r[o]);else t.push(encodeURI(e)+"="+encodeURIComponent(r));else t.push(encodeURI(e))}function R(t){for(var e,r,o={},n=t.split("&"),i=0,s=n.length;i<s;++i)-1===(r=(e=n[i]).indexOf("="))?o[decodeURIComponent(e)]="":o[decodeURIComponent(e.slice(0,r))]=decodeURIComponent(e.slice(r+1));return o}function P(t){return/[/+]json($|[^-\w])/.test(t)}function q(t){this.req=t,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;var e=this.xhr.status;1223===e&&(e=204),this._setStatusProperties(e),this.headers=function(t){for(var e,r,o,n,i=t.split(/\r?\n/),s={},a=0,u=i.length;a<u;++a)-1!==(e=(r=i[a]).indexOf(":"))&&(o=r.slice(0,e).toLowerCase(),n=j(r.slice(e+1)),s[o]=n);return s}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&t._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function D(t,e){var r=this;this._query=this._query||[],this.method=t,this.url=e,this.header={},this._header={},this.on("end",function(){var t,e=null,o=null;try{o=new q(r)}catch(n){return(e=new Error("Parser is unable to parse the response")).parse=!0,e.original=n,r.xhr?(e.rawResponse=void 0===r.xhr.responseType?r.xhr.responseText:r.xhr.response,e.status=r.xhr.status?r.xhr.status:null,e.statusCode=e.status):(e.rawResponse=null,e.status=null),r.callback(e)}r.emit("response",o);try{r._isResponseOK(o)||(t=new Error(o.statusText||o.text||"Unsuccessful HTTP response"))}catch(n){t=n}t?(t.original=e,t.response=o,t.status=o.status,r.callback(t,o)):r.callback(null,o)})}function H(t,e,r){var o=S("DELETE",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o}return S.serializeObject=C,S.parseString=R,S.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},S.serialize={"application/x-www-form-urlencoded":C,"application/json":r},S.parse={"application/x-www-form-urlencoded":R,"application/json":JSON.parse},b(q.prototype),q.prototype._parseBody=function(t){var e=S.parse[this.type];return this.req._parser?this.req._parser(this,t):(!e&&P(this.type)&&(e=S.parse["application/json"]),e&&t&&(t.length>0||t instanceof Object)?e(t):null)},q.prototype.toError=function(){var t=this.req,e=t.method,r=t.url,o="cannot ".concat(e," ").concat(r," (").concat(this.status,")"),n=new Error(o);return n.status=this.status,n.method=e,n.url=r,n},S.Response=q,t(D.prototype),l(D.prototype),D.prototype.type=function(t){return this.set("Content-Type",S.types[t]||t),this},D.prototype.accept=function(t){return this.set("Accept",S.types[t]||t),this},D.prototype.auth=function(t,e,r){return 1===arguments.length&&(e=""),"object"==x(e)&&null!==e&&(r=e,e=""),r||(r={type:"function"==typeof btoa?"basic":"auto"}),this._auth(t,e,r,function(t){if("function"==typeof btoa)return btoa(t);throw new Error("Cannot use basic auth, btoa is not a function")})},D.prototype.query=function(t){return"string"!=typeof t&&(t=C(t)),t&&this._query.push(t),this},D.prototype.attach=function(t,e,r){if(e){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(t,e,r||e.name)}return this},D.prototype._getFormData=function(){return this._formData||(this._formData=new E.FormData),this._formData},D.prototype.callback=function(t,e){if(this._shouldRetry(t,e))return this._retry();var r=this._callback;this.clearTimeout(),t&&(this._maxRetries&&(t.retries=this._retries-1),this.emit("error",t)),r(t,e)},D.prototype.crossDomainError=function(){var t=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");t.crossDomain=!0,t.status=this.status,t.method=this.method,t.url=this.url,this.callback(t)},D.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},D.prototype.ca=D.prototype.agent,D.prototype.buffer=D.prototype.ca,D.prototype.write=function(){throw new Error("Streaming is not supported in browser version of superagent")},D.prototype.pipe=D.prototype.write,D.prototype._isHost=function(t){return t&&"object"==x(t)&&!Array.isArray(t)&&"[object Object]"!==Object.prototype.toString.call(t)},D.prototype.end=function(t){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=t||k,this._finalizeQueryString(),this._end()},D.prototype._setUploadTimeout=function(){var t=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout(function(){t._timeoutError("Upload timeout of ",t._uploadTimeout,"ETIMEDOUT")},this._uploadTimeout))},D.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));var t=this;this.xhr=S.getXHR();var e=this.xhr,r=this._formData||this._data;this._setTimeouts(),e.onreadystatechange=function(){var r=e.readyState;if(r>=2&&t._responseTimeoutTimer&&clearTimeout(t._responseTimeoutTimer),4===r){var o;try{o=e.status}catch(n){o=0}if(!o){if(t.timedout||t._aborted)return;return t.crossDomainError()}t.emit("end")}};var o=function(e,r){r.total>0&&(r.percent=r.loaded/r.total*100,100===r.percent&&clearTimeout(t._uploadTimeoutTimer)),r.direction=e,t.emit("progress",r)};if(this.hasListeners("progress"))try{e.addEventListener("progress",o.bind(null,"download")),e.upload&&e.upload.addEventListener("progress",o.bind(null,"upload"))}catch(a){}e.upload&&this._setUploadTimeout();try{this.username&&this.password?e.open(this.method,this.url,!0,this.username,this.password):e.open(this.method,this.url,!0)}catch(u){return this.callback(u)}if(this._withCredentials&&(e.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof r&&!this._isHost(r)){var n=this._header["content-type"],i=this._serializer||S.serialize[n?n.split(";")[0]:""];!i&&P(n)&&(i=S.serialize["application/json"]),i&&(r=i(r))}for(var s in this.header)null!==this.header[s]&&Object.prototype.hasOwnProperty.call(this.header,s)&&e.setRequestHeader(s,this.header[s]);this._responseType&&(e.responseType=this._responseType),this.emit("request",this),e.send(void 0===r?null:r)},S.agent=function(){return new w},["GET","POST","OPTIONS","PATCH","PUT","DELETE"].forEach(function(t){w.prototype[t.toLowerCase()]=function(e,r){var o=new S.Request(t,e);return this._setDefaults(o),r&&o.end(r),o}}),w.prototype.del=w.prototype.delete,S.get=function(t,e,r){var o=S("GET",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},S.head=function(t,e,r){var o=S("HEAD",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},S.options=function(t,e,r){var o=S("OPTIONS",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},S.del=H,S.delete=H,S.patch=function(t,e,r){var o=S("PATCH",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},S.post=function(t,e,r){var o=S("POST",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},S.put=function(t,e,r){var o=S("PUT",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},O});
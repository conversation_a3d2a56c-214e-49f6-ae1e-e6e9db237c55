{"extends": ["eslint:recommended"], "env": {"node": false, "browser": true, "amd": true, "es6": true}, "plugins": ["compat"], "rules": {"compat/compat": "error", "no-console": "off", "no-empty": "off", "no-extra-semi": "off", "no-func-assign": "off", "no-undef": "off", "no-unused-vars": "off", "no-useless-escape": "off", "no-cond-assign": "off", "no-redeclare": "off", "node/no-exports-assign": "off"}, "globals": {"regeneratorRuntime": "writable"}, "settings": {"polyfills": ["Promise", "Array.from", "Symbol", "Object.getOwnPropertySymbols", "Object.setPrototypeOf"]}}
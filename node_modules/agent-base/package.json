{"_from": "agent-base@^4.2.0", "_id": "agent-base@4.2.1", "_inBundle": false, "_integrity": "sha512-JVwXMr9nHYTUXsBFKUqhJwvlcYU/blreOEUkhNR2eXZIvwd+c+o5V4MgDPKWnMS/56awN3TRzIP+KoPn+roQtg==", "_location": "/agent-base", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "agent-base@^4.2.0", "name": "agent-base", "escapedName": "agent-base", "rawSpec": "^4.2.0", "saveSpec": null, "fetchSpec": "^4.2.0"}, "_requiredBy": ["/http-proxy-agent", "/https-proxy-agent", "/pac-proxy-agent", "/proxy-agent", "/socks-proxy-agent"], "_resolved": "https://registry.npmjs.org/agent-base/-/agent-base-4.2.1.tgz", "_shasum": "d89e5999f797875674c07d87f260fc41e83e8ca9", "_spec": "agent-base@^4.2.0", "_where": "D:\\projects\\soil-samples-app\\node_modules\\proxy-agent", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "bundleDependencies": false, "dependencies": {"es6-promisify": "^5.0.0"}, "deprecated": false, "description": "Turn a function into an `http.Agent` instance", "devDependencies": {"mocha": "^3.4.2", "ws": "^3.0.0"}, "engines": {"node": ">= 4.0.0"}, "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "keywords": ["http", "agent", "base", "barebones", "https"], "license": "MIT", "main": "./index.js", "name": "agent-base", "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "scripts": {"test": "mocha --reporter spec"}, "version": "4.2.1"}
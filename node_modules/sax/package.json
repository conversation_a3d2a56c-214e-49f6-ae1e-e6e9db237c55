{"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "version": "1.1.4", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js", "posttest": "npm run lint", "lint": "standard -F test/*.js lib/*.js"}, "repository": "git://github.com/isaacs/sax-js.git", "files": ["lib/sax.js", "LICENSE", "LICENSE-W3C.html", "README.md"], "devDependencies": {"standard": "^5.3.1", "tap": "^2.1.1"}}
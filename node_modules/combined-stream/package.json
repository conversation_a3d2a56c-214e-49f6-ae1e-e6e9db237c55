{"_from": "combined-stream@^1.0.6", "_id": "combined-stream@1.0.7", "_inBundle": false, "_integrity": "sha512-brWl9y6vOB1xYPZcpZde3N9zDByXTosAeMDo4p1wzo6UMOX4vumB+TP1RZ76sfE6Md68Q0NJSrE/gbezd4Ul+w==", "_location": "/combined-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "combined-stream@^1.0.6", "name": "combined-stream", "escapedName": "combined-stream", "rawSpec": "^1.0.6", "saveSpec": null, "fetchSpec": "^1.0.6"}, "_requiredBy": ["/form-data"], "_resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.7.tgz", "_shasum": "2d1d24317afb8abe95d6d2c0b07b57813539d828", "_spec": "combined-stream@^1.0.6", "_where": "D:\\projects\\soil-samples-app\\node_modules\\form-data", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "bundleDependencies": false, "dependencies": {"delayed-stream": "~1.0.0"}, "deprecated": false, "description": "A stream that emits multiple other streams one after another.", "devDependencies": {"far": "~0.0.7"}, "engines": {"node": ">= 0.8"}, "homepage": "https://github.com/felixge/node-combined-stream", "license": "MIT", "main": "./lib/combined_stream", "name": "combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "scripts": {"test": "node test/run.js"}, "version": "1.0.7"}
{"_from": "component-emitter@^1.2.0", "_id": "component-emitter@1.2.1", "_inBundle": false, "_integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=", "_location": "/component-emitter", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "component-emitter@^1.2.0", "name": "component-emitter", "escapedName": "component-emitter", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/superagent"], "_resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz", "_shasum": "137918d6d78283f7df7a6b7c5a63e140e69425e6", "_spec": "component-emitter@^1.2.0", "_where": "D:\\projects\\soil-samples-app\\node_modules\\superagent", "bugs": {"url": "https://github.com/component/emitter/issues"}, "bundleDependencies": false, "component": {"scripts": {"emitter/index.js": "index.js"}}, "deprecated": false, "description": "Event emitter", "devDependencies": {"mocha": "*", "should": "*"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/component/emitter#readme", "license": "MIT", "main": "index.js", "name": "component-emitter", "repository": {"type": "git", "url": "git+https://github.com/component/emitter.git"}, "scripts": {"test": "make test"}, "version": "1.2.1"}

1.2.1 / 2016-04-18
==================

 * enable client side use

1.2.0 / 2014-02-12
==================

 * prefix events with `$` to support object prototype method names

1.1.3 / 2014-06-20
==================

 * republish for npm
 * add LICENSE file

1.1.2 / 2014-02-10
==================

  * package: rename to "component-emitter"
  * package: update "main" and "component" fields
  * Add license to Readme (same format as the other components)
  * created .npmignore
  * travis stuff

1.1.1 / 2013-12-01
==================

  * fix .once adding .on to the listener
  * docs: Emitter#off()
  * component: add `.repo` prop

1.1.0 / 2013-10-20
==================

 * add `.addEventListener()` and `.removeEventListener()` aliases

1.0.1 / 2013-06-27
==================

 * add support for legacy ie

1.0.0 / 2013-02-26
==================

  * add `.off()` support for removing all listeners

0.0.6 / 2012-10-08
==================

  * add `this._callbacks` initialization to prevent funky gotcha

0.0.5 / 2012-09-07
==================

  * fix `Emitter.call(this)` usage

0.0.3 / 2012-07-11
==================

  * add `.listeners()`
  * rename `.has()` to `.hasListeners()`

0.0.2 / 2012-06-28
==================

  * fix `.off()` with `.once()`-registered callbacks

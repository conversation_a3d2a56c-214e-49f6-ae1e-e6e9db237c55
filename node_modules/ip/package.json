{"name": "ip", "version": "1.1.9", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/indutny/node-ip", "repository": {"type": "git", "url": "http://github.com/indutny/node-ip.git"}, "files": ["lib", "README.md"], "main": "lib/ip", "devDependencies": {"eslint": "^8.15.0", "mocha": "^10.0.0"}, "scripts": {"lint": "eslint lib/*.js test/*.js", "test": "npm run lint && mocha --reporter spec test/*-test.js", "fix": "npm run lint -- --fix"}, "license": "MIT"}
{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "install", "description": "Minimal JavaScript module loader", "keywords": ["modules", "require", "commonjs", "exports", "browser", "packaging", "packager", "install"], "version": "0.8.9", "license": "MIT", "homepage": "http://github.com/benjamn/install", "repository": {"type": "git", "url": "git://github.com/benjamn/install.git"}, "main": "install.js", "scripts": {"prepublish": "scripts/prepublish.sh", "docs": "scripts/docs.sh", "test": "mocha --reporter spec --full-trace test/run.js"}, "devDependencies": {"docco": "^0.7.0", "mocha": "^3.2.0", "uglifyjs": "^2.4.10", "reify": "^0.8.0"}, "engines": {"node": ">= 0.10"}}
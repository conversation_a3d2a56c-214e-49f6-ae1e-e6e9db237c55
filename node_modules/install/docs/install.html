<!DOCTYPE html>

<html>
<head>
  <title>install.js</title>
  <meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, target-densitydpi=160dpi, initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
  <link rel="stylesheet" media="all" href="docco.css" />
</head>
<body>
  <div id="container">
    <div id="background"></div>
    
    <ul class="sections">
        
          <li id="title">
              <div class="annotation">
                  <h1>install.js</h1>
              </div>
          </li>
        
        
        
        <li id="section-1">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-1">&#182;</a>
              </div>
              
            </div>
            
            <div class="content"><div class='highlight'><pre>(<span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">global, undefined</span>) </span>{
  <span class="hljs-keyword">if</span> (global.makeInstaller) {
    <span class="hljs-keyword">return</span>;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">makeInstaller</span>(<span class="hljs-params">options</span>) </span>{
    <span class="hljs-keyword">var</span> root = <span class="hljs-keyword">new</span> File({});</pre></div></div>
            
        </li>
        
        
        <li id="section-2">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-2">&#182;</a>
              </div>
              <p>Set up a simple queue for tracking required modules with unmet
dependencies. See also <code>queueAppend</code> and <code>queueFlush</code>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    <span class="hljs-keyword">var</span> q = root.q = {};
    q.h = q.t = {}; <span class="hljs-comment">// Queue head, queue tail.</span></pre></div></div>
            
        </li>
        
        
        <li id="section-3">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-3">&#182;</a>
              </div>
              <p>Configurable function for deferring queue flushes.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    q.d = options &amp;&amp; options.defer || <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">fn</span>) </span>{
      setTimeout(fn, <span class="hljs-number">0</span>);
    };

    <span class="hljs-keyword">return</span> <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">install</span>(<span class="hljs-params">tree</span>) </span>{
      <span class="hljs-keyword">if</span> (isObject(tree)) {
        fileMergeContents(root, tree);
        queueFlush(root.q);
      }
      <span class="hljs-keyword">return</span> root.r;
    };
  }

  global.makeInstaller = makeInstaller;

  <span class="hljs-keyword">if</span> (<span class="hljs-keyword">typeof</span> exports === <span class="hljs-string">"object"</span>) {
    exports.makeInstaller = makeInstaller;
  }

  <span class="hljs-keyword">var</span> extensions = [<span class="hljs-string">""</span>, <span class="hljs-string">".js"</span>, <span class="hljs-string">".json"</span>];
  <span class="hljs-keyword">var</span> MISSING = {};
  <span class="hljs-keyword">var</span> hasOwn = MISSING.hasOwnProperty;
  <span class="hljs-keyword">var</span> Ap = <span class="hljs-built_in">Array</span>.prototype;

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">getOwn</span>(<span class="hljs-params">obj, key</span>) </span>{
    <span class="hljs-keyword">return</span> hasOwn.call(obj, key) &amp;&amp; obj[key];
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">isObject</span>(<span class="hljs-params">value</span>) </span>{
    <span class="hljs-keyword">return</span> value &amp;&amp; <span class="hljs-keyword">typeof</span> value === <span class="hljs-string">"object"</span>;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">isFunction</span>(<span class="hljs-params">value</span>) </span>{
    <span class="hljs-keyword">return</span> <span class="hljs-keyword">typeof</span> value === <span class="hljs-string">"function"</span>;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">isString</span>(<span class="hljs-params">value</span>) </span>{
    <span class="hljs-keyword">return</span> <span class="hljs-keyword">typeof</span> value === <span class="hljs-string">"string"</span>;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">queueAppend</span>(<span class="hljs-params">q, file</span>) </span>{</pre></div></div>
            
        </li>
        
        
        <li id="section-4">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-4">&#182;</a>
              </div>
              <p>Property names shortened to shave bytes: <code>.t</code> means <code>.tail</code>, <code>.h</code>
means <code>.head</code>, <code>.n</code> means <code>.next</code>, and <code>.f</code> means <code>.file</code>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    q.t = q.t.n = { f: file };
    <span class="hljs-keyword">if</span> (q.h.n === q.t) {</pre></div></div>
            
        </li>
        
        
        <li id="section-5">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-5">&#182;</a>
              </div>
              <p>If the queue contains only one File (the one we just added), go
ahead and schedule a flush.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      queueFlush(q);
    }
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">queueFlush</span>(<span class="hljs-params">q</span>) </span>{</pre></div></div>
            
        </li>
        
        
        <li id="section-6">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-6">&#182;</a>
              </div>
              <p>The <code>q.p</code> property is set to indicate a flush is pending.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    q.p || (q.p = <span class="hljs-literal">true</span>, q.d(<span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
      q.p = <span class="hljs-literal">undefined</span>;
      <span class="hljs-keyword">var</span> next = q.h.n;
      <span class="hljs-keyword">if</span> (next &amp;&amp; fileReady(next.f)) {
        queueFlush(q); <span class="hljs-comment">// Schedule the next flush.</span>
        q.h = next;
        fileEvaluate(next.f);
      }
    }));
  }</pre></div></div>
            
        </li>
        
        
        <li id="section-7">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-7">&#182;</a>
              </div>
              <p>These <code>unbound{Require,Ensure}</code> functions need to be bound to File
objects before they can be used. See <code>makeRequire</code>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>
  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">unboundRequire</span>(<span class="hljs-params">id</span>) </span>{
    <span class="hljs-keyword">var</span> result = fileEvaluate(fileResolve(<span class="hljs-keyword">this</span>, id));
    <span class="hljs-keyword">if</span> (result === MISSING) {
      <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">"Cannot find module '"</span> + id + <span class="hljs-string">"'"</span>);
    }
    <span class="hljs-keyword">return</span> result;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">unboundEnsure</span>(<span class="hljs-params"></span>) </span>{</pre></div></div>
            
        </li>
        
        
        <li id="section-8">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-8">&#182;</a>
              </div>
              <p>Flatten arguments into an array containing relative module
identifier strings and an optional callback function, then coerce
that array into a callback function with a <code>.d</code> property.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    <span class="hljs-keyword">var</span> flatArgs = Ap.concat.apply(Ap, <span class="hljs-built_in">arguments</span>);
    <span class="hljs-keyword">var</span> callback = ensureObjectOrFunction(flatArgs);</pre></div></div>
            
        </li>
        
        
        <li id="section-9">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-9">&#182;</a>
              </div>
              <p>Note that <code>queueAppend</code> schedules a flush if there are no other
callbacks waiting in the queue.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    queueAppend(<span class="hljs-keyword">this</span>.q, <span class="hljs-keyword">new</span> File(callback, <span class="hljs-keyword">this</span>));
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">makeRequire</span>(<span class="hljs-params">file</span>) </span>{
    <span class="hljs-keyword">var</span> <span class="hljs-built_in">require</span> = unboundRequire.bind(file);
    <span class="hljs-built_in">require</span>.ensure = unboundEnsure.bind(file);</pre></div></div>
            
        </li>
        
        
        <li id="section-10">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-10">&#182;</a>
              </div>
              <p>TODO Consider adding <code>require.promise</code>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    <span class="hljs-keyword">return</span> <span class="hljs-built_in">require</span>;
  }</pre></div></div>
            
        </li>
        
        
        <li id="section-11">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-11">&#182;</a>
              </div>
              <p>File objects represent either directories or modules that have been
installed. When a <code>File</code> respresents a directory, its <code>.c</code> (contents)
property is an object containing the names of the files (or
directories) that it contains. When a <code>File</code> represents a module, its
<code>.c</code> property is a function that can be invoked with the appropriate
<code>(require, exports, module)</code> arguments to evaluate the module. The
<code>.p</code> (parent) property of a File is either a directory <code>File</code> or
<code>null</code>. Note that a child may claim another <code>File</code> as its parent even
if the parent does not have an entry for that child in its <code>.c</code>
object.  This is important for implementing anonymous files, and
preventing child modules from using <code>../relative/identifier</code> syntax
to examine unrelated modules.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">File</span>(<span class="hljs-params">contents, <span class="hljs-comment">/*optional:*/</span> parent, name</span>) </span>{
    <span class="hljs-keyword">var</span> file = <span class="hljs-keyword">this</span>;</pre></div></div>
            
        </li>
        
        
        <li id="section-12">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-12">&#182;</a>
              </div>
              <p>Link to the parent file.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    file.p = parent = parent || <span class="hljs-literal">null</span>;

    <span class="hljs-keyword">if</span> (name) {</pre></div></div>
            
        </li>
        
        
        <li id="section-13">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-13">&#182;</a>
              </div>
              <p>If this file was created with <code>name</code>, join it with <code>parent.id</code> to
generate a module identifier.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      file.id = (parent &amp;&amp; parent.id || <span class="hljs-string">""</span>) + <span class="hljs-string">"/"</span> + name;
    }</pre></div></div>
            
        </li>
        
        
        <li id="section-14">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-14">&#182;</a>
              </div>
              <p>Queue for tracking required modules with unmet dependencies,
inherited from the <code>parent</code>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    file.q = parent &amp;&amp; parent.q;</pre></div></div>
            
        </li>
        
        
        <li id="section-15">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-15">&#182;</a>
              </div>
              <p>Each directory has its own bound version of the <code>require</code> function
that can resolve relative identifiers. Non-directory Files inherit
the require function of their parent directories, so we don’t have
to create a new require function every time we evaluate a module.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    file.r = isObject(contents)
      ? makeRequire(file)
      : parent &amp;&amp; parent.r;</pre></div></div>
            
        </li>
        
        
        <li id="section-16">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-16">&#182;</a>
              </div>
              <p>Set the initial value of <code>file.c</code> (the “contents” of the File).</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    fileMergeContents(file, contents);</pre></div></div>
            
        </li>
        
        
        <li id="section-17">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-17">&#182;</a>
              </div>
              <p>When the file is a directory, <code>file.ready</code> is an object mapping
module identifiers to boolean ready statuses. This information can
be shared by all files in the directory, because module resolution
always has the same results for all files in a given directory.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    file.ready = fileIsDirectory(file) &amp;&amp; {};
  }</pre></div></div>
            
        </li>
        
        
        <li id="section-18">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-18">&#182;</a>
              </div>
              <p>A file is ready if all of its dependencies are installed and ready.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileReady</span>(<span class="hljs-params">file</span>) </span>{
    <span class="hljs-keyword">var</span> result = !! file;
    <span class="hljs-keyword">var</span> <span class="hljs-built_in">module</span> = file &amp;&amp; file.c;
    <span class="hljs-keyword">var</span> deps = isFunction(<span class="hljs-built_in">module</span>) &amp;&amp; <span class="hljs-built_in">module</span>.d;
    <span class="hljs-keyword">if</span> (deps &amp;&amp; ! getOwn(<span class="hljs-built_in">module</span>, <span class="hljs-string">"seen"</span>)) {
      <span class="hljs-built_in">module</span>.seen = <span class="hljs-literal">true</span>;
      <span class="hljs-keyword">var</span> parentReadyCache = file.p.ready;
      result = <span class="hljs-built_in">Object</span>.keys(deps).every(<span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">dep</span>) </span>{</pre></div></div>
            
        </li>
        
        
        <li id="section-19">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-19">&#182;</a>
              </div>
              <p>By storing the results of these lookups in <code>parentReadyCache</code>,
we benefit when any other file in the same directory resolves
the same identifier.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>        <span class="hljs-keyword">return</span> parentReadyCache[dep] =
          parentReadyCache[dep] ||
          fileReady(fileResolve(file.p, dep));
      });
      <span class="hljs-built_in">module</span>.seen = <span class="hljs-literal">undefined</span>;
    }
    <span class="hljs-keyword">return</span> result;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileEvaluate</span>(<span class="hljs-params">file</span>) </span>{
    <span class="hljs-keyword">var</span> <span class="hljs-built_in">module</span> = file &amp;&amp; file.c;
    <span class="hljs-keyword">if</span> (isFunction(<span class="hljs-built_in">module</span>)) {
      <span class="hljs-keyword">if</span> (! hasOwn.call(<span class="hljs-built_in">module</span>, <span class="hljs-string">"exports"</span>)) {
        <span class="hljs-built_in">module</span>.id = file.id;
        <span class="hljs-built_in">module</span>.call(global, file.r, <span class="hljs-built_in">module</span>.exports = {}, <span class="hljs-built_in">module</span>);
      }
      <span class="hljs-keyword">return</span> <span class="hljs-built_in">module</span>.exports;
    }
    <span class="hljs-keyword">return</span> MISSING;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileIsDirectory</span>(<span class="hljs-params">file</span>) </span>{
    <span class="hljs-keyword">return</span> isObject(file.c);
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileMergeContents</span>(<span class="hljs-params">file, contents</span>) </span>{
    <span class="hljs-keyword">if</span> ((contents = ensureObjectOrFunction(contents))) {
      <span class="hljs-keyword">var</span> fileContents = file.c = file.c || (
        isFunction(contents) ? contents : {}
      );

      <span class="hljs-keyword">if</span> (isObject(contents) &amp;&amp; fileIsDirectory(file)) {
        <span class="hljs-built_in">Object</span>.keys(contents).forEach(<span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">key</span>) </span>{
          <span class="hljs-keyword">var</span> child = getOwn(fileContents, key);
          <span class="hljs-keyword">if</span> (child) {
            fileMergeContents(child, contents[key]);
          } <span class="hljs-keyword">else</span> {
            fileContents[key] = <span class="hljs-keyword">new</span> File(contents[key], file, key);
          }
        });
      }
    }
  };

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">ensureObjectOrFunction</span>(<span class="hljs-params">contents</span>) </span>{</pre></div></div>
            
        </li>
        
        
        <li id="section-20">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-20">&#182;</a>
              </div>
              <p>If contents is an array of strings and functions, return the last
function with a <code>.d</code> property containing all the strings.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    <span class="hljs-keyword">if</span> (<span class="hljs-built_in">Array</span>.isArray(contents)) {
      <span class="hljs-keyword">var</span> deps = {};
      <span class="hljs-keyword">var</span> func;

      contents.forEach(<span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">item</span>) </span>{
        <span class="hljs-keyword">if</span> (isString(item)) {
          deps[item] = <span class="hljs-literal">false</span>; <span class="hljs-comment">// Initially unsatisfied.</span>
        } <span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span> (isFunction(item)) {
          func = item;
        }
      });</pre></div></div>
            
        </li>
        
        
        <li id="section-21">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-21">&#182;</a>
              </div>
              <p>If no function was found in the array, provide a default function
that simply requires each dependency (really common case).</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      contents = func || <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">module, require</span>) </span>{
        <span class="hljs-built_in">Object</span>.keys(deps).forEach(<span class="hljs-built_in">require</span>);
      };

      contents.d = deps;

    } <span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span> (isFunction(contents)) {</pre></div></div>
            
        </li>
        
        
        <li id="section-22">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-22">&#182;</a>
              </div>
              <p>If contents is already a function, make sure it has deps.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      contents.d = contents.d || {};

    } <span class="hljs-keyword">else</span> <span class="hljs-keyword">if</span> (! isObject(contents)) {</pre></div></div>
            
        </li>
        
        
        <li id="section-23">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-23">&#182;</a>
              </div>
              <p>If contents is neither an array nor a function nor an object,
just give up and return null.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      contents = <span class="hljs-literal">null</span>;
    }

    <span class="hljs-keyword">return</span> contents;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileAppendIdPart</span>(<span class="hljs-params">file, part, isLastPart</span>) </span>{</pre></div></div>
            
        </li>
        
        
        <li id="section-24">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-24">&#182;</a>
              </div>
              <p>Always append relative to a directory.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    <span class="hljs-keyword">while</span> (file &amp;&amp; ! fileIsDirectory(file)) {
      file = file.p;
    }

    <span class="hljs-keyword">if</span> (! file || ! part || part === <span class="hljs-string">"."</span>) {
      <span class="hljs-keyword">return</span> file;
    }

    <span class="hljs-keyword">if</span> (part === <span class="hljs-string">".."</span>) {
      <span class="hljs-keyword">return</span> file.p;
    }

    <span class="hljs-keyword">for</span> (<span class="hljs-keyword">var</span> e = <span class="hljs-number">0</span>; e &lt; extensions.length; ++e) {
      <span class="hljs-keyword">var</span> withExtension = part + extensions[e];

      <span class="hljs-keyword">var</span> child = getOwn(file.c, withExtension);
      <span class="hljs-keyword">if</span> (child) {
        <span class="hljs-keyword">return</span> child;
      }

      <span class="hljs-keyword">if</span> (! isLastPart) {</pre></div></div>
            
        </li>
        
        
        <li id="section-25">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-25">&#182;</a>
              </div>
              <p>Only consider multiple file extensions if this part is the last
part of a module identifier, and not <code>.</code> or <code>..</code>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>        <span class="hljs-keyword">break</span>;
      }
    }
  };

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileAppendId</span>(<span class="hljs-params">file, id</span>) </span>{
    <span class="hljs-keyword">var</span> parts = id.split(<span class="hljs-string">"/"</span>);</pre></div></div>
            
        </li>
        
        
        <li id="section-26">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-26">&#182;</a>
              </div>
              <p>Use <code>Array.prototype.every</code> to terminate iteration early if
<code>fileAppendIdPart</code> returns a falsy value.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    parts.every(<span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">part, i</span>) </span>{
      <span class="hljs-keyword">return</span> file = fileAppendIdPart(file, part, i === parts.length - <span class="hljs-number">1</span>);
    });
    <span class="hljs-keyword">return</span> file;
  };

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileGetRoot</span>(<span class="hljs-params">file</span>) </span>{
    <span class="hljs-keyword">return</span> file &amp;&amp; fileGetRoot(file.p) || file;
  }

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">fileResolve</span>(<span class="hljs-params">file, id</span>) </span>{
    file =</pre></div></div>
            
        </li>
        
        
        <li id="section-27">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-27">&#182;</a>
              </div>
              <p>Absolute module identifiers (i.e. those that begin with a <code>/</code>
character) are interpreted relative to the root directory, which
is a slight deviation from Node, which has access to the entire
file system.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      id.charAt(<span class="hljs-number">0</span>) === <span class="hljs-string">"/"</span> ? fileAppendId(fileGetRoot(file), id) :</pre></div></div>
            
        </li>
        
        
        <li id="section-28">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-28">&#182;</a>
              </div>
              <p>Relative module identifiers are interpreted relative to the
current file, naturally.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      id.charAt(<span class="hljs-number">0</span>) === <span class="hljs-string">"."</span> ? fileAppendId(file, id) :</pre></div></div>
            
        </li>
        
        
        <li id="section-29">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-29">&#182;</a>
              </div>
              <p>Top-level module identifiers are interpreted as referring to
packages in <code>node_modules</code> directories.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      nodeModulesLookup(file, id);</pre></div></div>
            
        </li>
        
        
        <li id="section-30">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-30">&#182;</a>
              </div>
              <p>If the identifier resolves to a directory, we use the same logic as
Node to find an <code>index.js</code> or <code>package.json</code> file to evaluate.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>    <span class="hljs-keyword">while</span> (file &amp;&amp; fileIsDirectory(file)) {</pre></div></div>
            
        </li>
        
        
        <li id="section-31">
            <div class="annotation">
              
              <div class="pilwrap ">
                <a class="pilcrow" href="#section-31">&#182;</a>
              </div>
              <p>If <code>package.json</code> does not exist, <code>fileEvaluate</code> will return the
<code>MISSING</code> object, which has no <code>.main</code> property.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      <span class="hljs-keyword">var</span> pkg = fileEvaluate(fileAppendIdPart(file, <span class="hljs-string">"package.json"</span>));
      file = pkg &amp;&amp; isString(pkg.main) &amp;&amp;
        fileAppendId(file, pkg.main) || <span class="hljs-comment">// Might resolve to another directory!</span>
        fileAppendIdPart(file, <span class="hljs-string">"index.js"</span>);
    }

    <span class="hljs-keyword">return</span> file;
  };

  <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">nodeModulesLookup</span>(<span class="hljs-params">file, id</span>) </span>{
    <span class="hljs-keyword">return</span> fileIsDirectory(file) &amp;&amp;
      fileAppendId(file, <span class="hljs-string">"node_modules/"</span> + id) ||
      (file.p &amp;&amp; nodeModulesLookup(file.p, id));
  }
})(<span class="hljs-string">"object"</span> === <span class="hljs-keyword">typeof</span> global ? global :
   <span class="hljs-string">"object"</span> === <span class="hljs-keyword">typeof</span> <span class="hljs-built_in">window</span> ? <span class="hljs-built_in">window</span> :
   <span class="hljs-string">"object"</span> === <span class="hljs-keyword">typeof</span> self ? self : <span class="hljs-keyword">this</span>);</pre></div></div>
            
        </li>
        
    </ul>
  </div>
</body>
</html>

makeInstaller=function(r){"use strict";function n(r,n){return i(r)&&(l(g,r,n),o(x)&&x(q)),q}function e(r){this.id=r,this.children=[],this.childrenById={}}function t(r,n){return I.call(r,n)&&r[n]}function i(r){return r&&"object"==typeof r}function o(r){return"function"==typeof r}function u(r){return"string"==typeof r}function c(r){function n(n){var e=h(r,n);if(e)return s(e,r.m);var t=new Error("Cannot find module '"+n+"'");if(o(k))return k(n,r.m.id,t);throw t}return o(j)&&(n=j(n,r.m.id)),n.extensions=d(r).slice(0),n.resolve=function(n){var e=h(r,n);if(e)return e.m.id;var t=new Error("Cannot find module '"+n+"'");if(k&&o(k.resolve))return k.resolve(n,r.m.id,t);throw t},n}function f(r,n){var t=this;t.p=n=n||null,t.m=new e(r)}function s(r,n){var e=r&&r.c,t=r.m;if(!I.call(t,"exports")){if(n){t.parent=n;var i=n.children;Array.isArray(i)&&i.push(t)}o(t.useNode)&&t.useNode()||e(t.require=t.require||c(r),t.exports={},t,r.m.id,r.p.m.id),t.loaded=!0}return o(t.runModuleSetters)&&t.runModuleSetters(),t.exports}function a(r){return r&&i(r.c)}function l(r,n,e){if(Array.isArray(n)){var c=[];n.forEach(function(r){u(r)?c.push(r):o(r)&&(n=r)}),o(n)?n.d=c:n=null}else o(n)?n.d=n.d||[]:u(n)||i(n)||(n=null);n&&(r.c=r.c||(i(n)?{}:n),i(n)&&a(r)&&Object.keys(n).forEach(function(i){if(".."===i)o=r.p;else{var o=t(r.c,i);o||(o=r.c[i]=new f(r.m.id.replace(/\/*$/,"/")+i,r),o.o=e)}l(o,n[i],e)}))}function d(r){return r.o&&r.o.extensions||w}function p(r,n,e){for(;r&&!a(r);)r=r.p;if(!r||!n||"."===n)return r;if(".."===n)return r.p;var i=t(r.c,n);if(e&&(!i||a(i)))for(var o=0;o<e.length;++o){var u=t(r.c,n+e[o]);if(u&&!a(u))return u}return i}function v(r,n,e){var t=n.split("/");return t.every(function(n,i){return r=i<t.length-1?p(r,n):p(r,n,e)}),r}function m(r,n){var e=n&&n.m;r&&e&&(r.childrenById[e.id]=e)}function h(r,n,e,t){var e=e||r.m,i=d(r);for(r="/"===n.charAt(0)?v(g,n,i):"."===n.charAt(0)?v(r,n,i):y(r,n,i);a(r);){if(t=t||[],t.indexOf(r)<0){t.push(r);var o,c=p(r,"package.json"),f=c&&s(c,e);if(f&&A.some(function(r){return u(o=f[r])})&&(m(e,c),r=v(r,o,i)||h(r,o,e,t)))continue}r=p(r,"index.js")}return r&&u(r.c)&&(r=h(r,r.c,e,t)),m(e,r),r}function y(r,n,e){if(o(b)&&(n=b(n,r.m.id)),u(n)){for(var t;r&&!t;r=r.p)t=a(r)&&v(r,"node_modules/"+n,e);return t}}r=r||{};var w=r.extensions||[".js",".json"],x=r.onInstall,j=r.wrapRequire,b=r.override,k=r.fallback,A=r.mainFields||(r.browser?["browser","main"]:["main"]),I={}.hasOwnProperty,g=new f("/",new f("/..")),q=c(g);return e.prototype.resolve=function(r){return this.require.resolve(r)},n.Module=e,n},"object"==typeof exports&&(exports.makeInstaller=makeInstaller);
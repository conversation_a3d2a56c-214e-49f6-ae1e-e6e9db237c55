{"rules": {"class-name": true, "comment-format": [true, "check-space"], "indent": [true, "spaces"], "one-line": [true, "check-open-brace"], "quotemark": [true, "double"], "semicolon": true, "whitespace": [true, "check-branch", "check-operator", "check-separator", "check-type"], "strict-null-checks": false, "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}, {"call-signature": "onespace", "index-signature": "onespace", "parameter": "onespace", "property-declaration": "onespace", "variable-declaration": "onespace"}]}}
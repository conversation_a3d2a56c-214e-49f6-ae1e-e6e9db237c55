{"name": "cordova-plugin-code-push", "version": "1.13.1", "description": "CodePush Plugin for Apache Cordova", "cordova": {"id": "cordova-plugin-code-push", "platforms": ["android", "ios"]}, "repository": {"type": "git", "url": "https://github.com/Microsoft/cordova-plugin-code-push"}, "keywords": ["<PERSON><PERSON>", "code", "push", "ecosystem:cordova", "cordova-android", "cordova-ios"], "author": "Microsoft Corporation", "license": "MIT", "types": "./typings/codePush.d.ts", "devDependencies": {"@types/assert": "^1.4.2", "@types/cordova": "0.0.34", "@types/cordova-plugin-device": "^1.1.5", "@types/cordova-plugin-dialogs": "^1.3.2", "@types/cordova-plugin-file": "^0.0.3", "@types/mkdirp": "^0.5.2", "@types/mocha": "^5.2.7", "@types/node": "^12.6.3", "@types/power-assert": "^1.5.0", "@types/q": "^1.5.2", "archiver": "^3.0.0", "body-parser": "^1.19.0", "del": "^5.0.0", "express": "^4.17.1", "gulp": "^4.0.2", "gulp-insert": "^0.5.0", "gulp-tslint": "^8.1.4", "gulp-typescript": "^5.0.1", "mkdirp": "^0.5.1", "mocha": "^6.1.4", "mocha-junit-reporter": "^1.23.1", "q": "^1.5.1", "replace": "^1.2.0", "run-sequence": "^2.2.1", "tslint": "^5.18.0", "typescript": "^3.5.3"}}
<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="315.579" tests="36" failures="1">
  <testsuite name="Root Suite" timestamp="2020-07-24T17:32:19" tests="0" failures="0" time="0">
  </testsuite>
  <testsuite name="CodePush Cordova Plugin" timestamp="2020-07-24T17:32:19" tests="0" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="0">
  </testsuite>
  <testsuite name="Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView" timestamp="2020-07-24T17:32:19" tests="0" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="0">
  </testsuite>
  <testsuite name="window.codePush" timestamp="2020-07-24T17:32:19" tests="0" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="0">
  </testsuite>
  <testsuite name="#window.codePush.checkForUpdate" timestamp="2020-07-24T17:32:21" tests="5" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="24.447">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.checkForUpdate window.codePush.checkForUpdate.noUpdate" time="6.143" classname="window.codePush.checkForUpdate.noUpdate">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.checkForUpdate window.codePush.checkForUpdate.sendsBinaryHash" time="5.275" classname="window.codePush.checkForUpdate.sendsBinaryHash">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.checkForUpdate window.codePush.checkForUpdate.noUpdate.updateAppVersion" time="4.93" classname="window.codePush.checkForUpdate.noUpdate.updateAppVersion">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.checkForUpdate window.codePush.checkForUpdate.update" time="3.486" classname="window.codePush.checkForUpdate.update">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.checkForUpdate window.codePush.checkForUpdate.error" time="4.613" classname="window.codePush.checkForUpdate.error">
    </testcase>
  </testsuite>
  <testsuite name="#window.codePush.checkForUpdate.customKey" timestamp="2020-07-24T17:32:58" tests="1" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="5.893">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.checkForUpdate.customKey window.codePush.checkForUpdate.customKey.update" time="5.893" classname="window.codePush.checkForUpdate.customKey.update">
    </testcase>
  </testsuite>
  <testsuite name="#remotePackage.download" timestamp="2020-07-24T17:33:11" tests="2" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="7.959">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #remotePackage.download remotePackage.download.success" time="4.756" classname="remotePackage.download.success">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #remotePackage.download remotePackage.download.error" time="3.203" classname="remotePackage.download.error">
    </testcase>
  </testsuite>
  <testsuite name="#localPackage.install" timestamp="2020-07-24T17:33:24" tests="3" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="30.840999999999998">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.install localPackage.install.unzip.error" time="5.842" classname="localPackage.install.unzip.error">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.install localPackage.install.handlesDiff.againstBinary" time="8.081" classname="localPackage.install.handlesDiff.againstBinary">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.install localPackage.install.immediately" time="16.918" classname="localPackage.install.immediately">
    </testcase>
  </testsuite>
  <testsuite name="#localPackage.install.revert" timestamp="2020-07-24T17:34:02" tests="2" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="20.407">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.install.revert localPackage.install.revert.dorevert" time="13.227" classname="localPackage.install.revert.dorevert">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.install.revert localPackage.install.revert.norevert" time="7.18" classname="localPackage.install.revert.norevert">
    </testcase>
  </testsuite>
  <testsuite name="#localPackage.installOnNextResume" timestamp="2020-07-24T17:34:29" tests="2" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="18.962">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.installOnNextResume localPackage.installOnNextResume.dorevert" time="11.014" classname="localPackage.installOnNextResume.dorevert">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.installOnNextResume localPackage.installOnNextResume.norevert" time="7.948" classname="localPackage.installOnNextResume.norevert">
    </testcase>
  </testsuite>
  <testsuite name="#localPackage.installOnNextRestart" timestamp="2020-07-24T17:34:55" tests="3" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="47.078">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.installOnNextRestart localPackage.installOnNextRestart.dorevert" time="12.491" classname="localPackage.installOnNextRestart.dorevert">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.installOnNextRestart localPackage.installOnNextRestart.norevert" time="10.064" classname="localPackage.installOnNextRestart.norevert">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.installOnNextRestart localPackage.installOnNextRestart.revertToPrevious" time="24.523" classname="localPackage.installOnNextRestart.revertToPrevious">
    </testcase>
  </testsuite>
  <testsuite name="#localPackage.installOnNextRestart2x" timestamp="2020-07-24T17:35:49" tests="1" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="11.928">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #localPackage.installOnNextRestart2x localPackage.installOnNextRestart2x.revertToFirst" time="11.928" classname="localPackage.installOnNextRestart2x.revertToFirst">
    </testcase>
  </testsuite>
  <testsuite name="#codePush.restartApplication" timestamp="2020-07-24T17:36:08" tests="1" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="9.84">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #codePush.restartApplication codePush.restartApplication.checkPackages" time="9.84" classname="codePush.restartApplication.checkPackages">
    </testcase>
  </testsuite>
  <testsuite name="#window.codePush.sync" timestamp="2020-07-24T17:36:26" tests="0" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="0">
  </testsuite>
  <testsuite name="#window.codePush.sync 1x" timestamp="2020-07-24T17:36:26" tests="5" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="25.425000000000004">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 1x window.codePush.sync.noupdate" time="4.871" classname="window.codePush.sync.noupdate">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 1x window.codePush.sync.checkerror" time="3.526" classname="window.codePush.sync.checkerror">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 1x window.codePush.sync.downloaderror" time="3.041" classname="window.codePush.sync.downloaderror">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 1x window.codePush.sync.dorevert" time="6.683" classname="window.codePush.sync.dorevert">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 1x window.codePush.sync.update" time="7.304" classname="window.codePush.sync.update">
    </testcase>
  </testsuite>
  <testsuite name="#window.codePush.sync 2x" timestamp="2020-07-24T17:36:58" tests="5" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="1" time="24.673000000000002">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 2x window.codePush.sync.2x.noupdate" time="5.055" classname="window.codePush.sync.2x.noupdate">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 2x window.codePush.sync.2x.checkerror" time="3.221" classname="window.codePush.sync.2x.checkerror">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 2x window.codePush.sync.2x.downloaderror" time="3.103" classname="window.codePush.sync.2x.downloaderror">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 2x window.codePush.sync.2x.dorevert" time="6.7" classname="window.codePush.sync.2x.dorevert">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync 2x window.codePush.sync.2x.update" time="6.594" classname="window.codePush.sync.2x.update">
      <failure message="false == true" type="AssertionError [ERR_ASSERTION]"><![CDATA[AssertionError [ERR_ASSERTION]: false == true
    at /Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js:135:17
    at /Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js:181:36
    at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
    at next (node_modules/express/lib/router/route.js:137:13)
    at Route.dispatch (node_modules/express/lib/router/route.js:112:3)
    at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/repos/cordova-plugin-code-push/node_modules/express/lib/router/index.js:281:22
    at Function.process_params (node_modules/express/lib/router/index.js:335:12)
    at next (node_modules/express/lib/router/index.js:275:10)
    at /Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js:157:13
    at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (node_modules/express/lib/router/index.js:317:13)
    at /Users/<USER>/Documents/repos/cordova-plugin-code-push/node_modules/express/lib/router/index.js:284:7
    at Function.process_params (node_modules/express/lib/router/index.js:335:12)
    at next (node_modules/express/lib/router/index.js:275:10)
    at urlencodedParser (node_modules/body-parser/lib/types/urlencoded.js:82:7)
    at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (node_modules/express/lib/router/index.js:317:13)
    at /Users/<USER>/Documents/repos/cordova-plugin-code-push/node_modules/express/lib/router/index.js:284:7
    at Function.process_params (node_modules/express/lib/router/index.js:335:12)
    at next (node_modules/express/lib/router/index.js:275:10)
    at /Users/<USER>/Documents/repos/cordova-plugin-code-push/node_modules/body-parser/lib/read.js:130:5
    at invokeCallback (node_modules/raw-body/index.js:224:16)
    at done (node_modules/raw-body/index.js:213:7)
    at IncomingMessage.onEnd (node_modules/raw-body/index.js:273:7)
    at endReadableNT (_stream_readable.js:1064:12)
    at _combinedTickCallback (internal/process/next_tick.js:139:11)
    at process._tickCallback (internal/process/next_tick.js:181:9)]]></failure>
    </testcase>
  </testsuite>
  <testsuite name="#window.codePush.sync minimum background duration tests" timestamp="2020-07-24T17:37:29" tests="3" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="46.172000000000004">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync minimum background duration tests defaults to no minimum" time="11.766" classname="defaults to no minimum">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync minimum background duration tests min background duration 5s" time="21.513" classname="min background duration 5s">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync minimum background duration tests has no effect on restart" time="12.893" classname="has no effect on restart">
    </testcase>
  </testsuite>
  <testsuite name="#window.codePush.sync mandatory install mode tests" timestamp="2020-07-24T17:38:16" tests="3" file="/Users/<USER>/Documents/repos/cordova-plugin-code-push/bin/test/test.js" failures="0" time="41.954">
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync mandatory install mode tests defaults to IMMEDIATE" time="11.768" classname="defaults to IMMEDIATE">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync mandatory install mode tests works correctly when update is mandatory and mandatory install mode is specified" time="18.909" classname="works correctly when update is mandatory and mandatory install mode is specified">
    </testcase>
    <testcase name="CodePush Cordova Plugin Tests /Users/<USER>/Documents/repos/cordova-plugin-code-push on ios with UIWebView window.codePush #window.codePush.sync #window.codePush.sync mandatory install mode tests has no effect on updates that are not mandatory" time="11.277" classname="has no effect on updates that are not mandatory">
    </testcase>
  </testsuite>
</testsuites>
﻿<?xml version="1.0" encoding="UTF-8" ?>
    <plugin xmlns="http://apache.org/cordova/ns/plugins/1.0" id="cordova-plugin-code-push" version="1.13.1">
        <name>CodePush</name>
        <description>This plugin allows you to push code updates to your apps instantly using the CodePush service</description>
        <license>MIT</license>
        <keywords>cordova,code,push</keywords>
        <repo>https://github.com/Microsoft/cordova-plugin-code-push.git</repo>

        <dependency id="code-push" version="3.0.1" />
        <dependency id="cordova-plugin-dialogs" version=">=1.1.1" />
        <dependency id="cordova-plugin-device" version=">=1.1.0" />
        <dependency id="cordova-plugin-file" version=">=6.0.1" />
        <dependency id="cordova-plugin-advanced-http" version=">=2.2.0" />
        <dependency id="cordova-plugin-zip" version=">=3.1.0" />

        <js-module src="bin/www/codePush.js" name="codePush">
            <clobbers target="codePush" />
        </js-module>

        <js-module src="bin/www/localPackage.js" name="localPackage">
            <clobbers target="LocalPackage" />
        </js-module>

        <js-module src="bin/www/remotePackage.js" name="remotePackage">
            <clobbers target="RemotePackage" />
        </js-module>

        <js-module src="bin/www/syncStatus.js" name="syncStatus">
            <clobbers target="SyncStatus" />
        </js-module>

        <js-module src="bin/www/installMode.js" name="installMode">
            <clobbers target="InstallMode" />
        </js-module>

        <js-module src="bin/www/codePushUtil.js" name="codePushUtil">
            <runs/>
        </js-module>

        <js-module src="bin/www/fileUtil.js" name="fileUtil">
            <runs/>
        </js-module>

        <js-module src="bin/www/httpRequester.js" name="httpRequester">
            <runs/>
        </js-module>

        <js-module src="bin/www/nativeAppInfo.js" name="nativeAppInfo">
            <runs/>
        </js-module>

        <js-module src="bin/www/package.js" name="package">
            <runs/>
        </js-module>

        <js-module src="bin/www/sdk.js" name="sdk">
            <runs/>
        </js-module>

        <platform name="android">
            <framework src="src/android/build-extras.gradle" custom="true" type="gradleReference" />
            <source-file src="src/android/CodePush.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/CodePushException.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/CodePushPackageManager.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/CodePushPackageMetadata.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/CodePushPreferences.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/CodePushReportingManager.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/InstallMode.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/InstallOptions.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/ReportingStatus.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/StatusReport.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/UpdateHashUtils.java" target-dir="src/com/microsoft/cordova" />
            <source-file src="src/android/Utilities.java" target-dir="src/com/microsoft/cordova" />
            <config-file target="config.xml" parent="/*">
                <feature name="CodePush">
                    <param name="android-package" value="com.microsoft.cordova.CodePush" />
                    <param name="onload" value="true" />
                </feature>
            </config-file>
        </platform>

        <platform name="ios">
            <header-file src="src/ios/CodePush.h" />
            <source-file src="src/ios/CodePush.m" />
            <header-file src="src/ios/CodePushPackageMetadata.h" />
            <source-file src="src/ios/CodePushPackageMetadata.m" />
            <header-file src="src/ios/CodePushPackageManager.h" />
            <source-file src="src/ios/CodePushPackageManager.m" />
            <header-file src="src/ios/CodePushReportingManager.h" />
            <source-file src="src/ios/CodePushReportingManager.m" />
            <header-file src="src/ios/InstallOptions.h" />
            <source-file src="src/ios/InstallOptions.m" />
            <header-file src="src/ios/InstallMode.h" />
            <header-file src="src/ios/StatusReport.h" />
            <source-file src="src/ios/StatusReport.m" />
            <header-file src="src/ios/UpdateHashUtils.h" />
            <source-file src="src/ios/UpdateHashUtils.m" />
            <header-file src="src/ios/Utilities.h" />
            <source-file src="src/ios/Utilities.m" />
            <source-file src="src/ios/CDVWKWebViewEngine+CodePush.m" />

            <!-- JWT -->
            <source-file src="src/ios/JWT/Core/Algorithms/Base/CodePushJWTAlgorithmFactory.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/Base/CodePushJWTAlgorithmNone.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/ESFamily/CodePushJWTAlgorithmESBase.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/Holders/CodePushJWTAlgorithmDataHolder.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/Holders/CodePushJWTAlgorithmDataHolderChain.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/HSFamily/CodePushJWTAlgorithmHSBase.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/RSFamily/CodePushJWTAlgorithmRSBase.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/RSFamily/RSKeys/CodePushJWTCryptoKey.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/RSFamily/RSKeys/CodePushJWTCryptoKeyExtractor.m" />
            <source-file src="src/ios/JWT/Core/Algorithms/RSFamily/RSKeys/CodePushJWTCryptoSecurity.m" />
            <source-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaim.m" />
            <source-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaimsSet.m" />
            <source-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaimsSetSerializer.m" />
            <source-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaimsSetVerifier.m" />
            <source-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+ResultTypes.m" />
            <source-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+VersionOne.m" />
            <source-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+VersionThree.m" />
            <source-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+VersionTwo.m" />
            <source-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding.m" />
            <source-file src="src/ios/JWT/Core/Supplement/CodePushJWTBase64Coder.m" />
            <source-file src="src/ios/JWT/Core/Supplement/CodePushJWTErrorDescription.m" />
            <header-file src="src/ios/JWT/Core/Algorithms/Base/CodePushJWTAlgorithm.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/Base/CodePushJWTAlgorithmFactory.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/Base/CodePushJWTAlgorithmNone.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/ESFamily/CodePushJWTAlgorithmESBase.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/Holders/CodePushJWTAlgorithmDataHolder.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/Holders/CodePushJWTAlgorithmDataHolderChain.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/HSFamily/CodePushJWTAlgorithmHSBase.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/RSFamily/CodePushJWTAlgorithmRSBase.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/RSFamily/CodePushJWTRSAlgorithm.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/RSFamily/RSKeys/CodePushJWTCryptoKey.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/RSFamily/RSKeys/CodePushJWTCryptoKeyExtractor.h" />
            <header-file src="src/ios/JWT/Core/Algorithms/RSFamily/RSKeys/CodePushJWTCryptoSecurity.h" />
            <header-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaim.h" />
            <header-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaimsSet.h" />
            <header-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaimsSetSerializer.h" />
            <header-file src="src/ios/JWT/Core/ClaimSet/CodePushJWTClaimsSetVerifier.h" />
            <header-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+ResultTypes.h" />
            <header-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+VersionOne.h" />
            <header-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+VersionThree.h" />
            <header-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding+VersionTwo.h" />
            <header-file src="src/ios/JWT/Core/Coding/CodePushJWTCoding.h" />
            <header-file src="src/ios/JWT/Core/FrameworkSupplement/CodePushJWT.h" />
            <header-file src="src/ios/JWT/Core/Supplement/CodePushJWTBase64Coder.h" />
            <header-file src="src/ios/JWT/Core/Supplement/CodePushJWTDeprecations.h" />
            <header-file src="src/ios/JWT/Core/Supplement/CodePushJWTErrorDescription.h" />

            <!-- Base64 -->
            <source-file src="src/ios/Base64/CodePushMF_Base64Additions.m" />
            <header-file src="src/ios/Base64/CodePushMF_Base64Additions.h" />

            <config-file target="config.xml" parent="/*">
                <feature name="CodePush">
                    <param name="ios-package" value="CodePush" />
                    <param name="onload" value="true" />
                </feature>
            </config-file>
        </platform>
    </plugin>

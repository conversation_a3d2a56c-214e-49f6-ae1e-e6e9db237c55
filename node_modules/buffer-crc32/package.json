{"_from": "buffer-crc32@~0.2.3", "_id": "buffer-crc32@0.2.13", "_inBundle": false, "_integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=", "_location": "/buffer-crc32", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "buffer-crc32@~0.2.3", "name": "buffer-crc32", "escapedName": "buffer-crc32", "rawSpec": "~0.2.3", "saveSpec": null, "fetchSpec": "~0.2.3"}, "_requiredBy": ["/yazl"], "_resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "_shasum": "0d333e3f00eac50aa1454abd30ef8c2a5d9a7242", "_spec": "buffer-crc32@~0.2.3", "_where": "D:\\projects\\soil-samples-app\\node_modules\\yazl", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}], "dependencies": {}, "deprecated": false, "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "devDependencies": {"tap": "~0.2.5"}, "engines": {"node": "*"}, "files": ["index.js"], "homepage": "https://github.com/brianloveswords/buffer-crc32", "license": "MIT", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "main": "index.js", "name": "buffer-crc32", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js"}, "version": "0.2.13"}
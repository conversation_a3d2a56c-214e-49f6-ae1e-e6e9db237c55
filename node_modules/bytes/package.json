{"_from": "bytes@3.0.0", "_id": "bytes@3.0.0", "_inBundle": false, "_integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=", "_location": "/bytes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bytes@3.0.0", "name": "bytes", "escapedName": "bytes", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/raw-body"], "_resolved": "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz", "_shasum": "d32815404d689699f85a4ea4fa8755dd13a96048", "_spec": "bytes@3.0.0", "_where": "D:\\projects\\soil-samples-app\\node_modules\\raw-body", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "deprecated": false, "description": "Utility to parse a string bytes to bytes and vice-versa", "devDependencies": {"mocha": "2.5.3", "nyc": "10.3.2"}, "engines": {"node": ">= 0.8"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "homepage": "https://github.com/visionmedia/bytes.js#readme", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "license": "MIT", "name": "bytes", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "scripts": {"test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "3.0.0"}
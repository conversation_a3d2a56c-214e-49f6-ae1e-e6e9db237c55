{"version": 3, "sources": ["test/management-sdk.ts"], "names": ["testErrors", "<PERSON><PERSON><PERSON><PERSON>", "mockReturn"], "mappings": "AAAA,oDAAoD;AAEpD,IAAY,MAAM,WAAM,QAAQ,CAAC,CAAA;AACjC,IAAY,CAAC,WAAM,GAAG,CAAC,CAAA;AAEvB,IAAO,cAAc,WAAW,0BAA0B,CAAC,CAAC;AAE5D,IAAI,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAEpC,IAAI,OAAuB,CAAC;AAC5B,QAAQ,CAAC,gBAAgB,EAAE;IAEvB,UAAU,CAAC;QACP,OAAO,GAAG,IAAI,cAAc,CAAgB,gBAAgB,EAAqB,IAAI,EAAiB,kBAAkB,CAAC,CAAC;IAC9H,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC;QACF,AACA,iFADiF;QACjF,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,cAAa,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uEAAuE,EAAE,UAAC,IAAe;QACxF,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAExB,IAAI,wBAAwB,GAAU;YAClC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC;YAC/D,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;YACvC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;YAC9C,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;YAC1C,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;YAEtD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,CAAC;YAChE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,CAAC;YAChE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;YAC/C,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC;YAClG,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,CAAC;YAEnE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;YAC1D,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;YACjD,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;YAE7D,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;YAC3G,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;YAClH,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,oBAAoB,CAAC;SACpF,CAAC;QAEF,IAAI,MAAM,GAAG,CAAC,CAAO,IAAI,CAAC,CAAC;QAC3B,wBAAwB,CAAC,OAAO,CAAC,UAAS,CAAC;YACvC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC;gBACjB,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC;YACR,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,AACA,+EAD+E;4BAC3D,MAAW;YAC3BA,MAAMA,CAACA,CAACA,CAACA,OAAOA,CAAOA,UAACA,OAAYA,EAAEA,MAAWA,EAAEA,MAAWA;gBAC1DA,MAAMA,EAAEA,CAACA,IAAIA,CAACA;oBACVA,MAAMA,CAACA,IAAIA,CAACA,6BAA6BA,CAACA,CAACA;oBAC3CA,MAAMA,EAAEA,CAACA;gBACbA,CAACA,EAAEA,UAACA,KAAUA;oBACVA,MAAMA,CAACA,KAAKA,CAACA,KAAKA,CAACA,OAAOA,EAAEA,MAAMA,CAACA,CAACA;oBACpCA,MAAMA,CAACA,KAAKA,CAACA,UAAUA,CAACA,CAACA;oBACzBA,OAAOA,EAAEA,CAACA;gBACdA,CAACA,CAACA,CAACA;YACPA,CAACA,CAACA,CAACA;QACPA,CAACA;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,UAAC,IAAe;QAC1D,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAC7D,OAAO,CAAC,eAAe,EAAE;aACpB,IAAI,CAAC,UAAC,aAAsB;YACzB,MAAM,CAAC,aAAa,EAAE,yBAAyB,CAAC,CAAC;YACjD,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE,UAAC,IAAe;QAC5D,UAAU,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QACpC,OAAO,CAAC,eAAe,EAAE;aACpB,IAAI,CAAC,UAAC,aAAsB;YACzB,MAAM,CAAC,CAAC,aAAa,EAAE,6BAA6B,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kEAAkE,EAAE,UAAC,IAAe;QACnF,UAAU,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAEpC,AACA,kFADkF;QAClF,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;aACxB,IAAI,CAAC,UAAC,aAAsB;YACzB,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YAChE,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,UAAC,GAAG;YACH,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,wCAAwC,CAAC,CAAC;YACpF,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iDAAiD,EAAE,UAAC,IAAe;QAClE,UAAU,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QACjC,OAAO,CAAC,eAAe,EAAE;aACpB,IAAI,CAAC,UAAC,aAAsB;YACzB,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YAChE,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,UAAC,GAAG;YACH,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,qCAAqC,CAAC,CAAC;YAC9E,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE,UAAC,IAAe;QACrD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;QAC7E,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,cAAc,CAAC;aAC3C,IAAI,CAAC,UAAC,GAAG;YACN,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+BAA+B,EAAE,UAAC,IAAe;QAChD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QACxD,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,cAAc,CAAC;aAC3C,IAAI,CAAC,UAAC,GAAG;YACN,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC,EAAE,UAAC,KAAY,IAAK,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8BAA8B,EAAE,UAAC,IAAe;QAC/C,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAEjD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;aACpB,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE,UAAC,IAAe;QACrD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAElD,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC;aACrC,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE,UAAC,IAAe;QACrD,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAExB,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;aACvB,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,UAAC,IAAe;QAC1D,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACpB,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC;aACnC,IAAI,CAAC,UAAC,GAAG;YACN,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE,UAAC,IAAe;QACzD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,iBAAiB,EAAE,CAAC,CAAC;QAE/G,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,gBAAgB,CAAC;aAC7C,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE,UAAC,IAAe;QACtD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAExD,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,gBAAgB,CAAC;aAC7C,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,UAAC,IAAe;QACvD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAEzD,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC;aAC5B,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE,UAAC,IAAe;QAC5D,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAElD,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;aACrE,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE,UAAC,IAAe;QAC5D,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAExB,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,CAAC;aAChD,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gEAAgE,EAAE,UAAC,IAAe;QACjF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAEjD,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,gBAAgB,CAAC;aACpD,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC5B,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iEAAiE,EAAE,UAAC,IAAe;QAClF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAEjF,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,gBAAgB,CAAC;aACpD,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACjC,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,UAAC,IAAe;QAC9D,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,gBAAgB,CAAC;aACpD,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC,EAAE,UAAC,KAAY,IAAK,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iDAAiD,EAAE,UAAC,IAAe;QAClE,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,sBAAsB,CAAC,SAAS,EAAE,gBAAgB,CAAC;aACtD,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+CAA+C,EAAE,UAAC,IAAe;QAChE,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,sBAAsB,CAAC,SAAS,EAAE,gBAAgB,CAAC;aACtD,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC,EAAE,UAAC,KAAY,IAAK,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,UAAC,IAAe;QAC9D,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC,CAAC;QACpD,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC;aACvC,IAAI,CAAC,UAAC,GAAG;YACN,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE,UAAC,IAAe;QACzD,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QACxB,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC;aACvC,IAAI,CAAC,UAAC,GAAG;YACN,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC,EAAE,UAAC,KAAY,IAAK,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iEAAiE,EAAE,UAAC,IAAe;QAClF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAEvD,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC;aAC9B,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzC,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uEAAuE,EAAE,UAAC,IAAe;QACxF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;YACtB,aAAa,EAAE;gBACX,QAAQ,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE;gBACzD,QAAQ,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,KAAK,EAAE;aACpE;SACJ,CAAC,EAAE,GAAG,CAAC,CAAC;QAET,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC;aAC9B,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACvD,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,UAAC,IAAe;QAC9D,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAExB,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC;aAC1C,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uCAAuC,EAAE,UAAC,IAAe;QACxD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAEhF,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;aACxF,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE,UAAC,IAAe;QACtD,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,EAAE,EAAE,CAAC;aACzD,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC,EAAE,UAAC,KAAY,IAAK,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE,UAAC,IAAe;QACnD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAEhF,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;aAC/F,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;YAC/C,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gCAAgC,EAAE,UAAC,IAAe;QACjD,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;aAC9E,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC,EAAE,UAAC,KAAY,IAAK,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mCAAmC,EAAE,UAAC,IAAe;QACpD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAE9D,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC;aAC9C,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACX,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,UAAC,IAAe;QAClD,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpB,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC;aAC9C,IAAI,CAAC,UAAC,GAAQ;YACX,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC,EAAE,UAAC,KAAY,IAAK,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AAEH,AACA,6FAD6F;uBACtE,GAAQ;IAC3BC,MAAMA,CAACA,IAAIA,EAAEA,CAACA;AAClBA,CAACA;AAED,AACA,uFADuF;oBACnE,QAAgB,EAAE,UAAkB,EAAE,MAAW;IAAXC,sBAAWA,GAAXA,WAAWA;IACjEA,OAAOA,CAACA,iBAAiBA,CAACA,CAACA,OAAOA,EAAEA,CAACA;YACjCA,OAAOA,EAAEA,2BAA2BA;YACpCA,QAAQA,EAAEA,UAASA,KAAUA,EAAEA,MAAWA;gBACtC,IAAI,IAAI,GAAG,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,CAAC;gBACjD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACR,IAAI,GAAG,GAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACnC,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC;oBACxB,MAAM,GAAG,CAAC;gBACd,CAAC;gBACD,MAAM,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;YACzF,CAAC;YACDA,QAAQA,EAAEA,UAASA,KAAUA,EAAEA,IAASA,IAAS,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;SAClEA,CAACA,CAACA,CAACA;AACRA,CAACA", "file": "management-sdk.js", "sourcesContent": ["/// <reference path=\"../definitions/harness.d.ts\" />\n\nimport * as assert from \"assert\";\nimport * as Q from \"q\";\n\nimport AccountManager = require(\"../script/management-sdk\");\n\nvar request = require(\"superagent\");\n\nvar manager: AccountManager;\ndescribe(\"Management SDK\", () => {\n\n    beforeEach(() => {\n        manager = new AccountManager(/*accessKey=*/ \"dummyAccessKey\", /*customHeaders=*/ null, /*serverUrl=*/ \"http://localhost\");\n    });\n\n    after(() => {\n        // Prevent an exception that occurs due to how superagent-mock overwrites methods\n        request.Request.prototype._callback = function() { };\n    });\n\n    it(\"methods reject the promise with status code info when an error occurs\", (done: MochaDone) => {\n        mockReturn(\"Text\", 404);\n\n        var methodsWithErrorHandling: any[] = [\n            manager.addApp.bind(manager, \"appName\", \"appOs\", \"appPlatform\"),\n            manager.getApp.bind(manager, \"appName\"),\n            manager.renameApp.bind(manager, \"appName\", {}),\n            manager.removeApp.bind(manager, \"appName\"),\n            manager.transferApp.bind(manager, \"appName\", \"email1\"),\n\n            manager.addDeployment.bind(manager, \"appName\", \"deploymentName\"),\n            manager.getDeployment.bind(manager, \"appName\", \"deploymentName\"),\n            manager.getDeployments.bind(manager, \"appName\"),\n            manager.renameDeployment.bind(manager, \"appName\", \"deploymentName\", { name: \"newDeploymentName\" }),\n            manager.removeDeployment.bind(manager, \"appName\", \"deploymentName\"),\n\n            manager.addCollaborator.bind(manager, \"appName\", \"email1\"),\n            manager.getCollaborators.bind(manager, \"appName\"),\n            manager.removeCollaborator.bind(manager, \"appName\", \"email1\"),\n\n            manager.patchRelease.bind(manager, \"appName\", \"deploymentName\", \"label\", { description: \"newDescription\" }),\n            manager.promote.bind(manager, \"appName\", \"deploymentName\", \"newDeploymentName\", { description: \"newDescription\" }),\n            manager.rollback.bind(manager, \"appName\", \"deploymentName\", \"targetReleaseLabel\")\n        ];\n\n        var result = Q<void>(null);\n        methodsWithErrorHandling.forEach(function(f) {\n            result = result.then(() => {\n                return testErrors(f);\n            });\n        });\n\n        result.done(() => {\n            done();\n        });\n\n        // Test that the proper error code and text is passed through on a server error\n        function testErrors(method: any): Q.Promise<void> {\n            return Q.Promise<void>((resolve: any, reject: any, notify: any) => {\n                method().done(() => {\n                    assert.fail(\"Should have thrown an error\");\n                    reject();\n                }, (error: any) => {\n                    assert.equal(error.message, \"Text\");\n                    assert(error.statusCode);\n                    resolve();\n                });\n            });\n        }\n    });\n\n    it(\"isAuthenticated handles successful auth\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ authenticated: true }), 200, {});\n        manager.isAuthenticated()\n            .done((authenticated: boolean) => {\n                assert(authenticated, \"Should be authenticated\");\n                done();\n            });\n    });\n\n    it(\"isAuthenticated handles unsuccessful auth\", (done: MochaDone) => {\n        mockReturn(\"Unauthorized\", 401, {});\n        manager.isAuthenticated()\n            .done((authenticated: boolean) => {\n                assert(!authenticated, \"Should not be authenticated\");\n                done();\n            });\n    });\n\n    it(\"isAuthenticated handles unsuccessful auth with promise rejection\", (done: MochaDone) => {\n        mockReturn(\"Unauthorized\", 401, {});\n\n        // use optional parameter to ask for rejection of the promise if not authenticated\n        manager.isAuthenticated(true)\n            .done((authenticated: boolean) => {\n                assert.fail(\"isAuthenticated should have rejected the promise\");\n                done();\n            }, (err) => {\n                assert.equal(err.message, \"Unauthorized\", \"Error message should be 'Unauthorized'\");\n                done();\n            });\n    });\n\n    it(\"isAuthenticated handles unexpected status codes\", (done: MochaDone) => {\n        mockReturn(\"Not Found\", 404, {});\n        manager.isAuthenticated()\n            .done((authenticated: boolean) => {\n                assert.fail(\"isAuthenticated should have rejected the promise\");\n                done();\n            }, (err) => {\n                assert.equal(err.message, \"Not Found\", \"Error message should be 'Not Found'\");\n                done();\n            });\n    });\n\n    it(\"addApp handles successful response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ success: true }), 201, { location: \"/appName\" });\n        manager.addApp(\"appName\", \"iOS\", \"React-Native\")\n            .done((obj) => {\n                assert.ok(obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"addApp handles error response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ success: false }), 404, {});\n        manager.addApp(\"appName\", \"iOS\", \"React-Native\")\n            .done((obj) => {\n                throw new Error(\"Call should not complete successfully\");\n            }, (error: Error) => done());\n    });\n\n    it(\"getApp handles JSON response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ app: {} }), 200, {});\n\n        manager.getApp(\"appName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"updateApp handles success response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ apps: [] }), 200, {});\n\n        manager.renameApp(\"appName\", \"newAppName\")\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"removeApp handles success response\", (done: MochaDone) => {\n        mockReturn(\"\", 200, {});\n\n        manager.removeApp(\"appName\")\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"transferApp handles successful response\", (done: MochaDone) => {\n        mockReturn(\"\", 201);\n        manager.transferApp(\"appName\", \"email1\")\n            .done((obj) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"addDeployment handles success response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ deployment: { name: \"name\", key: \"key\" } }), 201, { location: \"/deploymentName\" });\n\n        manager.addDeployment(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"getDeployment handles JSON response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ deployment: {} }), 200, {});\n\n        manager.getDeployment(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"getDeployments handles JSON response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ deployments: [] }), 200, {});\n\n        manager.getDeployments(\"appName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"renameDeployment handles success response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ apps: [] }), 200, {});\n\n        manager.renameDeployment(\"appName\", \"deploymentName\", \"newDeploymentName\")\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"removeDeployment handles success response\", (done: MochaDone) => {\n        mockReturn(\"\", 200, {});\n\n        manager.removeDeployment(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"getDeploymentHistory handles success response with no packages\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ history: [] }), 200);\n\n        manager.getDeploymentHistory(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                assert.equal(obj.length, 0);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"getDeploymentHistory handles success response with two packages\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ history: [{ label: \"v1\" }, { label: \"v2\" }] }), 200);\n\n        manager.getDeploymentHistory(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                assert.equal(obj.length, 2);\n                assert.equal(obj[0].label, \"v1\");\n                assert.equal(obj[1].label, \"v2\");\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"getDeploymentHistory handles error response\", (done: MochaDone) => {\n        mockReturn(\"\", 404);\n\n        manager.getDeploymentHistory(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                throw new Error(\"Call should not complete successfully\");\n            }, (error: Error) => done());\n    });\n\n    it(\"clearDeploymentHistory handles success response\", (done: MochaDone) => {\n        mockReturn(\"\", 204);\n\n        manager.clearDeploymentHistory(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"clearDeploymentHistory handles error response\", (done: MochaDone) => {\n        mockReturn(\"\", 404);\n\n        manager.clearDeploymentHistory(\"appName\", \"deploymentName\")\n            .done((obj: any) => {\n                throw new Error(\"Call should not complete successfully\");\n            }, (error: Error) => done());\n    });\n\n    it(\"addCollaborator handles successful response\", (done: MochaDone) => {\n        mockReturn(\"\", 201, { location: \"/collaborators\" });\n        manager.addCollaborator(\"appName\", \"email1\")\n            .done((obj) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"addCollaborator handles error response\", (done: MochaDone) => {\n        mockReturn(\"\", 404, {});\n        manager.addCollaborator(\"appName\", \"email1\")\n            .done((obj) => {\n                throw new Error(\"Call should not complete successfully\");\n            }, (error: Error) => done());\n    });\n\n    it(\"getCollaborators handles success response with no collaborators\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ collaborators: {} }), 200);\n\n        manager.getCollaborators(\"appName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                assert.equal(Object.keys(obj).length, 0);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"getCollaborators handles success response with multiple collaborators\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({\n            collaborators: {\n                \"email1\": { permission: \"Owner\", isCurrentAccount: true },\n                \"email2\": { permission: \"Collaborator\", isCurrentAccount: false }\n            }\n        }), 200);\n\n        manager.getCollaborators(\"appName\")\n            .done((obj: any) => {\n                assert.ok(obj);\n                assert.equal(obj[\"email1\"].permission, \"Owner\");\n                assert.equal(obj[\"email2\"].permission, \"Collaborator\");\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"removeCollaborator handles success response\", (done: MochaDone) => {\n        mockReturn(\"\", 200, {});\n\n        manager.removeCollaborator(\"appName\", \"email1\")\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"patchRelease handles success response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ package: { description: \"newDescription\" } }), 200);\n\n        manager.patchRelease(\"appName\", \"deploymentName\", \"label\", { description: \"newDescription\" })\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"patchRelease handles error response\", (done: MochaDone) => {\n        mockReturn(\"\", 400);\n\n        manager.patchRelease(\"appName\", \"deploymentName\", \"label\", {})\n            .done((obj: any) => {\n                throw new Error(\"Call should not complete successfully\");\n            }, (error: Error) => done());\n    });\n\n    it(\"promote handles success response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ package: { description: \"newDescription\" } }), 200);\n\n        manager.promote(\"appName\", \"deploymentName\", \"newDeploymentName\", { description: \"newDescription\" })\n            .done((obj: any) => {\n                assert.ok(obj);\n                assert.equal(obj.description, \"newDescription\")\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"promote handles error response\", (done: MochaDone) => {\n        mockReturn(\"\", 400);\n\n        manager.promote(\"appName\", \"deploymentName\", \"newDeploymentName\", { rollout: 123 })\n            .done((obj: any) => {\n                throw new Error(\"Call should not complete successfully\");\n            }, (error: Error) => done());\n    });\n\n    it(\"rollback handles success response\", (done: MochaDone) => {\n        mockReturn(JSON.stringify({ package: { label: \"v1\" } }), 200);\n\n        manager.rollback(\"appName\", \"deploymentName\", \"v1\")\n            .done((obj: any) => {\n                assert.ok(!obj);\n                done();\n            }, rejectHandler);\n    });\n\n    it(\"rollback handles error response\", (done: MochaDone) => {\n        mockReturn(\"\", 400);\n\n        manager.rollback(\"appName\", \"deploymentName\", \"v1\")\n            .done((obj: any) => {\n                throw new Error(\"Call should not complete successfully\");\n            }, (error: Error) => done());\n    });\n});\n\n// Helper method that is used everywhere that an assert.fail() is needed in a promise handler\nfunction rejectHandler(val: any): void {\n    assert.fail();\n}\n\n// Wrapper for superagent-mock that abstracts away information not needed for SDK tests\nfunction mockReturn(bodyText: string, statusCode: number, header = {}): void {\n    require(\"superagent-mock\")(request, [{\n        pattern: \"http://localhost/(\\\\w+)/?\",\n        fixtures: function(match: any, params: any): any {\n            var isOk = statusCode >= 200 && statusCode < 300;\n            if (!isOk) {\n                var err: any = new Error(bodyText);\n                err.status = statusCode;\n                throw err;\n            }\n            return { text: bodyText, status: statusCode, ok: isOk, header: header, headers: {} };\n        },\n        callback: function(match: any, data: any): any { return data; }\n    }]);\n}\n"], "sourceRoot": "../.."}
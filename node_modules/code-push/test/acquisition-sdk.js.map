{"version": 3, "sources": ["test/acquisition-sdk.ts"], "names": ["clone"], "mappings": "AAAA,oDAAoD;AAEpD,IAAY,MAAM,WAAM,QAAQ,CAAC,CAAA;AAIjC,IAAY,cAAc,WAAM,2BAA2B,CAAC,CAAA;AAC5D,IAAY,OAAO,WAAM,yBAAyB,CAAC,CAAA;AAGnD,IAAI,aAAa,GAA6B,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE3E,IAAI,aAAa,GAAiC;IAC9C,UAAU,EAAE,OAAO;IACnB,cAAc,EAAE,WAAW;IAC3B,aAAa,EAAE,OAAO,CAAC,kBAAkB;IACzC,SAAS,EAAE,OAAO,CAAC,SAAS;CAC/B,CAAA;AAED,IAAI,sBAAsB,GAA2B;IACjD,aAAa,EAAE,OAAO,CAAC,kBAAkB;IACzC,WAAW,EAAE,QAAQ;IACrB,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa,CAAC,UAAU;IACpC,WAAW,EAAE,SAAS;IACtB,WAAW,EAAE,KAAK;IAClB,WAAW,EAAE,GAAG;CACnB,CAAC;AAEF,IAAI,kBAAkB,GAAiC;IACnD,aAAa,EAAE,OAAO,CAAC,kBAAkB;IACzC,WAAW,EAAE,aAAa,CAAC,WAAW;IACtC,WAAW,EAAE,aAAa,CAAC,WAAW;IACtC,KAAK,EAAE,aAAa,CAAC,KAAK;IAC1B,UAAU,EAAE,aAAa,CAAC,UAAU;IACpC,WAAW,EAAE,aAAa,CAAC,WAAW;IACtC,WAAW,EAAE,aAAa,CAAC,WAAW;IACtC,WAAW,EAAE,aAAa,CAAC,WAAW;CACzC,CAAC;AAEF,IAAI,kBAAkB,GAA4C;IAC9D,gBAAgB,EAAE,IAAI;IACtB,UAAU,EAAE,aAAa,CAAC,UAAU;CACvC,CAAC;AAEF,QAAQ,CAAC,iBAAiB,EAAE;IACxB,UAAU,CAAC;QACP,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kEAAkE,EAAE,UAAC,IAAe;QACnF,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,WAAW,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,UAAC,KAAY,EAAE,aAAqF;YAClK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iDAAiD,EAAE,UAAC,IAAe;QAClE,IAAI,mBAAmB,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChF,mBAAmB,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;QAE5D,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,WAAW,CAAC,6BAA6B,CAAC,mBAAmB,EAAE,UAAC,KAAY,EAAE,aAAqF;YAC/J,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAClC,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0EAA0E,EAAE,UAAC,IAAe;QAC3F,IAAI,oBAAoB,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACjF,oBAAoB,CAAC,WAAW,GAAG,SAAS,CAAC;QAE7C,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,WAAW,CAAC,6BAA6B,CAAC,oBAAoB,EAAE,UAAC,KAAY,EAAE,aAAqF;YAChK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE,UAAC,IAAe;QAC9E,IAAI,sBAAsB,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACnF,sBAAsB,CAAC,UAAU,GAAG,OAAO,CAAC;QAE5C,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,WAAW,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,UAAC,KAAY,EAAE,aAAqF;YAClK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oDAAoD,EAAE,UAAC,IAAe;QACrE,IAAI,uBAAuB,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACpF,uBAAuB,CAAC,UAAU,GAAG,OAAO,CAAC;QAE7C,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,WAAW,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,UAAC,KAAY,EAAE,aAAqF;YACnK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YACtC,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mCAAmC,EAAE,UAAC,IAAe;QACpD,IAAI,sBAAsB,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACnF,sBAAsB,CAAC,UAAU,GAAG,OAAO,CAAC;QAE5C,IAAI,YAAY,GAAiC;YAC7C,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;SAC3B,CAAC;QAEF,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,2BAA2B,CAAC,YAAY,CAAC,EAAE,aAAa,CAAC,CAAC;QAC9H,WAAW,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,UAAC,KAAY,EAAE,aAAmF;YAChK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE,UAAC,IAAe;QAC1E,IAAI,sBAAsB,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACnF,sBAAsB,CAAC,UAAU,GAAG,OAAO,CAAC;QAE5C,IAAI,kBAAkB,GAAiC;YACnD,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;SACnD,CAAC;QAEF,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,EAAE,aAAa,CAAC,CAAC;QACpI,WAAW,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,UAAC,KAAY,EAAE,aAAmF;YAChK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wEAAwE,EAAE,UAAC,IAAe;QACzF,IAAI,gCAAgC,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC7F,gCAAgC,CAAC,UAAU,GAAG,OAAO,CAAC;QAEtD,IAAI,yBAAyB,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;QACrD,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAEtC,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,WAAW,CAAC,6BAA6B,CAAC,gCAAgC,EAAE,UAAC,KAAY,EAAE,aAAqF;YAC5K,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+DAA+D,EAAE,UAAC,IAAe;QAChF,OAAO,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;QAEzC,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,WAAW,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,UAAC,KAAY,EAAE,aAA2C;YACxH,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;YAC9C,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE,UAAC,IAAe;QACxE,IAAI,cAAc,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC3E,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC;QAEjC,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QACpG,IAAI,CAAC;YACD,WAAW,CAAC,6BAA6B,CAAC,cAAc,EAAE,UAAC,KAAY,EAAE,aAAqF;gBAC1J,MAAM,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;gBAC5F,IAAI,EAAE,CAAC;YACX,CAAC,CAAC,CAAC;QACP,CAAE;QAAA,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACb,IAAI,EAAE,CAAC;QACX,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2EAA2E,EAAE,UAAC,IAAe;QAC5F,IAAI,sBAAsB,GAA2B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACnF,sBAAsB,CAAC,UAAU,GAAG,OAAO,CAAC;QAE5C,IAAI,kBAAkB,GAAiC;YACnD,UAAU,EAAE,GAAG;YACf,IAAI,EAAE,iBAAiB;SAC1B,CAAC;QAEF,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,EAAE,aAAa,CAAC,CAAC;QACpI,WAAW,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,UAAC,KAAY,EAAE,aAAmF;YAChK,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC7B,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE,UAAC,IAAe;QACnD,AACA,iCADiC;QACjC,IAAI,EAAE,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE,UAAC,IAAe;QAC7D,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QAEpG,WAAW,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,EAAE,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAC,KAAY,EAAE,SAAe;YAC1K,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACR,MAAM,KAAK,CAAC;YAChB,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,SAAS,EAAe,IAAI,CAAC,CAAC;YAE3C,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8CAA8C,EAAE,UAAC,IAAe;QAC/D,IAAI,WAAW,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,aAAa,CAAC,CAAC;QAEpG,WAAW,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,CAAC,UAAC,KAAY,EAAE,SAAe;YACpF,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;gBACR,MAAM,KAAK,CAAC;YAChB,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,SAAS,EAAe,IAAI,CAAC,CAAC;YAE3C,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AAEH,eAAkB,aAAgB;IAC9BA,MAAMA,CAACA,IAAIA,CAACA,KAAKA,CAACA,IAAIA,CAACA,SAASA,CAACA,aAAaA,CAACA,CAACA,CAACA;AACrDA,CAACA", "file": "acquisition-sdk.js", "sourcesContent": ["/// <reference path=\"../definitions/harness.d.ts\" />\n\nimport * as assert from \"assert\";\nimport * as express from \"express\";\nimport * as http from \"http\";\n\nimport * as acquisitionSdk from \"../script/acquisition-sdk\";\nimport * as mockApi from \"./acquisition-rest-mock\";\nimport * as rest from \"rest-definitions\";\n\nvar latestPackage: rest.UpdateCheckResponse = clone(mockApi.latestPackage);\n\nvar configuration: acquisitionSdk.Configuration = {\n    appVersion: \"1.5.0\",\n    clientUniqueId: \"My iPhone\",\n    deploymentKey: mockApi.validDeploymentKey,\n    serverUrl: mockApi.serverUrl,\n}\n\nvar templateCurrentPackage: acquisitionSdk.Package = {\n    deploymentKey: mockApi.validDeploymentKey,\n    description: \"sdfsdf\",\n    label: \"v1\",\n    appVersion: latestPackage.appVersion,\n    packageHash: \"hash001\",\n    isMandatory: false,\n    packageSize: 100\n};\n\nvar scriptUpdateResult: acquisitionSdk.RemotePackage = {\n    deploymentKey: mockApi.validDeploymentKey,\n    description: latestPackage.description,\n    downloadUrl: latestPackage.downloadURL,\n    label: latestPackage.label,\n    appVersion: latestPackage.appVersion,\n    isMandatory: latestPackage.isMandatory,\n    packageHash: latestPackage.packageHash,\n    packageSize: latestPackage.packageSize\n};\n\nvar nativeUpdateResult: acquisitionSdk.NativeUpdateNotification = {\n    updateAppVersion: true,\n    appVersion: latestPackage.appVersion\n};\n\ndescribe(\"Acquisition SDK\", () => {\n    beforeEach(() => {\n        mockApi.latestPackage = clone(latestPackage);\n    });\n\n    it(\"Package with lower label and different package hash gives update\", (done: MochaDone) => {\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        acquisition.queryUpdateWithCurrentPackage(templateCurrentPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage | acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            assert.deepEqual(scriptUpdateResult, returnPackage);\n            done();\n        });\n    });\n\n    it(\"Package with equal package hash gives no update\", (done: MochaDone) => {\n        var equalVersionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        equalVersionPackage.packageHash = latestPackage.packageHash;\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        acquisition.queryUpdateWithCurrentPackage(equalVersionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage | acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            assert.equal(null, returnPackage);\n            done();\n        });\n    });\n\n    it(\"Package with higher different hash and higher label version gives update\", (done: MochaDone) => {\n        var higherVersionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        higherVersionPackage.packageHash = \"hash990\";\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        acquisition.queryUpdateWithCurrentPackage(higherVersionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage | acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            assert.deepEqual(scriptUpdateResult, returnPackage);\n            done();\n        });\n    });\n\n    it(\"Package with lower native version gives update notification\", (done: MochaDone) => {\n        var lowerAppVersionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        lowerAppVersionPackage.appVersion = \"0.0.1\";\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        acquisition.queryUpdateWithCurrentPackage(lowerAppVersionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage | acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            assert.deepEqual(nativeUpdateResult, returnPackage);\n            done();\n        });\n    });\n\n    it(\"Package with higher native version gives no update\", (done: MochaDone) => {\n        var higherAppVersionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        higherAppVersionPackage.appVersion = \"9.9.0\";\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        acquisition.queryUpdateWithCurrentPackage(higherAppVersionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage | acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            assert.deepEqual(null, returnPackage);\n            done();\n        });\n    });\n\n    it(\"An empty response gives no update\", (done: MochaDone) => {\n        var lowerAppVersionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        lowerAppVersionPackage.appVersion = \"0.0.1\";\n\n        var emptyReponse: acquisitionSdk.Http.Response = {\n            statusCode: 200,\n            body: JSON.stringify({})\n        };\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.CustomResponseHttpRequester(emptyReponse), configuration);\n        acquisition.queryUpdateWithCurrentPackage(lowerAppVersionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage|acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            done();\n        });\n    });\n\n    it(\"An unexpected (but valid) JSON response gives no update\", (done: MochaDone) => {\n        var lowerAppVersionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        lowerAppVersionPackage.appVersion = \"0.0.1\";\n\n        var unexpectedResponse: acquisitionSdk.Http.Response = {\n            statusCode: 200,\n            body: JSON.stringify({ unexpected: \"response\" })\n        };\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.CustomResponseHttpRequester(unexpectedResponse), configuration);\n        acquisition.queryUpdateWithCurrentPackage(lowerAppVersionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage|acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            done();\n        });\n    });\n\n    it(\"Package for companion app ignores high native version and gives update\", (done: MochaDone) => {\n        var higherAppVersionCompanionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        higherAppVersionCompanionPackage.appVersion = \"9.9.0\";\n\n        var companionAppConfiguration = clone(configuration);\n        configuration.ignoreAppVersion = true;\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        acquisition.queryUpdateWithCurrentPackage(higherAppVersionCompanionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage | acquisitionSdk.NativeUpdateNotification) => {\n            assert.equal(null, error);\n            assert.deepEqual(scriptUpdateResult, returnPackage);\n            done();\n        });\n    });\n\n    it(\"If latest package is mandatory, returned package is mandatory\", (done: MochaDone) => {\n        mockApi.latestPackage.isMandatory = true;\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        acquisition.queryUpdateWithCurrentPackage(templateCurrentPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage) => {\n            assert.equal(null, error);\n            assert.equal(true, returnPackage.isMandatory);\n            done();\n        });\n    });\n\n    it(\"If invalid arguments are provided, an error is raised\", (done: MochaDone) => {\n        var invalidPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        invalidPackage.appVersion = null;\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n        try {\n            acquisition.queryUpdateWithCurrentPackage(invalidPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage | acquisitionSdk.NativeUpdateNotification) => {\n                assert.fail(\"Should throw an error if the native implementation gave an incorrect package\");\n                done();\n            });\n        } catch (error) {\n            done();\n        }\n    });\n\n    it(\"If an invalid JSON response is returned by the server, an error is raised\", (done: MochaDone) => {\n        var lowerAppVersionPackage: acquisitionSdk.Package = clone(templateCurrentPackage);\n        lowerAppVersionPackage.appVersion = \"0.0.1\";\n\n        var invalidJsonReponse: acquisitionSdk.Http.Response = {\n            statusCode: 200,\n            body: \"invalid {{ json\"\n        };\n\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.CustomResponseHttpRequester(invalidJsonReponse), configuration);\n        acquisition.queryUpdateWithCurrentPackage(lowerAppVersionPackage, (error: Error, returnPackage: acquisitionSdk.RemotePackage|acquisitionSdk.NativeUpdateNotification) => {\n            assert.notEqual(null, error);\n            done();\n        });\n    });\n\n    it(\"If deploymentKey is not valid...\", (done: MochaDone) => {\n        // TODO: behaviour is not defined\n        done();\n    });\n\n    it(\"reportStatusDeploy(...) signals completion\", (done: MochaDone): void => {\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n\n        acquisition.reportStatusDeploy(templateCurrentPackage, acquisitionSdk.AcquisitionStatus.DeploymentFailed, \"1.5.0\", mockApi.validDeploymentKey, ((error: Error, parameter: void): void => {\n            if (error) {\n                throw error;\n            }\n\n            assert.equal(parameter, /*expected*/ null);\n\n            done();\n        }));\n    });\n\n    it(\"reportStatusDownload(...) signals completion\", (done: MochaDone): void => {\n        var acquisition = new acquisitionSdk.AcquisitionManager(new mockApi.HttpRequester(), configuration);\n\n        acquisition.reportStatusDownload(templateCurrentPackage, ((error: Error, parameter: void): void => {\n            if (error) {\n                throw error;\n            }\n\n            assert.equal(parameter, /*expected*/ null);\n\n            done();\n        }));\n    });\n});\n\nfunction clone<T>(initialObject: T): T {\n    return JSON.parse(JSON.stringify(initialObject));\n}\n"], "sourceRoot": "../.."}
{"version": 3, "sources": ["test/acquisition-rest-mock.ts"], "names": ["HttpRequester", "HttpRequester.constructor", "HttpRequester.request", "CustomResponseHttpRequester", "CustomResponseHttpRequester.constructor", "CustomResponseHttpRequester.request", "Server", "Server.constructor", "Server.onAcquire", "Server.onUpdateCheck", "Server.onReportStatus"], "mappings": "AAAA,oDAAoD;AAGpD,IAAY,WAAW,WAAM,aAAa,CAAC,CAAA;AAE3C,IAAY,cAAc,WAAM,2BAA2B,CAAC,CAAA;AAGjD,0BAAkB,GAAG,gBAAgB,CAAC;AACtC,qBAAa,GAA6B;IACjD,WAAW,EAAE,kDAAkD;IAC/D,WAAW,EAAE,oBAAoB;IACjC,UAAU,EAAE,OAAO;IACnB,KAAK,EAAE,OAAO;IACd,WAAW,EAAE,KAAK;IAClB,WAAW,EAAE,IAAI;IACjB,gBAAgB,EAAE,KAAK;IACvB,WAAW,EAAE,SAAS;IACtB,WAAW,EAAE,IAAI;CACpB,CAAC;AAES,iBAAS,GAAG,kBAAkB,CAAC;AAC1C,IAAI,qBAAqB,GAAG,iBAAS,GAAG,sBAAsB,CAAC;AAC/D,IAAI,uBAAuB,GAAG,iBAAS,GAAG,wBAAwB,CAAC;AACnE,IAAI,cAAc,GAAG,iBAAS,GAAG,eAAe,CAAC;AAEjD;IAAAA;IAiBAC,CAACA;IAhBUD,+BAAOA,GAAdA,UAAeA,IAA8BA,EAAEA,GAAWA,EAAEA,qBAAqFA,EAAEA,QAAgEA;QAC/ME,EAAEA,CAACA,CAACA,CAACA,QAAQA,IAAIA,OAAOA,qBAAqBA,KAAKA,UAAUA,CAACA,CAACA,CAACA;YAC3DA,QAAQA,GAA0DA,qBAAqBA,CAACA;QAC5FA,CAACA;QAEDA,EAAEA,CAACA,CAACA,IAAIA,KAAKA,WAA4BA,IAAIA,GAAGA,CAACA,OAAOA,CAACA,cAAcA,CAACA,KAAKA,CAACA,CAACA,CAACA,CAACA;YAC7EA,IAAIA,MAAMA,GAAGA,WAAWA,CAACA,KAAKA,CAACA,GAAGA,CAACA,SAASA,CAACA,cAAcA,CAACA,MAAMA,CAACA,CAACA,CAACA;YACrEA,MAAMA,CAACA,aAAaA,CAACA,MAAMA,EAAEA,QAAQA,CAACA,CAACA;QAC3CA,CAACA;QAACA,IAAIA,CAACA,EAAEA,CAACA,CAACA,IAAIA,KAAKA,YAA6BA,IAAIA,GAAGA,KAAKA,qBAAqBA,CAACA,CAACA,CAACA;YACjFA,MAAMA,CAACA,cAAcA,CAACA,QAAQA,CAACA,CAACA;QACpCA,CAACA;QAACA,IAAIA,CAACA,EAAEA,CAACA,CAACA,IAAIA,KAAKA,YAA6BA,IAAIA,GAAGA,KAAKA,uBAAuBA,CAACA,CAACA,CAACA;YACnFA,MAAMA,CAACA,cAAcA,CAACA,QAAQA,CAACA,CAACA;QACpCA,CAACA;QAACA,IAAIA,CAACA,CAACA;YACJA,MAAMA,IAAIA,KAAKA,CAACA,iBAAiBA,CAACA,CAACA;QACvCA,CAACA;IACLA,CAACA;IACLF,oBAACA;AAADA,CAjBA,AAiBCA,IAAA;AAjBY,qBAAa,gBAiBzB,CAAA;AAED;IAGIG,qCAAYA,QAAsCA;QAC9CC,IAAIA,CAACA,QAAQA,GAAGA,QAAQA,CAACA;IAC7BA,CAACA;IAEMD,6CAAOA,GAAdA,UAAeA,IAA8BA,EAAEA,GAAWA,EAAEA,qBAAmFA,EAAEA,QAAgEA;QAC7ME,EAAEA,CAACA,CAACA,OAAOA,qBAAqBA,KAAKA,UAAUA,CAACA,CAACA,CAACA;YAC9CA,MAAMA,IAAIA,KAAKA,CAACA,yBAAyBA,CAACA,CAACA;QAC/CA,CAACA;QAEDA,QAAQA,GAA0DA,qBAAqBA,CAACA;QACxFA,QAAQA,CAACA,IAAIA,EAAEA,IAAIA,CAACA,QAAQA,CAACA,CAACA;IAClCA,CAACA;IACLF,kCAACA;AAADA,CAfA,AAeCA,IAAA;AAfY,mCAA2B,8BAevC,CAAA;AAED;IAAAG;IAgDAC,CAACA;IA/CiBD,gBAASA,GAAvBA,UAAwBA,MAAWA,EAAEA,QAA+DA;QAChGE,EAAEA,CAACA,CAACA,MAAMA,CAACA,aAAaA,KAAKA,0BAAkBA,CAACA,CAACA,CAACA;YAC9CA,QAAQA,CAAYA,IAAIA,EAAEA;gBACtBA,UAAUA,EAAEA,GAAGA;gBACfA,IAAIA,EAAEA,IAAIA,CAACA,SAASA,CAACA,EAAEA,UAAUA,EAAEA,EAAEA,WAAWA,EAAEA,KAAKA,EAAEA,EAAEA,CAACA;aAC/DA,CAACA,CAACA;QACPA,CAACA;QAACA,IAAIA,CAACA,CAACA;YACJA,QAAQA,CAAYA,IAAIA,EAAEA;gBACtBA,UAAUA,EAAEA,GAAGA;gBACfA,IAAIA,EAAEA,IAAIA,CAACA,SAASA,CAACA,EAAEA,UAAUA,EAAEA,qBAAaA,EAAEA,CAACA;aACtDA,CAACA,CAACA;QACPA,CAACA;IACLA,CAACA;IAEaF,oBAAaA,GAA3BA,UAA4BA,MAAWA,EAAEA,QAA+DA;QACpGG,IAAIA,aAAaA,GAA4BA;YACzCA,aAAaA,EAAEA,MAAMA,CAACA,aAAaA;YACnCA,UAAUA,EAAEA,MAAMA,CAACA,UAAUA;YAC7BA,WAAWA,EAAEA,MAAMA,CAACA,WAAWA;YAC/BA,WAAWA,EAAEA,CAACA,CAACA,CAACA,MAAMA,CAACA,WAAWA,CAACA;YACnCA,KAAKA,EAAEA,MAAMA,CAACA,KAAKA;SACtBA,CAACA;QAEFA,EAAEA,CAACA,CAACA,CAACA,aAAaA,CAACA,aAAaA,IAAIA,CAACA,aAAaA,CAACA,UAAUA,CAACA,CAACA,CAACA;YAC5DA,QAAQA,CAAYA,IAAIA,EAAEA,EAAEA,UAAUA,EAAEA,GAAGA,EAAEA,CAACA,CAACA;QACnDA,CAACA;QAACA,IAAIA,CAACA,CAACA;YACJA,IAAIA,UAAUA,GAA6BA,EAAEA,WAAWA,EAAEA,KAAKA,EAAEA,CAACA;YAClEA,EAAEA,CAACA,CAACA,aAAaA,CAACA,aAAaA,KAAKA,0BAAkBA,CAACA,CAACA,CAACA;gBACrDA,EAAEA,CAACA,CAACA,aAAaA,CAACA,WAAWA,IAAIA,aAAaA,CAACA,UAAUA,KAAKA,qBAAaA,CAACA,UAAUA,CAACA,CAACA,CAACA;oBACrFA,EAAEA,CAACA,CAACA,aAAaA,CAACA,WAAWA,KAAKA,qBAAaA,CAACA,WAAWA,CAACA,CAACA,CAACA;wBAC1DA,UAAUA,GAAGA,qBAAaA,CAACA;oBAC/BA,CAACA;gBACLA,CAACA;gBAACA,IAAIA,CAACA,EAAEA,CAACA,CAACA,aAAaA,CAACA,UAAUA,GAAGA,qBAAaA,CAACA,UAAUA,CAACA,CAACA,CAACA;oBAC7DA,UAAUA,GAAkCA,EAAEA,gBAAgBA,EAAEA,IAAIA,EAAEA,UAAUA,EAAEA,qBAAaA,CAACA,UAAUA,EAAEA,CAACA;gBACjHA,CAACA;YACLA,CAACA;YAEDA,QAAQA,CAAYA,IAAIA,EAAEA;gBACtBA,UAAUA,EAAEA,GAAGA;gBACfA,IAAIA,EAAEA,IAAIA,CAACA,SAASA,CAACA,EAAEA,UAAUA,EAAEA,UAAUA,EAAEA,CAACA;aACnDA,CAACA,CAACA;QACPA,CAACA;IACLA,CAACA;IAEaH,qBAAcA,GAA5BA,UAA6BA,QAA+DA;QACxFI,QAAQA,CAAWA,IAAIA,EAAeA,EAAEA,UAAUA,EAAEA,GAAGA,EAAEA,CAACA,CAACA;IAC/DA,CAACA;IACLJ,aAACA;AAADA,CAhDA,AAgDCA,IAAA", "file": "acquisition-rest-mock.js", "sourcesContent": ["/// <reference path=\"../definitions/harness.d.ts\" />\n\nimport * as express from \"express\";\nimport * as querystring from \"querystring\";\n\nimport * as acquisitionSdk from \"../script/acquisition-sdk\";\nimport * as rest from \"rest-definitions\";\n\nexport var validDeploymentKey = \"asdfasdfawerqw\";\nexport var latestPackage = <rest.UpdateCheckResponse>{\n    downloadURL: \"http://www.windowsazure.com/blobs/awperoiuqpweru\",\n    description: \"Angry flappy birds\",\n    appVersion: \"1.5.0\",\n    label: \"2.4.0\",\n    isMandatory: false,\n    isAvailable: true,\n    updateAppVersion: false,\n    packageHash: \"hash240\",\n    packageSize: 1024\n};\n\nexport var serverUrl = \"http://myurl.com\";\nvar reportStatusDeployUrl = serverUrl + \"/reportStatus/deploy\";\nvar reportStatusDownloadUrl = serverUrl + \"/reportStatus/download\";\nvar updateCheckUrl = serverUrl + \"/updateCheck?\";\n\nexport class HttpRequester implements acquisitionSdk.Http.Requester {\n    public request(verb: acquisitionSdk.Http.Verb, url: string, requestBodyOrCallback: string | acquisitionSdk.Callback<acquisitionSdk.Http.Response>, callback?: acquisitionSdk.Callback<acquisitionSdk.Http.Response>): void {\n        if (!callback && typeof requestBodyOrCallback === \"function\") {\n            callback = <acquisitionSdk.Callback<acquisitionSdk.Http.Response>>requestBodyOrCallback;\n        }\n\n        if (verb === acquisitionSdk.Http.Verb.GET && url.indexOf(updateCheckUrl) === 0) {\n            var params = querystring.parse(url.substring(updateCheckUrl.length));\n            Server.onUpdateCheck(params, callback);\n        } else if (verb === acquisitionSdk.Http.Verb.POST && url === reportStatusDeployUrl) {\n            Server.onReportStatus(callback);\n        } else if (verb === acquisitionSdk.Http.Verb.POST && url === reportStatusDownloadUrl) {\n            Server.onReportStatus(callback);\n        } else {\n            throw new Error(\"Unexpected call\");\n        }\n    }\n}\n\nexport class CustomResponseHttpRequester implements acquisitionSdk.Http.Requester {\n    response: acquisitionSdk.Http.Response;\n\n    constructor(response: acquisitionSdk.Http.Response) {\n        this.response = response;\n    }\n\n    public request(verb: acquisitionSdk.Http.Verb, url: string, requestBodyOrCallback: string|acquisitionSdk.Callback<acquisitionSdk.Http.Response>, callback?: acquisitionSdk.Callback<acquisitionSdk.Http.Response>): void {\n        if (typeof requestBodyOrCallback !== \"function\") {\n            throw new Error(\"Unexpected request body\");\n        }\n\n        callback = <acquisitionSdk.Callback<acquisitionSdk.Http.Response>>requestBodyOrCallback;\n        callback(null, this.response);\n    }\n}\n\nclass Server {\n    public static onAcquire(params: any, callback: acquisitionSdk.Callback<acquisitionSdk.Http.Response>): void {\n        if (params.deploymentKey !== validDeploymentKey) {\n            callback(/*error=*/ null, {\n                statusCode: 200,\n                body: JSON.stringify({ updateInfo: { isAvailable: false } })\n            });\n        } else {\n            callback(/*error=*/ null, {\n                statusCode: 200,\n                body: JSON.stringify({ updateInfo: latestPackage })\n            });\n        }\n    }\n\n    public static onUpdateCheck(params: any, callback: acquisitionSdk.Callback<acquisitionSdk.Http.Response>): void {\n        var updateRequest: rest.UpdateCheckRequest = {\n            deploymentKey: params.deploymentKey,\n            appVersion: params.appVersion,\n            packageHash: params.packageHash,\n            isCompanion: !!(params.isCompanion),\n            label: params.label\n        };\n\n        if (!updateRequest.deploymentKey || !updateRequest.appVersion) {\n            callback(/*error=*/ null, { statusCode: 400 });\n        } else {\n            var updateInfo = <rest.UpdateCheckResponse>{ isAvailable: false };\n            if (updateRequest.deploymentKey === validDeploymentKey) {\n                if (updateRequest.isCompanion || updateRequest.appVersion === latestPackage.appVersion) {\n                    if (updateRequest.packageHash !== latestPackage.packageHash) {\n                        updateInfo = latestPackage;\n                    }\n                } else if (updateRequest.appVersion < latestPackage.appVersion) {\n                    updateInfo = <rest.UpdateCheckResponse><any>{ updateAppVersion: true, appVersion: latestPackage.appVersion };\n                }\n            }\n\n            callback(/*error=*/ null, {\n                statusCode: 200,\n                body: JSON.stringify({ updateInfo: updateInfo })\n            });\n        }\n    }\n\n    public static onReportStatus(callback: acquisitionSdk.Callback<acquisitionSdk.Http.Response>): void {\n        callback(/*error*/ null, /*response*/ { statusCode: 200 });\n    }\n}\n"], "sourceRoot": "../.."}
{"version": 3, "sources": ["script/management-sdk.ts"], "names": ["urlEncode", "Acco<PERSON><PERSON><PERSON><PERSON>", "AccountManager.constructor", "AccountManager.accessKey", "AccountManager.isAuthenticated", "AccountManager.addAccessKey", "AccountManager.getAccessKey", "AccountManager.getAccessKeys", "AccountManager.getSessions", "AccountManager.patchAccessKey", "AccountManager.removeAccessKey", "AccountManager.removeSession", "AccountManager.getAccountInfo", "AccountManager.getApps", "AccountManager.getApp", "AccountManager.addApp", "AccountManager.removeApp", "AccountManager.renameApp", "AccountManager.transferApp", "AccountManager.getCollaborators", "AccountManager.addCollaborator", "AccountManager.removeCollaborator", "AccountManager.addDeployment", "AccountManager.clearDeploymentHistory", "AccountManager.getDeployments", "AccountManager.getDeployment", "AccountManager.renameDeployment", "AccountManager.removeDeployment", "AccountManager.getDeploymentMetrics", "AccountManager.getDeploymentHistory", "AccountManager.release", "AccountManager.patchRelease", "AccountManager.promote", "AccountManager.rollback", "AccountManager.packageFileFromPath", "AccountManager.generateRandomFilename", "AccountManager.get", "AccountManager.post", "AccountManager.patch", "AccountManager.del", "AccountManager.makeApiRequest", "AccountManager.getCodePushError", "AccountManager.getErrorStatus", "AccountManager.getErrorMessage", "AccountManager.attachCredentials", "AccountManager.appNameParam"], "mappings": "AAAA,IAAY,EAAE,WAAM,IAAI,CAAC,CAAA;AACzB,IAAY,EAAE,WAAM,IAAI,CAAC,CAAA;AACzB,IAAY,IAAI,WAAM,MAAM,CAAC,CAAA;AAC7B,IAAO,CAAC,WAAW,GAAG,CAAC,CAAC;AACxB,IAAO,KAAK,WAAW,OAAO,CAAC,CAAC;AAChC,IAAO,UAAU,WAAW,YAAY,CAAC,CAAC;AAC1C,IAAY,WAAW,WAAM,cAAc,CAAC,CAAA;AAC5C,IAAY,IAAI,WAAM,MAAM,CAAC,CAAA;AAE7B,IAAO,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;AAI3B,IAAI,UAAU,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAC7C,UAAU,CAAC,UAAU,CAAC,CAAC;AAEvB,IAAI,WAAW,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAY7C,AACA,yEADyE;mBACtD,OAAiB;IAAEA,gBAAmBA;SAAnBA,WAAmBA,CAAnBA,sBAAmBA,CAAnBA,IAAmBA;QAAnBA,+BAAmBA;;IACrDA,IAAIA,MAAMA,GAAGA,EAAEA,CAACA;IAChBA,GAAGA,CAACA,CAACA,GAAGA,CAACA,CAACA,GAAGA,CAACA,EAAEA,CAACA,GAAGA,OAAOA,CAACA,MAAMA,EAAEA,CAACA,EAAEA,EAAEA,CAACA;QACtCA,MAAMA,IAAIA,OAAOA,CAACA,CAACA,CAACA,CAACA;QACrBA,EAAEA,CAACA,CAACA,CAACA,GAAGA,MAAMA,CAACA,MAAMA,CAACA,CAACA,CAACA;YACpBA,MAAMA,IAAIA,kBAAkBA,CAACA,MAAMA,CAACA,CAACA,CAACA,CAACA,CAACA;QAC5CA,CAACA;IACLA,CAACA;IAEDA,MAAMA,CAACA,MAAMA,CAACA;AAClBA,CAACA;AAED;IAqBIC,wBAAYA,SAAiBA,EAAEA,aAAuBA,EAAEA,SAAkBA,EAAEA,KAAcA;QACtFC,EAAEA,CAACA,CAACA,CAACA,SAASA,CAACA;YAACA,MAAMA,IAAIA,KAAKA,CAACA,4BAA4BA,CAACA,CAACA;QAE9DA,IAAIA,CAACA,UAAUA,GAAGA,SAASA,CAACA;QAC5BA,IAAIA,CAACA,cAAcA,GAAGA,aAAaA,CAACA;QACpCA,IAAIA,CAACA,UAAUA,GAAGA,SAASA,IAAIA,cAAcA,CAACA,UAAUA,CAACA;QACzDA,IAAIA,CAACA,MAAMA,GAAGA,KAAKA,CAACA;IACxBA,CAACA;IAEDD,sBAAWA,qCAASA;aAApBA;YACIE,MAAMA,CAACA,IAAIA,CAACA,UAAUA,CAACA;QAC3BA,CAACA;;;OAAAF;IAEMA,wCAAeA,GAAtBA,UAAuBA,mBAA6BA;QAApDG,iBAuBCA;QAtBGA,MAAMA,CAACA,OAAOA,CAAMA,UAACA,OAAOA,EAAEA,MAAMA,EAAEA,MAAMA;YACxCA,IAAIA,OAAOA,GAA4BA,UAAUA,CAACA,GAAGA,CAACA,KAAIA,CAACA,UAAUA,GAAGA,OAAUA,gBAAgBA,gCAA1BA,SAASA,KAAiBA,CAACA,CAACA;YACpGA,EAAEA,CAACA,CAACA,KAAIA,CAACA,MAAMA,CAACA;gBAAOA,OAAQA,CAACA,KAAKA,CAACA,KAAIA,CAACA,MAAMA,CAACA,CAACA;YACnDA,KAAIA,CAACA,iBAAiBA,CAACA,OAAOA,CAACA,CAACA;YAEhCA,OAAOA,CAACA,GAAGA,CAACA,UAACA,GAAQA,EAAEA,GAAwBA;gBAC3CA,IAAIA,MAAMA,GAAWA,KAAIA,CAACA,cAAcA,CAACA,GAAGA,EAAEA,GAAGA,CAACA,CAACA;gBACnDA,EAAEA,CAACA,CAACA,GAAGA,IAAIA,MAAMA,KAAKA,cAAcA,CAACA,kBAAkBA,CAACA,CAACA,CAACA;oBACtDA,MAAMA,CAACA,KAAIA,CAACA,gBAAgBA,CAACA,GAAGA,EAAEA,GAAGA,CAACA,CAACA,CAACA;oBACxCA,MAAMA,CAACA;gBACXA,CAACA;gBAEDA,IAAIA,aAAaA,GAAYA,MAAMA,KAAKA,GAAGA,CAACA;gBAE5CA,EAAEA,CAACA,CAACA,CAACA,aAAaA,IAAIA,mBAAmBA,CAACA,CAAAA,CAACA;oBACvCA,MAAMA,CAACA,KAAIA,CAACA,gBAAgBA,CAACA,GAAGA,EAAEA,GAAGA,CAACA,CAACA,CAACA;oBACxCA,MAAMA,CAACA;gBACXA,CAACA;gBAEDA,OAAOA,CAACA,aAAaA,CAACA,CAACA;YAC3BA,CAACA,CAACA,CAACA;;QACPA,CAACA,CAACA,CAACA;IACPA,CAACA;IAEMH,qCAAYA,GAAnBA,UAAoBA,YAAoBA,EAAEA,GAAYA;QAClDI,EAAEA,CAACA,CAACA,CAACA,YAAYA,CAACA,CAACA,CAACA;YAChBA,MAAMA,IAAIA,KAAKA,CAACA,qDAAqDA,CAACA,CAACA;QAC3EA,CAACA;QAEDA,IAAIA,gBAAgBA,GAAqBA;YACrCA,SAASA,EAAEA,EAAEA,CAACA,QAAQA,EAAEA;YACxBA,YAAYA,cAAAA;YACZA,GAAGA,KAAAA;SACNA,CAACA;QAEFA,MAAMA,CAACA,IAAIA,CAACA,IAAIA,CAACA,OAAUA,cAAcA,8BAAxBA,SAASA,KAAeA,EAAEA,IAAIA,CAACA,SAASA,CAACA,gBAAgBA,CAACA,EAA0BA,IAAIA,CAACA;aACrGA,IAAIA,CAACA,UAACA,QAAsBA;YACzBA,MAAMA,CAACA;gBACHA,WAAWA,EAAEA,QAAQA,CAACA,IAAIA,CAACA,SAASA,CAACA,WAAWA;gBAChDA,OAAOA,EAAEA,QAAQA,CAACA,IAAIA,CAACA,SAASA,CAACA,OAAOA;gBACxCA,GAAGA,EAAEA,QAAQA,CAACA,IAAIA,CAACA,SAASA,CAACA,IAAIA;gBACjCA,IAAIA,EAAEA,QAAQA,CAACA,IAAIA,CAACA,SAASA,CAACA,YAAYA;aAC7CA,CAACA;QACNA,CAACA,CAACA,CAACA;;IACXA,CAACA;IAEMJ,qCAAYA,GAAnBA,UAAoBA,aAAqBA;QACrCK,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,cAAeA,EAAaA,EAAEA,kCAAxCA,SAASA,KAAgBA,aAAaA,EAAEA,CAACA;aACpDA,IAAIA,CAACA,UAACA,GAAiBA;YACpBA,MAAMA,CAACA;gBACHA,WAAWA,EAAEA,GAAGA,CAACA,IAAIA,CAACA,SAASA,CAACA,WAAWA;gBAC3CA,OAAOA,EAAEA,GAAGA,CAACA,IAAIA,CAACA,SAASA,CAACA,OAAOA;gBACnCA,IAAIA,EAAEA,GAAGA,CAACA,IAAIA,CAACA,SAASA,CAACA,YAAYA;aACxCA,CAACA;QACNA,CAACA,CAACA,CAACA;;IACXA,CAACA;IAEML,sCAAaA,GAApBA;QACIM,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,aAAaA,6BAAvBA,SAASA,KAAcA,CAACA;aACnCA,IAAIA,CAACA,UAACA,GAAiBA;YACpBA,IAAIA,UAAUA,GAAgBA,EAAEA,CAACA;YAEjCA,GAAGA,CAACA,IAAIA,CAACA,UAAUA,CAACA,OAAOA,CAACA,UAACA,eAAgCA;gBACzDA,CAACA,eAAeA,CAACA,SAASA,IAAIA,UAAUA,CAACA,IAAIA,CAACA;oBAC1CA,WAAWA,EAAEA,eAAeA,CAACA,WAAWA;oBACxCA,OAAOA,EAAEA,eAAeA,CAACA,OAAOA;oBAChCA,IAAIA,EAAEA,eAAeA,CAACA,YAAYA;iBACrCA,CAACA,CAACA;YACPA,CAACA,CAACA,CAACA;YAEHA,MAAMA,CAACA,UAAUA,CAACA;QACtBA,CAACA,CAACA,CAACA;;IACXA,CAACA;IAEMN,oCAAWA,GAAlBA;QACIO,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,aAAaA,6BAAvBA,SAASA,KAAcA,CAACA;aACnCA,IAAIA,CAACA,UAACA,GAAiBA;YACpBA,AAEAA,iEAFiEA;YACjEA,kDAAkDA;gBAC9CA,UAAUA,GAAuCA,EAAEA,CAACA;YACxDA,IAAIA,GAAGA,GAAWA,IAAIA,IAAIA,EAAEA,CAACA,OAAOA,EAAEA,CAACA;YACvCA,GAAGA,CAACA,IAAIA,CAACA,UAAUA,CAACA,OAAOA,CAACA,UAACA,eAAgCA;gBACzDA,EAAEA,CAACA,CAACA,eAAeA,CAACA,SAASA,IAAIA,eAAeA,CAACA,OAAOA,GAAGA,GAAGA,CAACA,CAACA,CAACA;oBAC7DA,UAAUA,CAACA,eAAeA,CAACA,SAASA,CAACA,GAAGA;wBACpCA,YAAYA,EAAEA,eAAeA,CAACA,WAAWA;wBACzCA,WAAWA,EAAEA,eAAeA,CAACA,SAASA;qBACzCA,CAACA;gBACNA,CAACA;YACLA,CAACA,CAACA,CAACA;YAEHA,IAAIA,QAAQA,GAAcA,MAAMA,CAACA,IAAIA,CAACA,UAAUA,CAACA;iBAC5CA,GAAGA,CAACA,UAACA,WAAmBA,IAAKA,OAAAA,UAAUA,CAACA,WAAWA,CAACA,EAAvBA,CAAuBA,CAACA,CAACA;YAE3DA,MAAMA,CAACA,QAAQA,CAACA;QACpBA,CAACA,CAACA,CAACA;;IACXA,CAACA;IAGMP,uCAAcA,GAArBA,UAAsBA,OAAeA,EAAEA,OAAgBA,EAAEA,GAAYA;QACjEQ,IAAIA,gBAAgBA,GAAqBA;YACrCA,YAAYA,EAAEA,OAAOA;YACrBA,GAAGA,KAAAA;SACNA,CAACA;QAEFA,MAAMA,CAACA,IAAIA,CAACA,KAAKA,CAACA,OAAUA,cAAeA,EAAOA,EAAEA,kCAAlCA,SAASA,KAAgBA,OAAOA,EAAEA,EAAEA,IAAIA,CAACA,SAASA,CAACA,gBAAgBA,CAACA,CAACA;aAClFA,IAAIA,CAACA,UAACA,GAAiBA;YACpBA,MAAMA,CAACA;gBACHA,WAAWA,EAAEA,GAAGA,CAACA,IAAIA,CAACA,SAASA,CAACA,WAAWA;gBAC3CA,OAAOA,EAAEA,GAAGA,CAACA,IAAIA,CAACA,SAASA,CAACA,OAAOA;gBACnCA,IAAIA,EAAEA,GAAGA,CAACA,IAAIA,CAACA,SAASA,CAACA,YAAYA;aACxCA,CAACA;QACNA,CAACA,CAACA,CAACA;;IACXA,CAACA;IAEMR,wCAAeA,GAAtBA,UAAuBA,IAAYA;QAC/BS,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,cAAeA,EAAIA,EAAEA,kCAA/BA,SAASA,KAAgBA,IAAIA,EAAEA,CAACA;aAC3CA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEMT,sCAAaA,GAApBA,UAAqBA,WAAmBA;QACpCU,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,YAAaA,EAAWA,EAAEA,gCAApCA,SAASA,KAAcA,WAAWA,EAAEA,CAACA;aAChDA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEDV,UAAUA;IACHA,uCAAcA,GAArBA;QACIW,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,UAAUA,0BAApBA,SAASA,KAAWA,CAACA;aAChCA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,OAAOA,EAAhBA,CAAgBA,CAACA,CAACA;;IACvDA,CAACA;IAEDX,OAAOA;IACAA,gCAAOA,GAAdA;QACIY,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,OAAOA,uBAAjBA,SAASA,KAAQA,CAACA;aAC7BA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,IAAIA,EAAbA,CAAaA,CAACA,CAACA;;IACpDA,CAACA;IAEMZ,+BAAMA,GAAbA,UAAcA,OAAeA;QACzBa,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,EAAEA,4BAA/CA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAEA,CAACA;aAC3DA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,GAAGA,EAAZA,CAAYA,CAACA,CAACA;;IACnDA,CAACA;IAEMb,+BAAMA,GAAbA,UAAcA,OAAeA,EAAEA,KAAaA,EAAEA,WAAmBA,EAAEA,4BAA6CA;QAA7Cc,4CAA6CA,GAA7CA,oCAA6CA;QAC5GA,IAAIA,GAAGA,GAAuBA;YAC1BA,IAAIA,EAAEA,OAAOA;YACbA,EAAEA,EAAEA,KAAKA;YACTA,QAAQA,EAAEA,WAAWA;YACrBA,4BAA4BA,EAAEA,4BAA4BA;SAC7DA,CAACA;QACFA,MAAMA,CAACA,IAAIA,CAACA,IAAIA,CAACA,OAAUA,QAAQA,wBAAlBA,SAASA,KAASA,EAAEA,IAAIA,CAACA,SAASA,CAACA,GAAGA,CAACA,EAA0BA,KAAKA,CAACA;aACnFA,IAAIA,CAACA,cAAMA,OAAAA,GAAGA,EAAHA,CAAGA,CAACA,CAACA;;IACzBA,CAACA;IAEMd,kCAASA,GAAhBA,UAAiBA,OAAeA;QAC5Be,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,EAAEA,4BAA/CA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAEA,CAACA;aAC3DA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEMf,kCAASA,GAAhBA,UAAiBA,UAAkBA,EAAEA,UAAkBA;QACnDgB,MAAMA,CAACA,IAAIA,CAACA,KAAKA,CAACA,OAAUA,QAASA,EAA6BA,EAAEA,4BAAlDA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,UAAUA,CAACA,EAAEA,EAAEA,IAAIA,CAACA,SAASA,CAACA,EAAEA,IAAIA,EAAEA,UAAUA,EAAEA,CAACA,CAACA;aACtGA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEMhB,oCAAWA,GAAlBA,UAAmBA,OAAeA,EAAEA,KAAaA;QAC7CiB,MAAMA,CAACA,IAAIA,CAACA,IAAIA,CAACA,OAAUA,QAASA,EAA0BA,YAAaA,EAAKA,EAAEA,0CAAjEA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAaA,KAAKA,EAAEA,EAAmBA,IAAIA,EAA0BA,KAAKA,CAACA;aACpIA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEDjB,gBAAgBA;IACTA,yCAAgBA,GAAvBA,UAAwBA,OAAeA;QACnCkB,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,gBAAgBA,0CAA7DA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,CAACA;aACzEA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,aAAaA,EAAtBA,CAAsBA,CAACA,CAACA;;IAC7DA,CAACA;IAEMlB,wCAAeA,GAAtBA,UAAuBA,OAAeA,EAAEA,KAAaA;QACjDmB,MAAMA,CAACA,IAAIA,CAACA,IAAIA,CAACA,OAAUA,QAASA,EAA0BA,iBAAkBA,EAAKA,EAAEA,+CAAtEA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAkBA,KAAKA,EAAEA,EAAmBA,IAAIA,EAA0BA,KAAKA,CAACA;aACzIA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEMnB,2CAAkBA,GAAzBA,UAA0BA,OAAeA,EAAEA,KAAaA;QACpDoB,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,iBAAkBA,EAAKA,EAAEA,+CAAtEA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAkBA,KAAKA,EAAEA,CAACA;aAClFA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEDpB,cAAcA;IACPA,sCAAaA,GAApBA,UAAqBA,OAAeA,EAAEA,cAAsBA;QACxDqB,IAAIA,UAAUA,GAAeA,EAAEA,IAAIA,EAAEA,cAAcA,EAAEA,CAACA;QACtDA,MAAMA,CAACA,IAAIA,CAACA,IAAIA,CAACA,OAAUA,QAASA,EAA0BA,eAAeA,yCAA5DA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAeA,EAAEA,IAAIA,CAACA,SAASA,CAACA,UAAUA,CAACA,EAA0BA,IAAIA,CAACA;aACnIA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,UAAUA,EAAnBA,CAAmBA,CAACA,CAACA;;IAC1DA,CAACA;IAEMrB,+CAAsBA,GAA7BA,UAA8BA,OAAeA,EAAEA,cAAsBA;QACjEsB,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,UAAUA,qDAArFA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAUA,CAACA;aACjGA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEMtB,uCAAcA,GAArBA,UAAsBA,OAAeA;QACjCuB,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,eAAeA,yCAA5DA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAeA,CAACA;aACxEA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,WAAWA,EAApBA,CAAoBA,CAACA,CAACA;;IAC3DA,CAACA;IAEMvB,sCAAaA,GAApBA,UAAqBA,OAAeA,EAAEA,cAAsBA;QACxDwB,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,EAAEA,6CAA7EA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAEA,CAACA;aACzFA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,UAAUA,EAAnBA,CAAmBA,CAACA,CAACA;;IAC1DA,CAACA;IAEMxB,yCAAgBA,GAAvBA,UAAwBA,OAAeA,EAAEA,iBAAyBA,EAAEA,iBAAyBA;QACzFyB,MAAMA,CAACA,IAAIA,CAACA,KAAKA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAiBA,EAAEA,6CAAhFA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,iBAAiBA,EAAEA,EAAEA,IAAIA,CAACA,SAASA,CAACA,EAAEA,IAAIA,EAAEA,iBAAiBA,EAAEA,CAACA,CAACA;aAC3IA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEMzB,yCAAgBA,GAAvBA,UAAwBA,OAAeA,EAAEA,cAAsBA;QAC3D0B,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,EAAEA,6CAA7EA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAEA,CAACA;aACzFA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEM1B,6CAAoBA,GAA3BA,UAA4BA,OAAeA,EAAEA,cAAsBA;QAC/D2B,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,UAAUA,qDAArFA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAUA,CAACA;aACjGA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,OAAOA,EAAhBA,CAAgBA,CAACA,CAACA;;IACvDA,CAACA;IAEM3B,6CAAoBA,GAA3BA,UAA4BA,OAAeA,EAAEA,cAAsBA;QAC/D4B,MAAMA,CAACA,IAAIA,CAACA,GAAGA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,UAAUA,qDAArFA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAUA,CAACA;aACjGA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,OAAOA,EAAhBA,CAAgBA,CAACA,CAACA;;IACvDA,CAACA;IAEM5B,gCAAOA,GAAdA,UAAeA,OAAeA,EAAEA,cAAsBA,EAAEA,QAAgBA,EAAEA,mBAA2BA,EAAEA,cAA2BA,EAAEA,sBAAmDA;QAAvL6B,iBA+CCA;QA7CGA,MAAMA,CAACA,OAAOA,CAAUA,UAACA,OAAOA,EAAEA,MAAMA,EAAEA,MAAMA;YAE5CA,cAAcA,CAACA,UAAUA,GAAGA,mBAAmBA,CAACA;YAChDA,IAAIA,OAAOA,GAA4BA,UAAUA,CAACA,IAAIA,CAACA,KAAIA,CAACA,UAAUA,GAAGA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,UAAUA,qDAArFA,SAASA,KAAUA,KAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAUA,CAACA,CAACA;YAChKA,EAAEA,CAACA,CAACA,KAAIA,CAACA,MAAMA,CAACA;gBAAOA,OAAQA,CAACA,KAAKA,CAACA,KAAIA,CAACA,MAAMA,CAACA,CAACA;YACnDA,KAAIA,CAACA,iBAAiBA,CAACA,OAAOA,CAACA,CAACA;YAEhCA,IAAIA,qBAAqBA,GAAyBA,KAAIA,CAACA,mBAAmBA,CAACA,QAAQA,CAACA,CAACA;YAErFA,qBAAqBA,CAACA,IAAIA,CAACA,UAACA,WAAwBA;gBAChDA,IAAIA,IAAIA,GAAQA,EAAEA,CAACA,gBAAgBA,CAACA,WAAWA,CAACA,IAAIA,CAACA,CAACA;gBACtDA,OAAOA,CAACA,MAAMA,CAACA,SAASA,EAAEA,IAAIA,CAACA;qBAC1BA,KAAKA,CAACA,aAAaA,EAAEA,IAAIA,CAACA,SAASA,CAACA,cAAcA,CAACA,CAACA;qBACpDA,EAAEA,CAACA,UAAUA,EAAEA,UAACA,KAAUA;oBACvBA,EAAEA,CAACA,CAACA,sBAAsBA,IAAIA,KAAKA,IAAIA,KAAKA,CAACA,KAAKA,GAAGA,CAACA,CAACA,CAACA,CAACA;wBACrDA,IAAIA,eAAeA,GAAWA,KAAKA,CAACA,MAAMA,GAAGA,KAAKA,CAACA,KAAKA,GAAGA,GAAGA,CAACA;wBAC/DA,sBAAsBA,CAACA,eAAeA,CAACA,CAACA;oBAC5CA,CAACA;gBACLA,CAACA,CAACA;qBACDA,GAAGA,CAACA,UAACA,GAAQA,EAAEA,GAAwBA;oBAEpCA,EAAEA,CAACA,CAACA,WAAWA,CAACA,WAAWA,CAACA,CAACA,CAACA;wBAC1BA,EAAEA,CAACA,UAAUA,CAACA,WAAWA,CAACA,IAAIA,CAACA,CAACA;oBACpCA,CAACA;oBAEDA,EAAEA,CAACA,CAACA,GAAGA,CAACA,CAACA,CAACA;wBACNA,MAAMA,CAACA,KAAIA,CAACA,gBAAgBA,CAACA,GAAGA,EAAEA,GAAGA,CAACA,CAACA,CAACA;wBACxCA,MAAMA,CAACA;oBACXA,CAACA;oBAEDA,IAAIA,CAACA;wBACDA,IAAIA,IAAIA,GAAGA,IAAIA,CAACA,KAAKA,CAACA,GAAGA,CAACA,IAAIA,CAACA,CAACA;oBACpCA,CAAEA;oBAAAA,KAAKA,CAACA,CAACA,GAAGA,CAACA,CAACA,CAACA;wBACXA,MAAMA,CAAgBA,EAAEA,OAAOA,EAAEA,+BAA6BA,GAAGA,CAACA,IAAMA,EAAEA,UAAUA,EAAEA,cAAcA,CAACA,qBAAqBA,EAAEA,CAACA,CAACA;wBAC9HA,MAAMA,CAACA;oBACXA,CAACA;oBAEDA,EAAEA,CAACA,CAACA,GAAGA,CAACA,EAAEA,CAACA,CAACA,CAACA;wBACTA,OAAOA,CAAUA,IAAIA,CAACA,OAAOA,CAACA,CAACA;oBACnCA,CAACA;oBAACA,IAAIA,CAACA,CAACA;wBACJA,MAAMA,CAAgBA,EAAEA,OAAOA,EAAEA,IAAIA,CAACA,OAAOA,EAAEA,UAAUA,EAAEA,GAAGA,IAAIA,GAAGA,CAACA,MAAMA,EAAEA,CAACA,CAACA;oBACpFA,CAACA;gBACLA,CAACA,CAACA,CAACA;YACXA,CAACA,CAACA,CAACA;;QACPA,CAACA,CAACA,CAACA;IACPA,CAACA;IAEM7B,qCAAYA,GAAnBA,UAAoBA,OAAeA,EAAEA,cAAsBA,EAAEA,KAAaA,EAAEA,cAA2BA;QACnG8B,cAAcA,CAACA,KAAKA,GAAGA,KAAKA,CAACA;QAC7BA,IAAIA,WAAWA,GAAWA,IAAIA,CAACA,SAASA,CAACA,EAAEA,WAAWA,EAAEA,cAAcA,EAAEA,CAACA,CAACA;QAC1EA,MAAMA,CAACA,IAAIA,CAACA,KAAKA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,UAAUA,qDAArFA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAUA,EAAEA,WAAWA,EAA0BA,KAAKA,CAACA;aAC/IA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEM9B,gCAAOA,GAAdA,UAAeA,OAAeA,EAAEA,oBAA4BA,EAAEA,yBAAiCA,EAAGA,cAA2BA;QACzH+B,IAAIA,WAAWA,GAAWA,IAAIA,CAACA,SAASA,CAACA,EAAEA,WAAWA,EAAEA,cAAcA,EAAEA,CAACA,CAACA;QAC1EA,MAAMA,CAACA,IAAIA,CAACA,IAAIA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAoBA,WAAYA,EAAyBA,EAAEA,0DAAxHA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,oBAAoBA,EAAYA,yBAAyBA,EAAEA,EAAEA,WAAWA,EAA0BA,IAAIA,CAACA;aAChLA,IAAIA,CAACA,UAACA,GAAiBA,IAAKA,OAAAA,GAAGA,CAACA,IAAIA,CAACA,OAAOA,EAAhBA,CAAgBA,CAACA,CAACA;;IACvDA,CAACA;IAEM/B,iCAAQA,GAAfA,UAAgBA,OAAeA,EAAEA,cAAsBA,EAAEA,aAAsBA;QAC3EgC,MAAMA,CAACA,IAAIA,CAACA,IAAIA,CAACA,OAAUA,QAASA,EAA0BA,eAAgBA,EAAcA,YAAaA,EAAmBA,EAAEA,2DAA7GA,SAASA,KAAUA,IAAIA,CAACA,YAAYA,CAACA,OAAOA,CAACA,EAAgBA,cAAcA,EAAaA,aAAaA,IAAIA,EAAEA,EAAEA,EAAmBA,IAAIA,EAA0BA,KAAKA,CAACA;aAChLA,IAAIA,CAACA,cAAMA,OAAAA,IAAIA,EAAJA,CAAIA,CAACA,CAACA;;IAC1BA,CAACA;IAEOhC,4CAAmBA,GAA3BA,UAA4BA,QAAgBA;QAA5CiC,iBA4CCA;QA3CGA,IAAIA,qBAA2CA,CAACA;QAChDA,EAAEA,CAACA,CAACA,EAAEA,CAACA,SAASA,CAACA,QAAQA,CAACA,CAACA,WAAWA,EAAEA,CAACA,CAACA,CAACA;YACvCA,qBAAqBA,GAAGA,OAAOA,CAAcA,UAACA,OAAoCA,EAAEA,MAA+BA;gBAC/GA,IAAIA,aAAaA,GAAWA,QAAQA,CAACA;gBAErCA,WAAWA,CAACA,QAAQA,CAACA,aAAaA,EAAEA,UAACA,KAAWA,EAAEA,WAAsBA,EAAEA,KAAgBA;oBACtFA,EAAEA,CAACA,CAACA,KAAKA,CAACA,CAACA,CAACA;wBACRA,MAAMA,CAACA,KAAKA,CAACA,CAACA;wBACdA,MAAMA,CAACA;oBACXA,CAACA;oBAEDA,IAAIA,iBAAiBA,GAAGA,IAAIA,CAACA,OAAOA,CAACA,aAAaA,CAACA,CAACA;oBACpDA,IAAIA,QAAQA,GAAWA,KAAIA,CAACA,sBAAsBA,CAACA,EAAEA,CAACA,GAAGA,MAAMA,CAACA;oBAChEA,IAAIA,OAAOA,GAAGA,IAAIA,IAAIA,CAACA,OAAOA,EAAEA,CAACA;oBACjCA,IAAIA,WAAWA,GAAmBA,EAAEA,CAACA,iBAAiBA,CAACA,QAAQA,CAACA,CAACA;oBAEjEA,OAAOA,CAACA,YAAYA,CAACA,IAAIA,CAACA,WAAWA,CAACA;yBACjCA,EAAEA,CAACA,OAAOA,EAAEA,UAACA,KAAYA;wBACtBA,MAAMA,CAACA,KAAKA,CAACA,CAACA;oBAClBA,CAACA,CAACA;yBACDA,EAAEA,CAACA,OAAOA,EAAEA;wBACTA,QAAQA,GAAGA,IAAIA,CAACA,IAAIA,CAACA,OAAOA,CAACA,GAAGA,EAAEA,EAAEA,QAAQA,CAACA,CAACA;wBAE9CA,OAAOA,CAACA,EAAEA,WAAWA,EAAEA,IAAIA,EAAEA,IAAIA,EAAEA,QAAQA,EAAEA,CAACA,CAACA;oBACnDA,CAACA,CAACA,CAACA;oBAEPA,GAAGA,CAACA,CAACA,GAAGA,CAACA,CAACA,GAAGA,CAACA,EAAEA,CAACA,GAAGA,KAAKA,CAACA,MAAMA,EAAEA,EAAEA,CAACA,EAAEA,CAACA;wBACpCA,IAAIA,IAAIA,GAAWA,KAAKA,CAACA,CAACA,CAACA,CAACA;wBAC5BA,IAAIA,YAAYA,GAAWA,IAAIA,CAACA,QAAQA,CAACA,iBAAiBA,EAAEA,IAAIA,CAACA,CAACA;wBAElEA,AACAA,yDADyDA;wBACzDA,YAAYA,GAAGA,KAAKA,CAACA,YAAYA,CAACA,CAACA;wBAEnCA,OAAOA,CAACA,OAAOA,CAACA,IAAIA,EAAEA,YAAYA,CAACA,CAACA;oBACxCA,CAACA;oBAEDA,OAAOA,CAACA,GAAGA,EAAEA,CAACA;gBAClBA,CAACA,CAACA,CAACA;YACPA,CAACA,CAACA,CAACA;QACPA,CAACA;QAACA,IAAIA,CAACA,CAACA;YACJA,qBAAqBA,GAAGA,CAACA,CAACA,EAAEA,WAAWA,EAAEA,KAAKA,EAAEA,IAAIA,EAAEA,QAAQA,EAAEA,CAACA,CAACA;QACtEA,CAACA;QACDA,MAAMA,CAACA,qBAAqBA,CAACA;IACjCA,CAACA;IAEOjC,+CAAsBA,GAA9BA,UAA+BA,MAAcA;QACzCkC,IAAIA,QAAQA,GAAWA,EAAEA,CAACA;QAC1BA,IAAIA,SAASA,GAAWA,gEAAgEA,CAACA;QAEzFA,GAAGA,CAACA,CAACA,GAAGA,CAACA,CAACA,GAAGA,CAACA,EAAEA,CAACA,GAAGA,MAAMA,EAAEA,CAACA,EAAEA,EAAEA,CAACA;YAC9BA,QAAQA,IAAIA,SAASA,CAACA,MAAMA,CAACA,IAAIA,CAACA,KAAKA,CAACA,IAAIA,CAACA,MAAMA,EAAEA,GAAGA,SAASA,CAACA,MAAMA,CAACA,CAACA,CAACA;QAC/EA,CAACA;QAEDA,MAAMA,CAACA,QAAQA,CAACA;IACpBA,CAACA;IAEOlC,4BAAGA,GAAXA,UAAYA,QAAgBA,EAAEA,kBAAkCA;QAAlCmC,kCAAkCA,GAAlCA,yBAAkCA;QAC5DA,MAAMA,CAACA,IAAIA,CAACA,cAAcA,CAACA,KAAKA,EAAEA,QAAQA,EAAmBA,IAAIA,EAAEA,kBAAkBA,EAAmBA,IAAIA,CAACA,CAACA;IAClHA,CAACA;IAEOnC,6BAAIA,GAAZA,UAAaA,QAAgBA,EAAEA,WAAmBA,EAAEA,kBAA2BA,EAAEA,WAAsDA;QAAtDoC,2BAAsDA,GAAtDA,8CAAsDA;QACnIA,MAAMA,CAACA,IAAIA,CAACA,cAAcA,CAACA,MAAMA,EAAEA,QAAQA,EAAEA,WAAWA,EAAEA,kBAAkBA,EAAEA,WAAWA,CAACA,CAACA;IAC/FA,CAACA;IAEOpC,8BAAKA,GAAbA,UAAcA,QAAgBA,EAAEA,WAAmBA,EAAEA,kBAAmCA,EAAEA,WAAsDA;QAA3FqC,kCAAmCA,GAAnCA,0BAAmCA;QAAEA,2BAAsDA,GAAtDA,8CAAsDA;QAC5IA,MAAMA,CAACA,IAAIA,CAACA,cAAcA,CAACA,OAAOA,EAAEA,QAAQA,EAAEA,WAAWA,EAAEA,kBAAkBA,EAAEA,WAAWA,CAACA,CAACA;IAChGA,CAACA;IAEOrC,4BAAGA,GAAXA,UAAYA,QAAgBA,EAAEA,kBAAmCA;QAAnCsC,kCAAmCA,GAAnCA,0BAAmCA;QAC7DA,MAAMA,CAACA,IAAIA,CAACA,cAAcA,CAACA,KAAKA,EAAEA,QAAQA,EAAmBA,IAAIA,EAAEA,kBAAkBA,EAAmBA,IAAIA,CAACA,CAACA;IAClHA,CAACA;IAEOtC,uCAAcA,GAAtBA,UAAuBA,MAAcA,EAAEA,QAAgBA,EAAEA,WAAmBA,EAAEA,kBAA2BA,EAAEA,WAAmBA;QAA9HuC,iBA2CCA;QA1CGA,MAAMA,CAACA,OAAOA,CAAeA,UAACA,OAAOA,EAAEA,MAAMA,EAAEA,MAAMA;YACjDA,IAAIA,OAAOA,GAAkCA,UAAWA,CAACA,MAAMA,CAACA,CAACA,KAAIA,CAACA,UAAUA,GAAGA,QAAQA,CAACA,CAACA;YAC7FA,EAAEA,CAACA,CAACA,KAAIA,CAACA,MAAMA,CAACA;gBAAOA,OAAQA,CAACA,KAAKA,CAACA,KAAIA,CAACA,MAAMA,CAACA,CAACA;YACnDA,KAAIA,CAACA,iBAAiBA,CAACA,OAAOA,CAACA,CAACA;YAEhCA,EAAEA,CAACA,CAACA,WAAWA,CAACA,CAACA,CAACA;gBACdA,EAAEA,CAACA,CAACA,WAAWA,CAACA,CAACA,CAACA;oBACdA,OAAOA,GAAGA,OAAOA,CAACA,GAAGA,CAACA,cAAcA,EAAEA,WAAWA,CAACA,CAACA;gBACvDA,CAACA;gBAEDA,OAAOA,GAAGA,OAAOA,CAACA,IAAIA,CAACA,WAAWA,CAACA,CAACA;YACxCA,CAACA;YAEDA,OAAOA,CAACA,GAAGA,CAACA,UAACA,GAAQA,EAAEA,GAAwBA;gBAC3CA,EAAEA,CAACA,CAACA,GAAGA,CAACA,CAACA,CAACA;oBACNA,MAAMA,CAACA,KAAIA,CAACA,gBAAgBA,CAACA,GAAGA,EAAEA,GAAGA,CAACA,CAACA,CAACA;oBACxCA,MAAMA,CAACA;gBACXA,CAACA;gBAEDA,IAAIA,CAACA;oBACDA,IAAIA,IAAIA,GAAGA,IAAIA,CAACA,KAAKA,CAACA,GAAGA,CAACA,IAAIA,CAACA,CAACA;gBACpCA,CAAEA;gBAAAA,KAAKA,CAACA,CAACA,GAAGA,CAACA,CAACA,CAACA;gBACfA,CAACA;gBAEDA,EAAEA,CAACA,CAACA,GAAGA,CAACA,EAAEA,CAACA,CAACA,CAACA;oBACTA,EAAEA,CAACA,CAACA,kBAAkBA,IAAIA,CAACA,IAAIA,CAACA,CAACA,CAACA;wBAC9BA,MAAMA,CAAgBA,EAAEA,OAAOA,EAAEA,+BAA6BA,GAAGA,CAACA,IAAMA,EAAEA,UAAUA,EAAEA,cAAcA,CAACA,qBAAqBA,EAAEA,CAACA,CAACA;oBAClIA,CAACA;oBAACA,IAAIA,CAACA,CAACA;wBACJA,OAAOA,CAAeA;4BAClBA,OAAOA,EAAEA,GAAGA,CAACA,MAAMA;4BACnBA,IAAIA,EAAEA,IAAIA;yBACbA,CAACA,CAACA;oBACPA,CAACA;gBACLA,CAACA;gBAACA,IAAIA,CAACA,CAACA;oBACJA,EAAEA,CAACA,CAACA,IAAIA,CAACA,CAACA,CAACA;wBACPA,MAAMA,CAAgBA,EAAEA,OAAOA,EAAEA,IAAIA,CAACA,OAAOA,EAAEA,UAAUA,EAAEA,KAAIA,CAACA,cAAcA,CAACA,GAAGA,EAAEA,GAAGA,CAACA,EAAEA,CAACA,CAACA;oBAChGA,CAACA;oBAACA,IAAIA,CAACA,CAACA;wBACJA,MAAMA,CAAgBA,EAAEA,OAAOA,EAAEA,GAAGA,CAACA,IAAIA,EAAEA,UAAUA,EAAEA,KAAIA,CAACA,cAAcA,CAACA,GAAGA,EAAEA,GAAGA,CAACA,EAAEA,CAACA,CAACA;oBAC5FA,CAACA;gBACLA,CAACA;YACLA,CAACA,CAACA,CAACA;QACPA,CAACA,CAACA,CAACA;IACPA,CAACA;IAEOvC,yCAAgBA,GAAxBA,UAAyBA,KAAUA,EAAEA,QAA6BA;QAC9DwC,EAAEA,CAACA,CAACA,KAAKA,CAACA,OAAOA,KAAKA,aAAaA,CAACA,CAACA,CAACA;YAClCA,KAAKA,CAACA,OAAOA,GAAGA,iGAA+FA,KAAKA,CAACA,OAAOA,MAAGA,CAACA;QACpIA,CAACA;QAEDA,MAAMA,CAACA;YACHA,OAAOA,EAAEA,IAAIA,CAACA,eAAeA,CAACA,KAAKA,EAAEA,QAAQA,CAACA;YAC9CA,UAAUA,EAAEA,IAAIA,CAACA,cAAcA,CAACA,KAAKA,EAAEA,QAAQA,CAACA;SACnDA,CAACA;IACNA,CAACA;IAEOxC,uCAAcA,GAAtBA,UAAuBA,KAAUA,EAAEA,QAA6BA;QAC5DyC,MAAMA,CAACA,CAACA,KAAKA,IAAIA,KAAKA,CAACA,MAAMA,CAACA,IAAIA,CAACA,QAAQA,IAAIA,QAAQA,CAACA,MAAMA,CAACA,IAAIA,cAAcA,CAACA,qBAAqBA,CAACA;IAC5GA,CAACA;IAEOzC,wCAAeA,GAAvBA,UAAwBA,KAAYA,EAAEA,QAA6BA;QAC/D0C,MAAMA,CAACA,QAAQA,IAAIA,QAAQA,CAACA,IAAIA,GAAGA,QAAQA,CAACA,IAAIA,GAAGA,KAAKA,CAACA,OAAOA,CAACA;IACrEA,CAACA;IAEO1C,0CAAiBA,GAAzBA,UAA0BA,OAAgCA;QACtD2C,EAAEA,CAACA,CAACA,IAAIA,CAACA,cAAcA,CAACA,CAACA,CAACA;YACtBA,GAAGA,CAACA,CAACA,GAAGA,CAACA,UAAUA,IAAIA,IAAIA,CAACA,cAAcA,CAACA,CAACA,CAACA;gBACzCA,OAAOA,CAACA,GAAGA,CAACA,UAAUA,EAAEA,IAAIA,CAACA,cAAcA,CAACA,UAAUA,CAACA,CAACA,CAACA;YAC7DA,CAACA;QACLA,CAACA;QAEDA,OAAOA,CAACA,GAAGA,CAACA,QAAQA,EAAEA,gCAA8BA,cAAcA,CAACA,WAAWA,UAAOA,CAACA,CAACA;QACvFA,OAAOA,CAACA,GAAGA,CAACA,eAAeA,EAAEA,YAAUA,IAAIA,CAACA,UAAYA,CAACA,CAACA;QAC1DA,OAAOA,CAACA,GAAGA,CAACA,wBAAwBA,EAAEA,WAAWA,CAACA,OAAOA,CAACA,CAACA;IAC/DA,CAACA;IAED3C,gHAAgHA;IAChHA,+GAA+GA;IAC/GA,iHAAiHA;IACjHA,0HAA0HA;IAC1HA,mHAAmHA;IACnHA,sEAAsEA;IACtEA,oHAAoHA;IACpHA,oHAAoHA;IACpHA,0EAA0EA;IAC1EA,mHAAmHA;IACnHA,qCAAqCA;IAC7BA,qCAAYA,GAApBA,UAAqBA,OAAeA;QAChC4C,MAAMA,CAACA,OAAOA,CAACA,OAAOA,CAACA,GAAGA,EAAEA,IAAIA,CAACA,CAACA;IACtCA,CAACA;IAxea5C,4BAAaA,GAAGA;QAC1BA,KAAKA,EAAEA,OAAOA;QACdA,YAAYA,EAAEA,cAAcA;KAC/BA,CAACA;IACYA,yBAAUA,GAAGA,+CAA+CA,CAACA;IAC7DA,uCAAwBA,GAAGA,0BAA0BA,CAACA;IAErDA,0BAAWA,GAAWA,CAACA,CAACA;IAEzBA,oCAAqBA,GAAGA,GAAGA,CAACA,CAAEA,mCAAmCA;IACjEA,oCAAqBA,GAAGA,GAAGA,CAACA;IAC5BA,8BAAeA,GAAGA,GAAGA,CAACA;IACtBA,6BAAcA,GAAGA,GAAGA,CAACA,CAASA,sCAAsCA;IACpEA,iCAAkBA,GAAGA,GAAGA,CAACA;IA4d3CA,qBAACA;AAADA,CA1eA,AA0eCA,IAAA;AAED,AAAwB,iBAAf,cAAc,CAAC", "file": "management-sdk.js", "sourcesContent": ["import * as fs from \"fs\";\nimport * as os from \"os\";\nimport * as path from \"path\";\nimport Q = require(\"q\");\nimport slash = require(\"slash\");\nimport superagent = require(\"superagent\");\nimport * as recursiveFs from \"recursive-fs\";\nimport * as yazl from \"yazl\";\n\nimport Promise = Q.Promise;\n\nimport { AccessKey, AccessKeyRequest, Account, App, AppCreationRequest, CodePushError, CollaboratorMap, CollaboratorProperties, Deployment, DeploymentMetrics, Headers, Package, PackageInfo, ServerAccessKey, Session, UpdateMetrics } from \"./types\";\n\nvar superproxy = require(\"superagent-proxy\");\nsuperproxy(superagent);\n\nvar packageJson = require(\"../package.json\");\n\ninterface JsonResponse {\n    headers: Headers;\n    body?: any;\n}\n\ninterface PackageFile {\n    isTemporary: boolean;\n    path: string;\n}\n\n// A template string tag function that URL encodes the substituted values\nfunction urlEncode(strings: string[], ...values: string[]): string {\n    var result = \"\";\n    for (var i = 0; i < strings.length; i++) {\n        result += strings[i];\n        if (i < values.length) {\n            result += encodeURIComponent(values[i]);\n        }\n    }\n\n    return result;\n}\n\nclass AccountManager {\n    public static AppPermission = {\n        OWNER: \"Owner\",\n        COLLABORATOR: \"Collaborator\"\n    };\n    public static SERVER_URL = \"https://codepush-management.azurewebsites.net\";\n    public static MOBILE_CENTER_SERVER_URL = \"https://mobile.azure.com\";\n\n    private static API_VERSION: number = 2;\n\n    public static ERROR_GATEWAY_TIMEOUT = 504;  // Used if there is a network error\n    public static ERROR_INTERNAL_SERVER = 500;\n    public static ERROR_NOT_FOUND = 404;\n    public static ERROR_CONFLICT = 409;         // Used if the resource already exists\n    public static ERROR_UNAUTHORIZED = 401;\n\n    private _accessKey: string;\n    private _serverUrl: string;\n    private _customHeaders: Headers;\n    private _proxy: string;\n\n    constructor(accessKey: string, customHeaders?: Headers, serverUrl?: string, proxy?: string) {\n        if (!accessKey) throw new Error(\"A token must be specified.\");\n\n        this._accessKey = accessKey;\n        this._customHeaders = customHeaders;\n        this._serverUrl = serverUrl || AccountManager.SERVER_URL;\n        this._proxy = proxy;\n    }\n\n    public get accessKey(): string {\n        return this._accessKey;\n    }\n\n    public isAuthenticated(throwIfUnauthorized?: boolean): Promise<boolean> {\n        return Promise<any>((resolve, reject, notify) => {\n            var request: superagent.Request<any> = superagent.get(this._serverUrl + urlEncode `/authenticated`);\n            if (this._proxy) (<any>request).proxy(this._proxy);\n            this.attachCredentials(request);\n\n            request.end((err: any, res: superagent.Response) => {\n                var status: number = this.getErrorStatus(err, res);\n                if (err && status !== AccountManager.ERROR_UNAUTHORIZED) {\n                    reject(this.getCodePushError(err, res));\n                    return;\n                }\n\n                var authenticated: boolean = status === 200;\n\n                if (!authenticated && throwIfUnauthorized){\n                    reject(this.getCodePushError(err, res));\n                    return;\n                }\n\n                resolve(authenticated);\n            });\n        });\n    }\n\n    public addAccessKey(friendlyName: string, ttl?: number): Promise<AccessKey> {\n        if (!friendlyName) {\n            throw new Error(\"A name must be specified when adding an access key.\");\n        }\n\n        var accessKeyRequest: AccessKeyRequest = {\n            createdBy: os.hostname(),\n            friendlyName,\n            ttl\n        };\n\n        return this.post(urlEncode `/accessKeys/`, JSON.stringify(accessKeyRequest), /*expectResponseBody=*/ true)\n            .then((response: JsonResponse) => {\n                return {\n                    createdTime: response.body.accessKey.createdTime,\n                    expires: response.body.accessKey.expires,\n                    key: response.body.accessKey.name,\n                    name: response.body.accessKey.friendlyName\n                };\n            });\n    }\n\n    public getAccessKey(accessKeyName: string): Promise<AccessKey> {\n        return this.get(urlEncode `/accessKeys/${accessKeyName}`)\n            .then((res: JsonResponse) => {\n                return {\n                    createdTime: res.body.accessKey.createdTime,\n                    expires: res.body.accessKey.expires,\n                    name: res.body.accessKey.friendlyName,\n                };\n            });\n    }\n\n    public getAccessKeys(): Promise<AccessKey[]> {\n        return this.get(urlEncode `/accessKeys`)\n            .then((res: JsonResponse) => {\n                var accessKeys: AccessKey[] = [];\n\n                res.body.accessKeys.forEach((serverAccessKey: ServerAccessKey) => {\n                    !serverAccessKey.isSession && accessKeys.push({\n                        createdTime: serverAccessKey.createdTime,\n                        expires: serverAccessKey.expires,\n                        name: serverAccessKey.friendlyName\n                    });\n                });\n\n                return accessKeys;\n            });\n    }\n\n    public getSessions(): Promise<Session[]> {\n        return this.get(urlEncode `/accessKeys`)\n            .then((res: JsonResponse) => {\n                // A machine name might be associated with multiple session keys,\n                // but we should only return one per machine name.\n                var sessionMap: { [machineName: string]: Session } = {};\n                var now: number = new Date().getTime();\n                res.body.accessKeys.forEach((serverAccessKey: ServerAccessKey) => {\n                    if (serverAccessKey.isSession && serverAccessKey.expires > now) {\n                        sessionMap[serverAccessKey.createdBy] = {\n                            loggedInTime: serverAccessKey.createdTime,\n                            machineName: serverAccessKey.createdBy\n                        };\n                    }\n                });\n\n                var sessions: Session[] = Object.keys(sessionMap)\n                    .map((machineName: string) => sessionMap[machineName]);\n\n                return sessions;\n            });\n    }\n\n\n    public patchAccessKey(oldName: string, newName?: string, ttl?: number): Promise<AccessKey> {\n        var accessKeyRequest: AccessKeyRequest = {\n            friendlyName: newName,\n            ttl\n        };\n\n        return this.patch(urlEncode `/accessKeys/${oldName}`, JSON.stringify(accessKeyRequest))\n            .then((res: JsonResponse) => {\n                return {\n                    createdTime: res.body.accessKey.createdTime,\n                    expires: res.body.accessKey.expires,\n                    name: res.body.accessKey.friendlyName,\n                };\n            });\n    }\n\n    public removeAccessKey(name: string): Promise<void> {\n        return this.del(urlEncode `/accessKeys/${name}`)\n            .then(() => null);\n    }\n\n    public removeSession(machineName: string): Promise<void> {\n        return this.del(urlEncode `/sessions/${machineName}`)\n            .then(() => null);\n    }\n\n    // Account\n    public getAccountInfo(): Promise<Account> {\n        return this.get(urlEncode `/account`)\n            .then((res: JsonResponse) => res.body.account);\n    }\n\n    // Apps\n    public getApps(): Promise<App[]> {\n        return this.get(urlEncode `/apps`)\n            .then((res: JsonResponse) => res.body.apps);\n    }\n\n    public getApp(appName: string): Promise<App> {\n        return this.get(urlEncode `/apps/${this.appNameParam(appName)}`)\n            .then((res: JsonResponse) => res.body.app);\n    }\n\n    public addApp(appName: string, appOs: string, appPlatform: string, manuallyProvisionDeployments: boolean = false): Promise<App> {\n        var app: AppCreationRequest = {\n            name: appName,\n            os: appOs,\n            platform: appPlatform,\n            manuallyProvisionDeployments: manuallyProvisionDeployments\n        };\n        return this.post(urlEncode `/apps/`, JSON.stringify(app), /*expectResponseBody=*/ false)\n            .then(() => app);\n    }\n\n    public removeApp(appName: string): Promise<void> {\n        return this.del(urlEncode `/apps/${this.appNameParam(appName)}`)\n            .then(() => null);\n    }\n\n    public renameApp(oldAppName: string, newAppName: string): Promise<void> {\n        return this.patch(urlEncode `/apps/${this.appNameParam(oldAppName)}`, JSON.stringify({ name: newAppName }))\n            .then(() => null);\n    }\n\n    public transferApp(appName: string, email: string): Promise<void> {\n        return this.post(urlEncode `/apps/${this.appNameParam(appName)}/transfer/${email}`, /*requestBody=*/ null, /*expectResponseBody=*/ false)\n            .then(() => null);\n    }\n\n    // Collaborators\n    public getCollaborators(appName: string): Promise<CollaboratorMap> {\n        return this.get(urlEncode `/apps/${this.appNameParam(appName)}/collaborators`)\n            .then((res: JsonResponse) => res.body.collaborators);\n    }\n\n    public addCollaborator(appName: string, email: string): Promise<void> {\n        return this.post(urlEncode `/apps/${this.appNameParam(appName)}/collaborators/${email}`, /*requestBody=*/ null, /*expectResponseBody=*/ false)\n            .then(() => null);\n    }\n\n    public removeCollaborator(appName: string, email: string): Promise<void> {\n        return this.del(urlEncode `/apps/${this.appNameParam(appName)}/collaborators/${email}`)\n            .then(() => null);\n    }\n\n    // Deployments\n    public addDeployment(appName: string, deploymentName: string): Promise<Deployment> {\n        var deployment = <Deployment>{ name: deploymentName };\n        return this.post(urlEncode `/apps/${this.appNameParam(appName)}/deployments/`, JSON.stringify(deployment), /*expectResponseBody=*/ true)\n            .then((res: JsonResponse) => res.body.deployment);\n    }\n\n    public clearDeploymentHistory(appName: string, deploymentName: string): Promise<void> {\n        return this.del(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}/history`)\n            .then(() => null);\n    }\n\n    public getDeployments(appName: string): Promise<Deployment[]> {\n        return this.get(urlEncode `/apps/${this.appNameParam(appName)}/deployments/`)\n            .then((res: JsonResponse) => res.body.deployments);\n    }\n\n    public getDeployment(appName: string, deploymentName: string): Promise<Deployment> {\n        return this.get(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}`)\n            .then((res: JsonResponse) => res.body.deployment);\n    }\n\n    public renameDeployment(appName: string, oldDeploymentName: string, newDeploymentName: string): Promise<void> {\n        return this.patch(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${oldDeploymentName}`, JSON.stringify({ name: newDeploymentName }))\n            .then(() => null);\n    }\n\n    public removeDeployment(appName: string, deploymentName: string): Promise<void> {\n        return this.del(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}`)\n            .then(() => null);\n    }\n\n    public getDeploymentMetrics(appName: string, deploymentName: string): Promise<DeploymentMetrics> {\n        return this.get(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}/metrics`)\n            .then((res: JsonResponse) => res.body.metrics);\n    }\n\n    public getDeploymentHistory(appName: string, deploymentName: string): Promise<Package[]> {\n        return this.get(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}/history`)\n            .then((res: JsonResponse) => res.body.history);\n    }\n\n    public release(appName: string, deploymentName: string, filePath: string, targetBinaryVersion: string, updateMetadata: PackageInfo, uploadProgressCallback?: (progress: number) => void): Promise<Package> {\n\n        return Promise<Package>((resolve, reject, notify) => {\n\n            updateMetadata.appVersion = targetBinaryVersion;\n            var request: superagent.Request<any> = superagent.post(this._serverUrl + urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}/release`);\n            if (this._proxy) (<any>request).proxy(this._proxy);\n            this.attachCredentials(request);\n\n            var getPackageFilePromise: Promise<PackageFile> = this.packageFileFromPath(filePath);\n\n            getPackageFilePromise.then((packageFile: PackageFile) => {\n                var file: any = fs.createReadStream(packageFile.path);\n                request.attach(\"package\", file)\n                    .field(\"packageInfo\", JSON.stringify(updateMetadata))\n                    .on(\"progress\", (event: any) => {\n                        if (uploadProgressCallback && event && event.total > 0) {\n                            var currentProgress: number = event.loaded / event.total * 100;\n                            uploadProgressCallback(currentProgress);\n                        }\n                    })\n                    .end((err: any, res: superagent.Response) => {\n\n                        if (packageFile.isTemporary) {\n                            fs.unlinkSync(packageFile.path);\n                        }\n\n                        if (err) {\n                            reject(this.getCodePushError(err, res));\n                            return;\n                        }\n\n                        try {\n                            var body = JSON.parse(res.text);\n                        } catch (err) {\n                            reject(<CodePushError>{ message: `Could not parse response: ${res.text}`, statusCode: AccountManager.ERROR_INTERNAL_SERVER });\n                            return;\n                        }\n\n                        if (res.ok) {\n                            resolve(<Package>body.package);\n                        } else {\n                            reject(<CodePushError>{ message: body.message, statusCode: res && res.status });\n                        }\n                    });\n            });\n        });\n    }\n\n    public patchRelease(appName: string, deploymentName: string, label: string, updateMetadata: PackageInfo): Promise<void> {\n        updateMetadata.label = label;\n        var requestBody: string = JSON.stringify({ packageInfo: updateMetadata });\n        return this.patch(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}/release`, requestBody, /*expectResponseBody=*/ false)\n            .then(() => null);\n    }\n\n    public promote(appName: string, sourceDeploymentName: string, destinationDeploymentName: string,  updateMetadata: PackageInfo): Promise<Package> {\n        var requestBody: string = JSON.stringify({ packageInfo: updateMetadata });\n        return this.post(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${sourceDeploymentName}/promote/${destinationDeploymentName}`, requestBody, /*expectResponseBody=*/ true)\n            .then((res: JsonResponse) => res.body.package);\n    }\n\n    public rollback(appName: string, deploymentName: string, targetRelease?: string): Promise<void> {\n        return this.post(urlEncode `/apps/${this.appNameParam(appName)}/deployments/${deploymentName}/rollback/${targetRelease || ``}`, /*requestBody=*/ null, /*expectResponseBody=*/ false)\n            .then(() => null);\n    }\n\n    private packageFileFromPath(filePath: string): Promise<PackageFile> {\n        var getPackageFilePromise: Promise<PackageFile>;\n        if (fs.lstatSync(filePath).isDirectory()) {\n            getPackageFilePromise = Promise<PackageFile>((resolve: (file: PackageFile) => void, reject: (reason: Error) => void): void => {\n                var directoryPath: string = filePath;\n\n                recursiveFs.readdirr(directoryPath, (error?: any, directories?: string[], files?: string[]): void => {\n                    if (error) {\n                        reject(error);\n                        return;\n                    }\n\n                    var baseDirectoryPath = path.dirname(directoryPath);\n                    var fileName: string = this.generateRandomFilename(15) + \".zip\";\n                    var zipFile = new yazl.ZipFile();\n                    var writeStream: fs.WriteStream = fs.createWriteStream(fileName);\n\n                    zipFile.outputStream.pipe(writeStream)\n                        .on(\"error\", (error: Error): void => {\n                            reject(error);\n                        })\n                        .on(\"close\", (): void => {\n                            filePath = path.join(process.cwd(), fileName);\n\n                            resolve({ isTemporary: true, path: filePath });\n                        });\n\n                    for (var i = 0; i < files.length; ++i) {\n                        var file: string = files[i];\n                        var relativePath: string = path.relative(baseDirectoryPath, file);\n\n                        // yazl does not like backslash (\\) in the metadata path.\n                        relativePath = slash(relativePath);\n\n                        zipFile.addFile(file, relativePath);\n                    }\n\n                    zipFile.end();\n                });\n            });\n        } else {\n            getPackageFilePromise = Q({ isTemporary: false, path: filePath });\n        }\n        return getPackageFilePromise;\n    }\n\n    private generateRandomFilename(length: number): string {\n        var filename: string = \"\";\n        var validChar: string = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n\n        for (var i = 0; i < length; i++) {\n            filename += validChar.charAt(Math.floor(Math.random() * validChar.length));\n        }\n\n        return filename;\n    }\n\n    private get(endpoint: string, expectResponseBody: boolean = true): Promise<JsonResponse> {\n        return this.makeApiRequest(\"get\", endpoint, /*requestBody=*/ null, expectResponseBody, /*contentType=*/ null);\n    }\n\n    private post(endpoint: string, requestBody: string, expectResponseBody: boolean, contentType: string = \"application/json;charset=UTF-8\"): Promise<JsonResponse> {\n        return this.makeApiRequest(\"post\", endpoint, requestBody, expectResponseBody, contentType);\n    }\n\n    private patch(endpoint: string, requestBody: string, expectResponseBody: boolean = false, contentType: string = \"application/json;charset=UTF-8\"): Promise<JsonResponse> {\n        return this.makeApiRequest(\"patch\", endpoint, requestBody, expectResponseBody, contentType);\n    }\n\n    private del(endpoint: string, expectResponseBody: boolean = false): Promise<JsonResponse> {\n        return this.makeApiRequest(\"del\", endpoint, /*requestBody=*/ null, expectResponseBody, /*contentType=*/ null);\n    }\n\n    private makeApiRequest(method: string, endpoint: string, requestBody: string, expectResponseBody: boolean, contentType: string): Promise<JsonResponse> {\n        return Promise<JsonResponse>((resolve, reject, notify) => {\n            var request: superagent.Request<any> = (<any>superagent)[method](this._serverUrl + endpoint);\n            if (this._proxy) (<any>request).proxy(this._proxy);\n            this.attachCredentials(request);\n\n            if (requestBody) {\n                if (contentType) {\n                    request = request.set(\"Content-Type\", contentType);\n                }\n\n                request = request.send(requestBody);\n            }\n\n            request.end((err: any, res: superagent.Response) => {\n                if (err) {\n                    reject(this.getCodePushError(err, res));\n                    return;\n                }\n\n                try {\n                    var body = JSON.parse(res.text);\n                } catch (err) {\n                }\n\n                if (res.ok) {\n                    if (expectResponseBody && !body) {\n                        reject(<CodePushError>{ message: `Could not parse response: ${res.text}`, statusCode: AccountManager.ERROR_INTERNAL_SERVER });\n                    } else {\n                        resolve(<JsonResponse>{\n                            headers: res.header,\n                            body: body\n                        });\n                    }\n                } else {\n                    if (body) {\n                        reject(<CodePushError>{ message: body.message, statusCode: this.getErrorStatus(err, res) });\n                    } else {\n                        reject(<CodePushError>{ message: res.text, statusCode: this.getErrorStatus(err, res) });\n                    }\n                }\n            });\n        });\n    }\n\n    private getCodePushError(error: any, response: superagent.Response): CodePushError {\n        if (error.syscall === \"getaddrinfo\") {\n            error.message = `Unable to connect to the CodePush server. Are you offline, or behind a firewall or proxy?\\n(${error.message})`;\n        }\n\n        return {\n            message: this.getErrorMessage(error, response),\n            statusCode: this.getErrorStatus(error, response)\n        };\n    }\n\n    private getErrorStatus(error: any, response: superagent.Response): number {\n        return (error && error.status) || (response && response.status) || AccountManager.ERROR_GATEWAY_TIMEOUT;\n    }\n\n    private getErrorMessage(error: Error, response: superagent.Response): string {\n        return response && response.text ? response.text : error.message;\n    }\n\n    private attachCredentials(request: superagent.Request<any>): void {\n        if (this._customHeaders) {\n            for (var headerName in this._customHeaders) {\n                request.set(headerName, this._customHeaders[headerName]);\n            }\n        }\n\n        request.set(\"Accept\", `application/vnd.code-push.v${AccountManager.API_VERSION}+json`);\n        request.set(\"Authorization\", `Bearer ${this._accessKey}`);\n        request.set(\"X-CodePush-SDK-Version\", packageJson.version);\n    }\n\n    // IIS and Azure web apps have this annoying behavior where %2F (URL encoded slashes) in the URL are URL decoded\n    // BEFORE the requests reach node. That essentially means there's no good way to encode a \"/\" in the app name--\n    // URL encodeing will work when running locally but when running on Azure it gets decoded before express sees it,\n    // so app names with slashes don't get routed properly. See https://github.com/tjanczuk/iisnode/issues/343 (or other sites\n    // that complain about the same) for some more info. I explored some IIS config based workarounds, but the previous\n    // link seems to say they won't work, so I eventually gave up on that.\n    // Anyway, to workaround this issue, we now allow the client to encode / characters as ~~ (two tildes, URL encoded).\n    // The CLI now converts / to ~~ if / appears in an app name, before passing that as part of the URL. This code below\n    // does the encoding. It's hack, but seems like the least bad option here.\n    // Eventually, this service will go away & we'll all be on Max's new service. That's hosted in docker, no more IIS,\n    // so this issue should go away then.\n    private appNameParam(appName: string) {\n        return appName.replace(\"/\", \"~~\");\n    }\n}\n\nexport = AccountManager;\n"], "sourceRoot": "../.."}
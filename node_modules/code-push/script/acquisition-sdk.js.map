{"version": 3, "sources": ["script/acquisition-sdk.ts"], "names": ["AcquisitionStatus", "AcquisitionStatus.constructor", "AcquisitionManager", "AcquisitionManager.constructor", "AcquisitionManager.queryUpdateWithCurrentPackage", "AcquisitionManager.reportStatusDeploy", "AcquisitionManager.reportStatusDownload", "queryStringify"], "mappings": "AAAA,oDAAoD;AAuDpD;IAAAA;IAGAC,CAACA;IAFiBD,qCAAmBA,GAAGA,qBAAqBA,CAACA;IAC5CA,kCAAgBA,GAAGA,kBAAkBA,CAACA;IACxDA,wBAACA;AAADA,CAHA,AAGCA,IAAA;AAHY,yBAAiB,oBAG7B,CAAA;AAED;IAQIE,4BAAYA,aAA6BA,EAAEA,aAA4BA;QACnEC,IAAIA,CAACA,cAAcA,GAAGA,aAAaA,CAACA;QAEpCA,IAAIA,CAACA,UAAUA,GAAGA,aAAaA,CAACA,SAASA,CAACA;QAC1CA,EAAEA,CAACA,CAACA,IAAIA,CAACA,UAAUA,CAACA,KAAKA,CAACA,CAACA,CAACA,CAACA,KAAKA,GAAGA,CAACA,CAACA,CAACA;YACpCA,IAAIA,CAACA,UAAUA,IAAIA,GAAGA,CAACA;QAC3BA,CAACA;QAEDA,IAAIA,CAACA,WAAWA,GAAGA,aAAaA,CAACA,UAAUA,CAACA;QAC5CA,IAAIA,CAACA,eAAeA,GAAGA,aAAaA,CAACA,cAAcA,CAACA;QACpDA,IAAIA,CAACA,cAAcA,GAAGA,aAAaA,CAACA,aAAaA,CAACA;QAClDA,IAAIA,CAACA,iBAAiBA,GAAGA,aAAaA,CAACA,gBAAgBA,CAACA;IAC5DA,CAACA;IAEMD,0DAA6BA,GAApCA,UAAqCA,cAAuBA,EAAEA,QAA6DA;QAA3HE,iBAgECA;QA/DGA,EAAEA,CAACA,CAACA,CAACA,cAAcA,IAAIA,CAACA,cAAcA,CAACA,UAAUA,CAACA,CAACA,CAACA;YAChDA,MAAMA,IAAIA,KAAKA,CAACA,uDAAuDA,CAACA,EAAGA,oDAAoDA;QACnIA,CAACA,GAD4EA;QAG7EA,IAAIA,aAAaA,GAAuBA;YACpCA,aAAaA,EAAEA,IAAIA,CAACA,cAAcA;YAClCA,UAAUA,EAAEA,cAAcA,CAACA,UAAUA;YACrCA,WAAWA,EAAEA,cAAcA,CAACA,WAAWA;YACvCA,WAAWA,EAAEA,IAAIA,CAACA,iBAAiBA;YACnCA,KAAKA,EAAEA,cAAcA,CAACA,KAAKA;YAC3BA,cAAcA,EAAEA,IAAIA,CAACA,eAAeA;SACvCA,CAACA;QAEFA,IAAIA,UAAUA,GAAWA,IAAIA,CAACA,UAAUA,GAAGA,cAAcA,GAAGA,cAAcA,CAACA,aAAaA,CAACA,CAACA;QAE1FA,IAAIA,CAACA,cAAcA,CAACA,OAAOA,CAACA,WAAaA,EAAEA,UAAUA,EAAEA,UAACA,KAAYA,EAAEA,QAAuBA;YACzFA,EAAEA,CAACA,CAACA,KAAKA,CAACA,CAACA,CAACA;gBACRA,QAAQA,CAACA,KAAKA,EAAqBA,IAAIA,CAACA,CAACA;gBACzCA,MAAMA,CAACA;YACXA,CAACA;YAEDA,EAAEA,CAACA,CAACA,QAAQA,CAACA,UAAUA,KAAKA,GAAGA,CAACA,CAACA,CAACA;gBAC9BA,IAAIA,YAAiBA,CAACA;gBACtBA,EAAEA,CAACA,CAACA,QAAQA,CAACA,UAAUA,KAAKA,CAACA,CAACA,CAACA,CAACA;oBAC5BA,YAAYA,GAAGA,8BAA4BA,UAAUA,kJAA+IA,CAACA;gBACzMA,CAACA;gBAACA,IAAIA,CAACA,CAACA;oBACJA,YAAYA,GAAMA,QAAQA,CAACA,UAAUA,UAAKA,QAAQA,CAACA,IAAMA,CAACA;gBAC9DA,CAACA;gBACDA,QAAQA,CAACA,IAAIA,KAAKA,CAACA,YAAYA,CAACA,EAAqBA,IAAIA,CAACA,CAACA;gBAC3DA,MAAMA,CAACA;YACXA,CAACA;YACDA,IAAIA,CAACA;gBACDA,IAAIA,cAAcA,GAAGA,IAAIA,CAACA,KAAKA,CAACA,QAAQA,CAACA,IAAIA,CAACA,CAACA;gBAC/CA,IAAIA,UAAUA,GAAwBA,cAAcA,CAACA,UAAUA,CAACA;YACpEA,CAAEA;YAAAA,KAAKA,CAACA,CAACA,KAAKA,CAACA,CAACA,CAACA;gBACbA,QAAQA,CAACA,KAAKA,EAAqBA,IAAIA,CAACA,CAACA;gBACzCA,MAAMA,CAACA;YACXA,CAACA;YAEDA,EAAEA,CAACA,CAACA,CAACA,UAAUA,CAACA,CAACA,CAACA;gBACdA,QAAQA,CAACA,KAAKA,EAAqBA,IAAIA,CAACA,CAACA;gBACzCA,MAAMA,CAACA;YACXA,CAACA;YAACA,IAAIA,CAACA,EAAEA,CAACA,CAACA,UAAUA,CAACA,gBAAgBA,CAACA,CAACA,CAACA;gBACrCA,QAAQA,CAAYA,IAAIA,EAAEA,EAAEA,gBAAgBA,EAAEA,IAAIA,EAAEA,UAAUA,EAAEA,UAAUA,CAACA,UAAUA,EAAEA,CAACA,CAACA;gBACzFA,MAAMA,CAACA;YACXA,CAACA;YAACA,IAAIA,CAACA,EAAEA,CAACA,CAACA,CAACA,UAAUA,CAACA,WAAWA,CAACA,CAACA,CAACA;gBACjCA,QAAQA,CAAYA,IAAIA,EAAqBA,IAAIA,CAACA,CAACA;gBACnDA,MAAMA,CAACA;YACXA,CAACA;YAEDA,IAAIA,aAAaA,GAAkBA;gBAC/BA,aAAaA,EAAEA,KAAIA,CAACA,cAAcA;gBAClCA,WAAWA,EAAEA,UAAUA,CAACA,WAAWA;gBACnCA,KAAKA,EAAEA,UAAUA,CAACA,KAAKA;gBACvBA,UAAUA,EAAEA,UAAUA,CAACA,UAAUA;gBACjCA,WAAWA,EAAEA,UAAUA,CAACA,WAAWA;gBACnCA,WAAWA,EAAEA,UAAUA,CAACA,WAAWA;gBACnCA,WAAWA,EAAEA,UAAUA,CAACA,WAAWA;gBACnCA,WAAWA,EAAEA,UAAUA,CAACA,WAAWA;aACtCA,CAACA;YAEFA,QAAQA,CAAYA,IAAIA,EAAEA,aAAaA,CAACA,CAACA;QAC7CA,CAACA,CAACA,CAACA;IACPA,CAACA;IAEMF,+CAAkBA,GAAzBA,UAA0BA,eAAyBA,EAAEA,MAAeA,EAAEA,yBAAkCA,EAAEA,qBAA8BA,EAAEA,QAAyBA;QAC/JG,IAAIA,GAAGA,GAAWA,IAAIA,CAACA,UAAUA,GAAGA,qBAAqBA,CAACA;QAC1DA,IAAIA,IAAIA,GAA2BA;YAC/BA,UAAUA,EAAEA,IAAIA,CAACA,WAAWA;YAC5BA,aAAaA,EAAEA,IAAIA,CAACA,cAAcA;SACrCA,CAACA;QAEFA,EAAEA,CAACA,CAACA,IAAIA,CAACA,eAAeA,CAACA,CAACA,CAACA;YACvBA,IAAIA,CAACA,cAAcA,GAAGA,IAAIA,CAACA,eAAeA,CAACA;QAC/CA,CAACA;QAEDA,EAAEA,CAACA,CAACA,eAAeA,CAACA,CAACA,CAACA;YAClBA,IAAIA,CAACA,KAAKA,GAAGA,eAAeA,CAACA,KAAKA,CAACA;YACnCA,IAAIA,CAACA,UAAUA,GAAGA,eAAeA,CAACA,UAAUA,CAACA;YAE7CA,MAAMA,CAACA,CAACA,MAAMA,CAACA,CAACA,CAACA;gBACbA,KAAKA,iBAAiBA,CAACA,mBAAmBA,CAACA;gBAC3CA,KAAKA,iBAAiBA,CAACA,gBAAgBA;oBACnCA,IAAIA,CAACA,MAAMA,GAAGA,MAAMA,CAACA;oBACrBA,KAAKA,CAACA;gBAEVA;oBACIA,EAAEA,CAACA,CAACA,QAAQA,CAACA,CAACA,CAACA;wBACXA,EAAEA,CAACA,CAACA,CAACA,MAAMA,CAACA,CAACA,CAACA;4BACVA,QAAQA,CAACA,IAAIA,KAAKA,CAACA,0BAA0BA,CAACA,EAAeA,IAAIA,CAACA,CAACA;wBACvEA,CAACA;wBAACA,IAAIA,CAACA,CAACA;4BACJA,QAAQA,CAACA,IAAIA,KAAKA,CAACA,wBAAwBA,GAAGA,MAAMA,GAAGA,KAAKA,CAACA,EAAeA,IAAIA,CAACA,CAACA;wBACtFA,CAACA;oBACLA,CAACA;oBACDA,MAAMA,CAACA;YACfA,CAACA;QACLA,CAACA;QAEDA,EAAEA,CAACA,CAACA,yBAAyBA,CAACA,CAACA,CAACA;YAC5BA,IAAIA,CAACA,yBAAyBA,GAAGA,yBAAyBA,CAACA;QAC/DA,CAACA;QAEDA,EAAEA,CAACA,CAACA,qBAAqBA,CAACA,CAACA,CAACA;YACxBA,IAAIA,CAACA,qBAAqBA,GAAGA,qBAAqBA,CAACA;QACvDA,CAACA;QAEDA,QAAQA,GAAGA,OAAOA,SAASA,CAACA,SAASA,CAACA,MAAMA,GAAGA,CAACA,CAACA,KAAKA,UAAUA,IAAIA,SAASA,CAACA,SAASA,CAACA,MAAMA,GAAGA,CAACA,CAACA,CAACA;QAEpGA,IAAIA,CAACA,cAAcA,CAACA,OAAOA,CAACA,YAAcA,EAAEA,GAAGA,EAAEA,IAAIA,CAACA,SAASA,CAACA,IAAIA,CAACA,EAAEA,UAACA,KAAYA,EAAEA,QAAuBA;YACzGA,EAAEA,CAACA,CAACA,QAAQA,CAACA,CAACA,CAACA;gBACXA,EAAEA,CAACA,CAACA,KAAKA,CAACA,CAACA,CAACA;oBACRA,QAAQA,CAACA,KAAKA,EAAeA,IAAIA,CAACA,CAACA;oBACnCA,MAAMA,CAACA;gBACXA,CAACA;gBAEDA,EAAEA,CAACA,CAACA,QAAQA,CAACA,UAAUA,KAAKA,GAAGA,CAACA,CAACA,CAACA;oBAC9BA,QAAQA,CAACA,IAAIA,KAAKA,CAACA,QAAQA,CAACA,UAAUA,GAAGA,IAAIA,GAAGA,QAAQA,CAACA,IAAIA,CAACA,EAAeA,IAAIA,CAACA,CAACA;oBACnFA,MAAMA,CAACA;gBACXA,CAACA;gBAEDA,QAAQA,CAAWA,IAAIA,EAAeA,IAAIA,CAACA,CAACA;YAChDA,CAACA;QACLA,CAACA,CAACA,CAACA;IACPA,CAACA;IAEMH,iDAAoBA,GAA3BA,UAA4BA,iBAA0BA,EAAEA,QAAyBA;QAC7EI,IAAIA,GAAGA,GAAWA,IAAIA,CAACA,UAAUA,GAAGA,uBAAuBA,CAACA;QAC5DA,IAAIA,IAAIA,GAAmBA;YACvBA,cAAcA,EAAEA,IAAIA,CAACA,eAAeA;YACpCA,aAAaA,EAAEA,IAAIA,CAACA,cAAcA;YAClCA,KAAKA,EAAEA,iBAAiBA,CAACA,KAAKA;SACjCA,CAACA;QAEFA,IAAIA,CAACA,cAAcA,CAACA,OAAOA,CAACA,YAAcA,EAAEA,GAAGA,EAAEA,IAAIA,CAACA,SAASA,CAACA,IAAIA,CAACA,EAAEA,UAACA,KAAYA,EAAEA,QAAuBA;YACzGA,EAAEA,CAACA,CAACA,QAAQA,CAACA,CAACA,CAACA;gBACXA,EAAEA,CAACA,CAACA,KAAKA,CAACA,CAACA,CAACA;oBACRA,QAAQA,CAACA,KAAKA,EAAeA,IAAIA,CAACA,CAACA;oBACnCA,MAAMA,CAACA;gBACXA,CAACA;gBAEDA,EAAEA,CAACA,CAACA,QAAQA,CAACA,UAAUA,KAAKA,GAAGA,CAACA,CAACA,CAACA;oBAC9BA,QAAQA,CAACA,IAAIA,KAAKA,CAACA,QAAQA,CAACA,UAAUA,GAAGA,IAAIA,GAAGA,QAAQA,CAACA,IAAIA,CAACA,EAAeA,IAAIA,CAACA,CAACA;oBACnFA,MAAMA,CAACA;gBACXA,CAACA;gBAEDA,QAAQA,CAAWA,IAAIA,EAAeA,IAAIA,CAACA,CAACA;YAChDA,CAACA;QACLA,CAACA,CAACA,CAACA;IACPA,CAACA;IACLJ,yBAACA;AAADA,CA5KA,AA4KCA,IAAA;AA5KY,0BAAkB,qBA4K9B,CAAA;AAED,wBAAwB,MAAc;IAClCK,IAAIA,WAAWA,GAAGA,EAAEA,CAACA;IACrBA,IAAIA,OAAOA,GAAYA,IAAIA,CAACA;IAE5BA,GAAGA,CAACA,CAACA,GAAGA,CAACA,QAAQA,IAAIA,MAAMA,CAACA,CAACA,CAACA;QAC1BA,EAAEA,CAACA,CAACA,MAAMA,CAACA,cAAcA,CAACA,QAAQA,CAACA,CAACA,CAACA,CAACA;YAClCA,IAAIA,KAAKA,GAAiBA,MAAOA,CAACA,QAAQA,CAACA,CAACA;YAC5CA,EAAEA,CAACA,CAACA,CAACA,OAAOA,CAACA,CAACA,CAACA;gBACXA,WAAWA,IAAIA,GAAGA,CAACA;YACvBA,CAACA;YAEDA,WAAWA,IAAIA,kBAAkBA,CAACA,QAAQA,CAACA,GAAGA,GAAGA,CAACA;YAClDA,EAAEA,CAACA,CAACA,KAAKA,KAAKA,IAAIA,IAAIA,OAAOA,KAAKA,KAAKA,WAAWA,CAACA,CAACA,CAACA;gBACjDA,WAAWA,IAAIA,kBAAkBA,CAACA,KAAKA,CAACA,CAACA;YAC7CA,CAACA;YAEDA,OAAOA,GAAGA,KAAKA,CAACA;QACpBA,CAACA;IACLA,CAACA;IAEDA,MAAMA,CAACA,WAAWA,CAACA;AACvBA,CAACA", "file": "acquisition-sdk.js", "sourcesContent": ["/// <reference path=\"../definitions/harness.d.ts\" />\n\nimport { UpdateCheckResponse, UpdateCheckRequest, DeploymentStatusReport, DownloadReport } from \"rest-definitions\";\n\nexport module Http {\n    export const enum Verb {\n        GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS, CONNECT, PATCH\n    }\n\n    export interface Response {\n        statusCode: number;\n        body?: string;\n    }\n\n    export interface Requester {\n        request(verb: Verb, url: string, callback: Callback<Response>): void;\n        request(verb: Verb, url: string, requestBody: string, callback: Callback<Response>): void;\n    }\n}\n\n// All fields are non-nullable, except when retrieving the currently running package on the first run of the app,\n// in which case only the appVersion is compulsory\nexport interface Package {\n    deploymentKey: string;\n    description: string;\n    label: string;\n    appVersion: string;\n    isMandatory: boolean;\n    packageHash: string;\n    packageSize: number;\n}\n\nexport interface RemotePackage extends Package {\n    downloadUrl: string;\n}\n\nexport interface NativeUpdateNotification {\n    updateAppVersion: boolean;   // Always true\n    appVersion: string;\n}\n\nexport interface LocalPackage extends Package {\n    localPath: string;\n}\n\nexport interface Callback<T> { (error: Error, parameter: T): void; }\n\nexport interface Configuration {\n    appVersion: string;\n    clientUniqueId: string;\n    deploymentKey: string;\n    serverUrl: string;\n    ignoreAppVersion?: boolean\n}\n\nexport class AcquisitionStatus {\n    public static DeploymentSucceeded = \"DeploymentSucceeded\";\n    public static DeploymentFailed = \"DeploymentFailed\";\n}\n\nexport class AcquisitionManager {\n    private _appVersion: string;\n    private _clientUniqueId: string;\n    private _deploymentKey: string;\n    private _httpRequester: Http.Requester;\n    private _ignoreAppVersion: boolean;\n    private _serverUrl: string;\n\n    constructor(httpRequester: Http.Requester, configuration: Configuration) {\n        this._httpRequester = httpRequester;\n\n        this._serverUrl = configuration.serverUrl;\n        if (this._serverUrl.slice(-1) !== \"/\") {\n            this._serverUrl += \"/\";\n        }\n\n        this._appVersion = configuration.appVersion;\n        this._clientUniqueId = configuration.clientUniqueId;\n        this._deploymentKey = configuration.deploymentKey;\n        this._ignoreAppVersion = configuration.ignoreAppVersion;\n    }\n\n    public queryUpdateWithCurrentPackage(currentPackage: Package, callback?: Callback<RemotePackage | NativeUpdateNotification>): void {\n        if (!currentPackage || !currentPackage.appVersion) {\n            throw new Error(\"Calling common acquisition SDK with incorrect package\");  // Unexpected; indicates error in our implementation\n        }\n\n        var updateRequest: UpdateCheckRequest = {\n            deploymentKey: this._deploymentKey,\n            appVersion: currentPackage.appVersion,\n            packageHash: currentPackage.packageHash,\n            isCompanion: this._ignoreAppVersion,\n            label: currentPackage.label,\n            clientUniqueId: this._clientUniqueId\n        };\n\n        var requestUrl: string = this._serverUrl + \"updateCheck?\" + queryStringify(updateRequest);\n\n        this._httpRequester.request(Http.Verb.GET, requestUrl, (error: Error, response: Http.Response) => {\n            if (error) {\n                callback(error, /*remotePackage=*/ null);\n                return;\n            }\n\n            if (response.statusCode !== 200) {\n                let errorMessage: any;\n                if (response.statusCode === 0) {\n                    errorMessage = `Couldn't send request to ${requestUrl}, xhr.statusCode = 0 was returned. One of the possible reasons for that might be connection problems. Please, check your internet connection.`;\n                } else {\n                    errorMessage = `${response.statusCode}: ${response.body}`;\n                }\n                callback(new Error(errorMessage), /*remotePackage=*/ null);\n                return;\n            }\n            try {\n                var responseObject = JSON.parse(response.body);\n                var updateInfo: UpdateCheckResponse = responseObject.updateInfo;\n            } catch (error) {\n                callback(error, /*remotePackage=*/ null);\n                return;\n            }\n\n            if (!updateInfo) {\n                callback(error, /*remotePackage=*/ null);\n                return;\n            } else if (updateInfo.updateAppVersion) {\n                callback(/*error=*/ null, { updateAppVersion: true, appVersion: updateInfo.appVersion });\n                return;\n            } else if (!updateInfo.isAvailable) {\n                callback(/*error=*/ null, /*remotePackage=*/ null);\n                return;\n            }\n\n            var remotePackage: RemotePackage = {\n                deploymentKey: this._deploymentKey,\n                description: updateInfo.description,\n                label: updateInfo.label,\n                appVersion: updateInfo.appVersion,\n                isMandatory: updateInfo.isMandatory,\n                packageHash: updateInfo.packageHash,\n                packageSize: updateInfo.packageSize,\n                downloadUrl: updateInfo.downloadURL\n            };\n\n            callback(/*error=*/ null, remotePackage);\n        });\n    }\n\n    public reportStatusDeploy(deployedPackage?: Package, status?: string, previousLabelOrAppVersion?: string, previousDeploymentKey?: string, callback?: Callback<void>): void {\n        var url: string = this._serverUrl + \"reportStatus/deploy\";\n        var body: DeploymentStatusReport = {\n            appVersion: this._appVersion,\n            deploymentKey: this._deploymentKey\n        };\n\n        if (this._clientUniqueId) {\n            body.clientUniqueId = this._clientUniqueId;\n        }\n\n        if (deployedPackage) {\n            body.label = deployedPackage.label;\n            body.appVersion = deployedPackage.appVersion;\n\n            switch (status) {\n                case AcquisitionStatus.DeploymentSucceeded:\n                case AcquisitionStatus.DeploymentFailed:\n                    body.status = status;\n                    break;\n\n                default:\n                    if (callback) {\n                        if (!status) {\n                            callback(new Error(\"Missing status argument.\"), /*not used*/ null);\n                        } else {\n                            callback(new Error(\"Unrecognized status \\\"\" + status + \"\\\".\"), /*not used*/ null);\n                        }\n                    }\n                    return;\n            }\n        }\n\n        if (previousLabelOrAppVersion) {\n            body.previousLabelOrAppVersion = previousLabelOrAppVersion;\n        }\n\n        if (previousDeploymentKey) {\n            body.previousDeploymentKey = previousDeploymentKey;\n        }\n\n        callback = typeof arguments[arguments.length - 1] === \"function\" && arguments[arguments.length - 1];\n\n        this._httpRequester.request(Http.Verb.POST, url, JSON.stringify(body), (error: Error, response: Http.Response): void => {\n            if (callback) {\n                if (error) {\n                    callback(error, /*not used*/ null);\n                    return;\n                }\n\n                if (response.statusCode !== 200) {\n                    callback(new Error(response.statusCode + \": \" + response.body), /*not used*/ null);\n                    return;\n                }\n\n                callback(/*error*/ null, /*not used*/ null);\n            }\n        });\n    }\n\n    public reportStatusDownload(downloadedPackage: Package, callback?: Callback<void>): void {\n        var url: string = this._serverUrl + \"reportStatus/download\";\n        var body: DownloadReport = {\n            clientUniqueId: this._clientUniqueId,\n            deploymentKey: this._deploymentKey,\n            label: downloadedPackage.label\n        };\n\n        this._httpRequester.request(Http.Verb.POST, url, JSON.stringify(body), (error: Error, response: Http.Response): void => {\n            if (callback) {\n                if (error) {\n                    callback(error, /*not used*/ null);\n                    return;\n                }\n\n                if (response.statusCode !== 200) {\n                    callback(new Error(response.statusCode + \": \" + response.body), /*not used*/ null);\n                    return;\n                }\n\n                callback(/*error*/ null, /*not used*/ null);\n            }\n        });\n    }\n}\n\nfunction queryStringify(object: Object): string {\n    var queryString = \"\";\n    var isFirst: boolean = true;\n\n    for (var property in object) {\n        if (object.hasOwnProperty(property)) {\n            var value: string = (<any>object)[property];\n            if (!isFirst) {\n                queryString += \"&\";\n            }\n\n            queryString += encodeURIComponent(property) + \"=\";\n            if (value !== null && typeof value !== \"undefined\") {\n                queryString += encodeURIComponent(value);\n            }\n\n            isFirst = false;\n        }\n    }\n\n    return queryString;\n}"], "sourceRoot": "../.."}
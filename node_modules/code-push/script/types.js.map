{"version": 3, "sources": ["script/types.ts"], "names": [], "mappings": "AAmBuD", "file": "types.js", "sourcesContent": ["export { AccessKeyRequest, Account, App, AppCreationRequest, CollaboratorMap, CollaboratorProperties, Deployment, DeploymentMetrics, Package, PackageInfo, AccessKey as ServerAccessKey, UpdateMetrics } from \"rest-definitions\";\n\nexport interface CodePushError {\n    message: string;\n    statusCode: number;\n}\n\nexport interface AccessKey {\n    createdTime: number;\n    expires: number;\n    name: string;\n    key?: string;\n}\n\nexport interface Session {\n    loggedInTime: number;\n    machineName: string;\n}\n\nexport type Headers = { [headerName: string]: string };\n"], "sourceRoot": "../.."}
{"_from": "code-push", "_id": "code-push@2.0.6", "_inBundle": false, "_integrity": "sha512-3ln6rqE9KkYUhSzgjKVOAd2pUijBFY3QG951fgIiG3uNGg8V57XuYmftPq3EGS0YTxX2SPEUOQvHPhw7Wxmggw==", "_location": "/code-push", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "code-push", "name": "code-push", "escapedName": "code-push", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/code-push/-/code-push-2.0.6.tgz", "_shasum": "fb4e140c90fdb7fa047f3be6f53caacc8915d0b8", "_spec": "code-push", "_where": "D:\\projects\\soil-samples-app", "author": {"name": "Microsoft Corporation"}, "bugs": {"url": "https://github.com/Microsoft/code-push/issues"}, "bundleDependencies": false, "dependencies": {"q": "^1.4.1", "recursive-fs": "0.1.4", "slash": "1.0.0", "superagent": "^3.8.0", "superagent-proxy": "^1.0.3", "yazl": "^2.4.1"}, "deprecated": false, "description": "Management SDK for the CodePush service", "homepage": "https://microsoft.github.io/code-push", "license": "MIT", "main": "script/index.js", "name": "code-push", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/code-push.git"}, "scripts": {"test": "gulp"}, "version": "2.0.6"}
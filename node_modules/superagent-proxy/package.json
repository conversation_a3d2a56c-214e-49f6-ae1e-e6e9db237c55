{"name": "superagent-proxy", "version": "2.1.0", "description": "`Request#proxy(uri)` superagent extension", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "engines": {"node": ">=6"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/superagent-proxy.git"}, "keywords": ["superagent", "http", "https", "proxy", "socks"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/superagent-proxy/issues"}, "dependencies": {"proxy-agent": "^4.0.0", "debug": "^3.1.0"}, "peerDependencies": {"superagent": ">= 0.15.4 || 1 || 2 || 3"}, "devDependencies": {"mocha": "2", "superagent": ">= 0.15.4 || 1 || 2 || 3"}}
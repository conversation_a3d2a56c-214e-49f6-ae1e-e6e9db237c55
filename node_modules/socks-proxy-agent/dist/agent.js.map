{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../src/agent.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,8CAAsB;AAEtB,8CAAsB;AACtB,8CAAsB;AACtB,kDAAgC;AAChC,2CAAkE;AAClE,iCAAoE;AAGpE,MAAM,KAAK,GAAG,eAAW,CAAC,mBAAmB,CAAC,CAAC;AAE/C,SAAS,SAAS,CAAC,IAAY;IAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACtC,aAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC7B,IAAI,GAAG,EAAE;gBACR,MAAM,CAAC,GAAG,CAAC,CAAC;aACZ;iBAAM;gBACN,OAAO,CAAC,GAAG,CAAC,CAAC;aACb;QACF,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACvB,IAA4B;IAE5B,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAI,IAAI,GAAuB,CAAC,CAAC;IAEjC,0DAA0D;IAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC;IACxC,IAAI,CAAC,IAAI,EAAE;QACV,MAAM,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC;KACjC;IAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QAClC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;KACjB;SAAM,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QACzC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KAC/B;IAED,0EAA0E;IAC1E,iEAAiE;IACjE,IAAI,CAAC,IAAI,EAAE;QACV,IAAI,GAAG,IAAI,CAAC;KACZ;IAED,sEAAsE;IACtE,iBAAiB;IACjB,IAAI,IAAI,CAAC,QAAQ,EAAE;QAClB,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;YACvC,KAAK,QAAQ;gBACZ,MAAM,GAAG,IAAI,CAAC;YACf,eAAe;YACf,KAAK,SAAS;gBACb,IAAI,GAAG,CAAC,CAAC;gBACT,MAAM;YACP,KAAK,QAAQ;gBACZ,MAAM,GAAG,IAAI,CAAC;YACf,eAAe;YACf,KAAK,OAAO,CAAC,CAAC,sCAAsC;YACpD,KAAK,SAAS;gBACb,IAAI,GAAG,CAAC,CAAC;gBACT,MAAM;YACP;gBACC,MAAM,IAAI,SAAS,CAClB,8CAA8C,IAAI,CAAC,QAAQ,EAAE,CAC7D,CAAC;SACH;KACD;IAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;QACrC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;YACvC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;SACjB;aAAM;YACN,MAAM,IAAI,SAAS,CAAC,+BAA+B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;SAChE;KACD;IAED,MAAM,KAAK,GAAe;QACzB,IAAI;QACJ,IAAI;QACJ,IAAI;KACJ,CAAC;IAEF,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC;IAC1C,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC7B,IAAI,IAAI,CAAC,IAAI,EAAE;QACd,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACjB,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;KACnB;IACD,IAAI,MAAM,EAAE;QACX,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE;YACtC,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SACjB,CAAC,CAAC;KACH;IACD,IAAI,QAAQ,EAAE;QACb,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE;YACxC,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,KAAK;SACjB,CAAC,CAAC;KACH;IAED,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC1B,CAAC;AAED;;;;GAIG;AACH,MAAqB,eAAgB,SAAQ,kBAAK;IAIjD,YAAY,KAAsC;QACjD,IAAI,IAA4B,CAAC;QACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC9B,IAAI,GAAG,aAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACxB;aAAM;YACN,IAAI,GAAG,KAAK,CAAC;SACb;QACD,IAAI,CAAC,IAAI,EAAE;YACV,MAAM,IAAI,SAAS,CAClB,2DAA2D,CAC3D,CAAC;SACF;QACD,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,MAAM,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACG,QAAQ,CACb,GAAkB,EAClB,IAAoB;;YAEpB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;YAC/B,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YAEnC,IAAI,CAAC,IAAI,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;aACtC;YAED,IAAI,MAAM,EAAE;gBACX,mEAAmE;gBACnE,IAAI,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC;aAC7B;YAED,MAAM,SAAS,GAAuB;gBACrC,KAAK;gBACL,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;gBAC3B,OAAO,EAAE,SAAS;gBAClB,OAAO;aACP,CAAC;YACF,KAAK,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAC;YACxD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,mBAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACjE,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAErD,IAAI,IAAI,CAAC,cAAc,EAAE;gBACxB,sDAAsD;gBACtD,8CAA8C;gBAC9C,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC;gBAC3C,OAAO,aAAG,CAAC,OAAO,iCACd,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,KACjD,MAAM;oBACN,UAAU,IACT,CAAC;aACH;YAED,OAAO,MAAM,CAAC;QACf,CAAC;KAAA;CACD;AArED,kCAqEC;AAED,SAAS,IAAI,CACZ,GAAM,EACN,GAAG,IAAO;IAIV,MAAM,GAAG,GAAG,EAEX,CAAC;IACF,IAAI,GAAqB,CAAC;IAC1B,KAAK,GAAG,IAAI,GAAG,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;SACpB;KACD;IACD,OAAO,GAAG,CAAC;AACZ,CAAC"}
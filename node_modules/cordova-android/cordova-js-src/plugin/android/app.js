/*
 *
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 *
*/

var exec = require('cordova/exec');
var APP_PLUGIN_NAME = Number(require('cordova').platformVersion.split('.')[0]) >= 4 ? 'CoreAndroid' : 'App';

module.exports = {
    /**
    * Clear the resource cache.
    */
    clearCache: function () {
        exec(null, null, APP_PLUGIN_NAME, 'clearCache', []);
    },

    /**
    * Load the url into the webview or into new browser instance.
    *
    * @param url           The URL to load
    * @param props         Properties that can be passed in to the activity:
    *      wait: int                           => wait msec before loading URL
    *      loadingDialog: "Title,Message"      => display a native loading dialog
    *      loadUrlTimeoutValue: int            => time in msec to wait before triggering a timeout error
    *      clearHistory: boolean              => clear webview history (default=false)
    *      openExternal: boolean              => open in a new browser (default=false)
    *
    * Example:
    *      navigator.app.loadUrl("http://server/myapp/index.html", {wait:2000, loadingDialog:"Wait,Loading App", loadUrlTimeoutValue: 60000});
    */
    loadUrl: function (url, props) {
        exec(null, null, APP_PLUGIN_NAME, 'loadUrl', [url, props]);
    },

    /**
    * Cancel loadUrl that is waiting to be loaded.
    */
    cancelLoadUrl: function () {
        exec(null, null, APP_PLUGIN_NAME, 'cancelLoadUrl', []);
    },

    /**
    * Clear web history in this web view.
    * Instead of BACK button loading the previous web page, it will exit the app.
    */
    clearHistory: function () {
        exec(null, null, APP_PLUGIN_NAME, 'clearHistory', []);
    },

    /**
    * Go to previous page displayed.
    * This is the same as pressing the backbutton on Android device.
    */
    backHistory: function () {
        exec(null, null, APP_PLUGIN_NAME, 'backHistory', []);
    },

    /**
    * Override the default behavior of the Android back button.
    * If overridden, when the back button is pressed, the "backKeyDown" JavaScript event will be fired.
    *
    * Note: The user should not have to call this method.  Instead, when the user
    *       registers for the "backbutton" event, this is automatically done.
    *
    * @param override        T=override, F=cancel override
    */
    overrideBackbutton: function (override) {
        exec(null, null, APP_PLUGIN_NAME, 'overrideBackbutton', [override]);
    },

    /**
    * Override the default behavior of the Android volume button.
    * If overridden, when the volume button is pressed, the "volume[up|down]button"
    * JavaScript event will be fired.
    *
    * Note: The user should not have to call this method.  Instead, when the user
    *       registers for the "volume[up|down]button" event, this is automatically done.
    *
    * @param button          volumeup, volumedown
    * @param override        T=override, F=cancel override
    */
    overrideButton: function (button, override) {
        exec(null, null, APP_PLUGIN_NAME, 'overrideButton', [button, override]);
    },

    /**
    * Exit and terminate the application.
    */
    exitApp: function () {
        return exec(null, null, APP_PLUGIN_NAME, 'exitApp', []);
    }
};

/* Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
*/

ext {
    apply from: 'cordova.gradle'
    cdvCompileSdkVersion = privateHelpers.getProjectTarget()
    cdvBuildToolsVersion = privateHelpers.findLatestInstalledBuildTools()
}

buildscript {
    repositories {
        mavenCentral()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:2.2.3'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.5'
        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.7.3'
    }
}

apply plugin: 'com.android.library'
apply plugin: 'com.github.dcendents.android-maven'
apply plugin: 'com.jfrog.bintray'

group = 'org.apache.cordova'
version = '6.2.3'

android {
    compileSdkVersion cdvCompileSdkVersion
    buildToolsVersion cdvBuildToolsVersion
    publishNonDefault true

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_6
        targetCompatibility JavaVersion.VERSION_1_6
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src']
            resources.srcDirs = ['src']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
        }
    }

    packagingOptions {
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
    }
}

install {
    repositories.mavenInstaller {
        pom {
            project {
                packaging 'aar'
                name 'Cordova'
                url 'https://cordova.apache.org'
                licenses {
                    license {
                        name 'The Apache Software License, Version 2.0'
                        url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }
                developers {
                    developer {
                        id 'stevengill'
                        name 'Steve Gill'
                    }
                }
                scm {
                    connection 'https://git-wip-us.apache.org/repos/asf?p=cordova-android.git'
                    developerConnection 'https://git-wip-us.apache.org/repos/asf?p=cordova-android.git'
                    url 'https://git-wip-us.apache.org/repos/asf?p=cordova-android'

                }
            }
        }
    }
}

task sourcesJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier = 'sources'
}

artifacts {
    archives sourcesJar
}

bintray {
    user = System.getenv('BINTRAY_USER')
    key = System.getenv('BINTRAY_KEY')
    configurations = ['archives']
    pkg {
        repo = 'maven'
        name = 'cordova-android'
        userOrg = 'cordova'
        licenses = ['Apache-2.0']
        vcsUrl = 'https://git-wip-us.apache.org/repos/asf?p=cordova-android.git'
        websiteUrl = 'https://cordova.apache.org'
        issueTrackerUrl = 'https://issues.apache.org/jira/browse/CB'
        publicDownloadNumbers = true
        licenses = ['Apache-2.0']
        labels = ['android', 'cordova', 'phonegap']
        version {
            name = '6.2.3'
            released  = new Date()
            vcsTag = '6.2.3'
        }
    }
}

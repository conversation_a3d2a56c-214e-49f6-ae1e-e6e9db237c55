#!/usr/bin/env node

/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/
var path   = require('path');
var Api = require('./templates/cordova/Api');
var args  = require('nopt')({
    'link': Boolean,
    'shared': Boolean,
    'help': Boolean
}, { 'd' : '--verbose' });

if (args.help || args.argv.remain.length === 0) {
    console.log('Usage: ' + path.relative(process.cwd(), path.join(__dirname, 'update')) + ' <path_to_project> [--link]');
    console.log('    --link will use the CordovaLib project directly instead of making a copy.');
    process.exit(1);
}

require('./templates/cordova/loggingHelper').adjustLoggerLevel(args);

Api.updatePlatform(args.argv.remain[0], {link: (args.link || args.shared)}).done();

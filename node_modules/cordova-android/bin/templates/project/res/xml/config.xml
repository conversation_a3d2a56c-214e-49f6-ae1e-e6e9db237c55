<?xml version="1.0" encoding="UTF-8"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
-->
<widget xmlns     = "http://www.w3.org/ns/widgets"
        id        = "io.cordova.helloCordova"
        version   = "2.0.0">
    <name>Hello Cordova</name>

    <description>
        A sample Apache Cordova application that responds to the deviceready event.
    </description>

    <author href="http://cordova.io" email="<EMAIL>">
        Apache Cordova Team
    </author>

    <!-- <content src="http://mysite.com/myapp.html" /> for external pages -->
    <content src="index.html" />

    <!-- Whitelist docs: https://github.com/apache/cordova-plugin-whitelist -->
    <access origin="*" />
    <!-- Grant certain URLs the ability to launch external applications. This
         behaviour is set to match that of Cordova versions before 3.6.0, and
         should be reviewed before launching an application in production. It
         may be changed in the future. -->
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <allow-intent href="market:*" />

    <preference name="loglevel" value="DEBUG" />
    <!--
      <preference name="splashscreen" value="splash" />
      <preference name="backgroundColor" value="0xFFF" />
      <preference name="loadUrlTimeoutValue" value="20000" />
      <preference name="InAppBrowserStorageEnabled" value="true" />
      <preference name="disallowOverscroll" value="true" />
    -->
</widget>

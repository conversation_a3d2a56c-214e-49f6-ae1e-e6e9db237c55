<?xml version="1.0" encoding="utf-8"?>
<!--
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
  package="__PACKAGE__" android:versionName="1.0" android:versionCode="1" android:hardwareAccelerated="true">
    <supports-screens
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true"
        android:resizeable="true"
        android:anyDensity="true"
        />

    <uses-permission android:name="android.permission.INTERNET" />

    <application android:icon="@mipmap/icon" android:label="@string/app_name"
        android:hardwareAccelerated="true" android:supportsRtl="true">
        <activity android:name="__ACTIVITY__"
                android:label="@string/activity_name"
                android:launchMode="singleTop"
                android:theme="@android:style/Theme.DeviceDefault.NoActionBar"
                android:windowSoftInputMode="adjustResize"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale">
            <intent-filter android:label="@string/launcher_name">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

    <uses-sdk android:minSdkVersion="16" android:targetSdkVersion="__APILEVEL__"/>
</manifest>

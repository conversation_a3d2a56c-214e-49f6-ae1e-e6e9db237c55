{"_args": [[{"raw": "brace-expansion@^1.0.0", "scope": null, "escapedName": "brace-expansion", "name": "brace-expansion", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/minimatch"]], "_from": "brace-expansion@>=1.0.0 <2.0.0", "_id": "brace-expansion@1.1.7", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/brace-expansion", "_nodeVersion": "7.8.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.7.tgz_1491552830231_0.7213963181711733"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.2.0", "_phantomChildren": {}, "_requested": {"raw": "brace-expansion@^1.0.0", "scope": null, "escapedName": "brace-expansion", "name": "brace-expansion", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/minimatch"], "_resolved": "http://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.7.tgz", "_shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "_shrinkwrap": null, "_spec": "brace-expansion@^1.0.0", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/minimatch", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "description": "Brace expansion as known from sh/bash", "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "directories": {}, "dist": {"shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.7.tgz"}, "gitHead": "892512024872ca7680554be90f6e8ce065053372", "homepage": "https://github.com/juliangruber/brace-expansion", "keywords": [], "license": "MIT", "main": "index.js", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "name": "brace-expansion", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "scripts": {"bench": "matcha test/perf/bench.js", "gentest": "bash test/generate.sh", "test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.1.7"}
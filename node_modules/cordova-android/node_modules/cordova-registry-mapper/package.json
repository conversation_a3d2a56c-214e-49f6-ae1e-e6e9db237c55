{"_args": [[{"raw": "cordova-registry-mapper@^1.1.8", "scope": null, "escapedName": "cordova-registry-mapper", "name": "cordova-registry-mapper", "rawSpec": "^1.1.8", "spec": ">=1.1.8 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "cordova-registry-mapper@>=1.1.8 <2.0.0", "_id": "cordova-registry-mapper@1.1.15", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/cordova-registry-mapper", "_nodeVersion": "5.4.1", "_npmUser": {"name": "ste<PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.5.3", "_phantomChildren": {}, "_requested": {"raw": "cordova-registry-mapper@^1.1.8", "scope": null, "escapedName": "cordova-registry-mapper", "name": "cordova-registry-mapper", "rawSpec": "^1.1.8", "spec": ">=1.1.8 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/cordova-registry-mapper/-/cordova-registry-mapper-1.1.15.tgz", "_shasum": "e244b9185b8175473bff6079324905115f83dc7c", "_shrinkwrap": null, "_spec": "cordova-registry-mapper@^1.1.8", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/stevengill/cordova-registry-mapper/issues"}, "dependencies": {}, "description": "Maps old plugin ids to new plugin names for fetching from npm", "devDependencies": {"tape": "^3.5.0"}, "directories": {}, "dist": {"shasum": "e244b9185b8175473bff6079324905115f83dc7c", "tarball": "https://registry.npmjs.org/cordova-registry-mapper/-/cordova-registry-mapper-1.1.15.tgz"}, "gitHead": "00af0f028ec94154a364eeabe38b8e22320647bd", "homepage": "https://github.com/stevengill/cordova-registry-mapper#readme", "keywords": ["<PERSON><PERSON>", "plugins"], "license": "Apache version 2.0", "main": "index.js", "maintainers": [{"name": "ste<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "cordova-registry-mapper", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/stevengill/cordova-registry-mapper.git"}, "scripts": {"test": "node tests/test.js"}, "version": "1.1.15"}
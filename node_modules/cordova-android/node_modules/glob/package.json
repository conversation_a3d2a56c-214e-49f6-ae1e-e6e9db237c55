{"_args": [[{"raw": "glob@^5.0.13", "scope": null, "escapedName": "glob", "name": "glob", "rawSpec": "^5.0.13", "spec": ">=5.0.13 <6.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "glob@>=5.0.13 <6.0.0", "_id": "glob@5.0.15", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/glob", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.3.2", "_phantomChildren": {}, "_requested": {"raw": "glob@^5.0.13", "scope": null, "escapedName": "glob", "name": "glob", "rawSpec": "^5.0.13", "spec": ">=5.0.13 <6.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "_shasum": "1bc936b9e02f4a603fcc222ecf7633d30b8b93b1", "_shrinkwrap": null, "_spec": "glob@^5.0.13", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/node-glob/issues"}, "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "description": "a little globber", "devDependencies": {"mkdirp": "0", "rimraf": "^2.2.8", "tap": "^1.1.4", "tick": "0.0.6"}, "directories": {}, "dist": {"shasum": "1bc936b9e02f4a603fcc222ecf7633d30b8b93b1", "tarball": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz"}, "engines": {"node": "*"}, "files": ["glob.js", "sync.js", "common.js"], "gitHead": "3a7e71d453dd80e75b196fd262dd23ed54beeceb", "homepage": "https://github.com/isaacs/node-glob#readme", "license": "ISC", "main": "glob.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "glob", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-glob.git"}, "scripts": {"bench": "bash benchmark.sh", "benchclean": "node benchclean.js", "prepublish": "npm run benchclean", "prof": "bash prof.sh && cat profile.txt", "profclean": "rm -f v8.log profile.txt", "test": "tap test/*.js --cov", "test-regen": "npm run profclean && TEST_REGEN=1 node test/00-setup.js"}, "version": "5.0.15"}
{"_args": [[{"raw": "big-integer@^1.6.7", "scope": null, "escapedName": "big-integer", "name": "big-integer", "rawSpec": "^1.6.7", "spec": ">=1.6.7 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/bplist-parser"]], "_from": "big-integer@>=1.6.7 <2.0.0", "_id": "big-integer@1.6.22", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/big-integer", "_nodeVersion": "6.9.4", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/big-integer-1.6.22.tgz_1493091323169_0.5048394540790468"}, "_npmUser": {"name": "peterolson", "email": "<EMAIL>"}, "_npmVersion": "3.10.10", "_phantomChildren": {}, "_requested": {"raw": "big-integer@^1.6.7", "scope": null, "escapedName": "big-integer", "name": "big-integer", "rawSpec": "^1.6.7", "spec": ">=1.6.7 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/bplist-parser"], "_resolved": "https://registry.npmjs.org/big-integer/-/big-integer-1.6.22.tgz", "_shasum": "487c95fce886022ea48ff5f19e388932df46dd2e", "_shrinkwrap": null, "_spec": "big-integer@^1.6.7", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/bplist-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {}, "bugs": {"url": "https://github.com/peterolson/BigInteger.js/issues"}, "contributors": [], "dependencies": {}, "description": "An arbitrary length integer library for Javascript", "devDependencies": {"coveralls": "^2.11.4", "jasmine": "2.1.x", "jasmine-core": "^2.3.4", "karma": "^0.13.3", "karma-coverage": "^0.4.2", "karma-jasmine": "^0.3.6", "karma-phantomjs-launcher": "~0.1", "uglifyjs": "^2.4.10"}, "directories": {}, "dist": {"shasum": "487c95fce886022ea48ff5f19e388932df46dd2e", "tarball": "https://registry.npmjs.org/big-integer/-/big-integer-1.6.22.tgz"}, "engines": {"node": ">=0.6"}, "gitHead": "40483b881b4380931e5af6f2f8a161b6caa71690", "homepage": "https://github.com/peterolson/BigInteger.js#readme", "keywords": ["math", "big", "bignum", "bigint", "biginteger", "integer", "arbitrary", "precision", "arithmetic"], "license": "Unlicense", "main": "./BigInteger", "maintainers": [{"name": "peterolson", "email": "<EMAIL>"}], "name": "big-integer", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+ssh://**************/peterolson/BigInteger.js.git"}, "scripts": {"minify": "uglifyjs BigInteger.js -o BigInteger.min.js", "test": "karma start my.conf.js"}, "version": "1.6.22"}
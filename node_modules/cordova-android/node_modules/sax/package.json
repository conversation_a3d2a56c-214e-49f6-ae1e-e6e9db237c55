{"_args": [[{"raw": "sax@0.3.5", "scope": null, "escapedName": "sax", "name": "sax", "rawSpec": "0.3.5", "spec": "0.3.5", "type": "version"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/elementtree"]], "_defaultsLoaded": true, "_engineSupported": true, "_from": "sax@0.3.5", "_id": "sax@0.3.5", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/sax", "_nodeVersion": "v0.6.7-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "1.1.0-beta-7", "_phantomChildren": {}, "_requested": {"raw": "sax@0.3.5", "scope": null, "escapedName": "sax", "name": "sax", "rawSpec": "0.3.5", "spec": "0.3.5", "type": "version"}, "_requiredBy": ["/cordova-android/elementtree"], "_resolved": "http://registry.npmjs.org/sax/-/sax-0.3.5.tgz", "_shasum": "88fcfc1f73c0c8bbd5b7c776b6d3f3501eed073d", "_shrinkwrap": null, "_spec": "sax@0.3.5", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/elementtree", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/sax-js/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "An evented streaming XML parser in JavaScript", "devDependencies": {}, "directories": {}, "dist": {"shasum": "88fcfc1f73c0c8bbd5b7c776b6d3f3501eed073d", "tarball": "https://registry.npmjs.org/sax/-/sax-0.3.5.tgz"}, "engines": {"node": "*"}, "homepage": "https://github.com/isaacs/sax-js#readme", "license": {"type": "MIT", "url": "https://raw.github.com/isaacs/sax-js/master/LICENSE"}, "main": "lib/sax.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "sax", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/isaacs/sax-js.git"}, "scripts": {"test": "node test/index.js"}, "version": "0.3.5"}
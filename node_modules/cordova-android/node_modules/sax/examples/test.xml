<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE RootElement [
	<!ENTITY e SYSTEM "001.ent">
]>
<RootElement param="value">
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
    <FirstElement>
        Some Text
    </FirstElement>
    <?some_pi some_attr="some_value"?>
    <!-- this is a comment -- but this isnt part of the comment -->
    <!-- this is a comment == and this is a part of the comment -->
    <!-- this is a comment > and this is also part of the thing -->
    <!invalid comment>
    <![CDATA[ this is random stuff. & and < and > are ok in here. ]]>
    <SecondElement param2="something">
        Pre-Text &amp; <Inline>Inlined text</Inline> Post-text.
        &#xF8FF;
    </SecondElement>
</RootElement>
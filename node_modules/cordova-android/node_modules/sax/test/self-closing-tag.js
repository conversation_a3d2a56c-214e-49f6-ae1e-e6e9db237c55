
require(__dirname).test({
  xml :
  "<root>   "+
    "<haha /> "+
    "<haha/>  "+
    "<monkey> "+
      "=(|)     "+
    "</monkey>"+
  "</root>  ",
  expect : [
    ["opentag", {name:"RO<PERSON>", attributes:{}}],
    ["opentag", {name:"<PERSON><PERSON><PERSON>", attributes:{}}],
    ["closetag", "HAHA"],
    ["opentag", {name:"<PERSON><PERSON><PERSON>", attributes:{}}],
    ["closetag", "H<PERSON><PERSON>"],
    // ["opentag", {name:"<PERSON><PERSON><PERSON>", attributes:{}}],
    // ["closetag", "HAHA"],
    ["opentag", {name:"<PERSON><PERSON><PERSON><PERSON>", attributes:{}}],
    ["text", "=(|)"],
    ["closetag", "MONKE<PERSON>"],
    ["closetag", "ROOT"]
  ],
  opt : { trim : true }
});
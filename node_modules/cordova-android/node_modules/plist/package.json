{"_args": [[{"raw": "plist@^1.2.0", "scope": null, "escapedName": "plist", "name": "plist", "rawSpec": "^1.2.0", "spec": ">=1.2.0 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "plist@>=1.2.0 <2.0.0", "_id": "plist@1.2.0", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/plist", "_nodeVersion": "5.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.3.11", "_phantomChildren": {}, "_requested": {"raw": "plist@^1.2.0", "scope": null, "escapedName": "plist", "name": "plist", "rawSpec": "^1.2.0", "spec": ">=1.2.0 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/plist/-/plist-1.2.0.tgz", "_shasum": "084b5093ddc92506e259f874b8d9b1afb8c79593", "_shrinkwrap": null, "_spec": "plist@^1.2.0", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/TooTallNate/node-plist/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "dependencies": {"base64-js": "0.0.8", "util-deprecate": "1.0.2", "xmlbuilder": "4.0.0", "xmldom": "0.1.x"}, "description": "Mac OS X Plist parser/builder for Node.js and browsers", "devDependencies": {"browserify": "12.0.1", "mocha": "2.3.3", "multiline": "1.0.2", "zuul": "3.7.2"}, "directories": {}, "dist": {"shasum": "084b5093ddc92506e259f874b8d9b1afb8c79593", "tarball": "https://registry.npmjs.org/plist/-/plist-1.2.0.tgz"}, "gitHead": "69520574f27864145192338b72e608fbe1bda6f7", "homepage": "https://github.com/TooTallNate/node-plist#readme", "keywords": ["apple", "browser", "mac", "plist", "parser", "xml"], "license": "MIT", "main": "lib/plist.js", "maintainers": [{"name": "TooTallNate", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "plist", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-plist.git"}, "scripts": {"test": "make test"}, "version": "1.2.0"}
{"_args": [[{"raw": "underscore@^1.8.3", "scope": null, "escapedName": "underscore", "name": "underscore", "rawSpec": "^1.8.3", "spec": ">=1.8.3 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "underscore@>=1.8.3 <2.0.0", "_id": "underscore@1.8.3", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/underscore", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_npmVersion": "1.4.28", "_phantomChildren": {}, "_requested": {"raw": "underscore@^1.8.3", "scope": null, "escapedName": "underscore", "name": "underscore", "rawSpec": "^1.8.3", "spec": ">=1.8.3 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/underscore/-/underscore-1.8.3.tgz", "_shasum": "4f3fb53b106e6097fcf9cb4109f2a5e9bdfa5022", "_shrinkwrap": null, "_spec": "underscore@^1.8.3", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jashkenas/underscore/issues"}, "dependencies": {}, "description": "JavaScript's functional programming helper library.", "devDependencies": {"docco": "*", "eslint": "0.6.x", "karma": "~0.12.31", "karma-qunit": "~0.1.4", "qunit-cli": "~0.2.0", "uglify-js": "2.4.x"}, "directories": {}, "dist": {"shasum": "4f3fb53b106e6097fcf9cb4109f2a5e9bdfa5022", "tarball": "https://registry.npmjs.org/underscore/-/underscore-1.8.3.tgz"}, "files": ["underscore.js", "underscore-min.js", "underscore-min.map", "LICENSE"], "gitHead": "e4743ab712b8ab42ad4ccb48b155034d02394e4d", "homepage": "http://underscorejs.org", "keywords": ["util", "functional", "server", "client", "browser"], "license": "MIT", "main": "underscore.js", "maintainers": [{"name": "jashkenas", "email": "<EMAIL>"}], "name": "underscore", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/jashkenas/underscore.git"}, "scripts": {"build": "uglifyjs underscore.js -c \"evaluate=false\" --comments \"/    .*/\" -m --source-map underscore-min.map -o underscore-min.js", "doc": "docco underscore.js", "lint": "eslint underscore.js test/*.js", "test": "npm run test-node && npm run lint", "test-browser": "npm i karma-phantomjs-launcher && ./node_modules/karma/bin/karma start", "test-node": "qunit-cli test/*.js"}, "version": "1.8.3"}
{"_args": [[{"raw": "os-tmpdir@^1.0.0", "scope": null, "escapedName": "os-tmpdir", "name": "os-tmpdir", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/osenv"]], "_from": "os-tmpdir@>=1.0.0 <2.0.0", "_id": "os-tmpdir@1.0.2", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/os-tmpdir", "_nodeVersion": "6.6.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/os-tmpdir-1.0.2.tgz_1475211274587_0.14931037812493742"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.10.3", "_phantomChildren": {}, "_requested": {"raw": "os-tmpdir@^1.0.0", "scope": null, "escapedName": "os-tmpdir", "name": "os-tmpdir", "rawSpec": "^1.0.0", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/osenv"], "_resolved": "http://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "_shasum": "bbe67406c79aa85c5cfec766fe5734555dfa1274", "_shrinkwrap": null, "_spec": "os-tmpdir@^1.0.0", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/osenv", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/os-tmpdir/issues"}, "dependencies": {}, "description": "Node.js os.tmpdir() ponyfill", "devDependencies": {"ava": "*", "xo": "^0.16.0"}, "directories": {}, "dist": {"shasum": "bbe67406c79aa85c5cfec766fe5734555dfa1274", "tarball": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "gitHead": "1abf9cf5611b4be7377060ea67054b45cbf6813c", "homepage": "https://github.com/sindresorhus/os-tmpdir#readme", "keywords": ["built-in", "core", "ponyfill", "polyfill", "shim", "os", "tmpdir", "tempdir", "tmp", "temp", "dir", "directory", "env", "environment"], "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "os-tmpdir", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/os-tmpdir.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}
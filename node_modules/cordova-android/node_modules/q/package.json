{"_args": [[{"raw": "q@^1.4.1", "scope": null, "escapedName": "q", "name": "q", "rawSpec": "^1.4.1", "spec": ">=1.4.1 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android"]], "_from": "q@>=1.4.1 <2.0.0", "_id": "q@1.5.0", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/q", "_nodeVersion": "6.9.5", "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/q-1.5.0.tgz_1490148893963_0.4695124195422977"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.10.10", "_phantomChildren": {}, "_requested": {"raw": "q@^1.4.1", "scope": null, "escapedName": "q", "name": "q", "rawSpec": "^1.4.1", "spec": ">=1.4.1 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android", "/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/q/-/q-1.5.0.tgz", "_shasum": "dd01bac9d06d30e6f219aecb8253ee9ebdc308f1", "_shrinkwrap": null, "_spec": "q@^1.4.1", "_where": "/Users/<USER>/repo/cordova/cordova-android", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kriskowal"}, "bugs": {"url": "http://github.com/kriskowal/q/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kriskowal"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeditoolkit.com"}, {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}], "dependencies": {}, "description": "A library for promises (CommonJS/Promises/A,B,D)", "devDependencies": {"cover": "*", "grunt": "~0.4.1", "grunt-cli": "~0.1.9", "grunt-contrib-uglify": "~0.9.1", "jasmine-node": "1.11.0", "jshint": "~2.1.9", "matcha": "~0.2.0", "opener": "*", "promises-aplus-tests": "1.x"}, "directories": {"test": "./spec"}, "dist": {"shasum": "dd01bac9d06d30e6f219aecb8253ee9ebdc308f1", "tarball": "https://registry.npmjs.org/q/-/q-1.5.0.tgz"}, "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}, "files": ["LICENSE", "q.js", "queue.js"], "gitHead": "4fecabe07ff9f3683a3d4548e7f81c2aba693326", "homepage": "https://github.com/kriskowal/q", "keywords": ["q", "promise", "promises", "promises-a", "promises-aplus", "deferred", "future", "async", "flow control", "fluent", "browser", "node"], "license": "MIT", "main": "q.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}], "name": "q", "optionalDependencies": {}, "overlay": {"teleport": {"dependencies": {"system": ">=0.0.4"}}}, "repository": {"type": "git", "url": "git://github.com/kriskowal/q.git"}, "scripts": {"benchmark": "matcha", "cover": "cover run jasmine-node spec && cover report html && opener cover_html/index.html", "lint": "j<PERSON>t q.js", "minify": "grunt", "prepublish": "grunt", "test": "npm ls -s && jasmine-node spec && promises-aplus-tests spec/aplus-adapter && npm run -s lint", "test-browser": "opener spec/q-spec.html"}, "version": "1.5.0"}
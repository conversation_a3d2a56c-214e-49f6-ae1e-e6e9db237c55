{"_args": [[{"raw": "util-deprecate@1.0.2", "scope": null, "escapedName": "util-deprecate", "name": "util-deprecate", "rawSpec": "1.0.2", "spec": "1.0.2", "type": "version"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/plist"]], "_from": "util-deprecate@1.0.2", "_id": "util-deprecate@1.0.2", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/util-deprecate", "_nodeVersion": "4.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_npmVersion": "2.14.4", "_phantomChildren": {}, "_requested": {"raw": "util-deprecate@1.0.2", "scope": null, "escapedName": "util-deprecate", "name": "util-deprecate", "rawSpec": "1.0.2", "spec": "1.0.2", "type": "version"}, "_requiredBy": ["/cordova-android/plist"], "_resolved": "http://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "_shasum": "450d4dc9fa70de732762fbd2d4a28981419a0ccf", "_shrinkwrap": null, "_spec": "util-deprecate@1.0.2", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/plist", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "browser": "browser.js", "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "dependencies": {}, "description": "The Node.js `util.deprecate()` function with browser support", "devDependencies": {}, "directories": {}, "dist": {"shasum": "450d4dc9fa70de732762fbd2d4a28981419a0ccf", "tarball": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"}, "gitHead": "475fb6857cd23fafff20c1be846c1350abf8e6d4", "homepage": "https://github.com/TooTallNate/util-deprecate", "keywords": ["util", "deprecate", "browserify", "browser", "node"], "license": "MIT", "main": "node.js", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "name": "util-deprecate", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.2"}
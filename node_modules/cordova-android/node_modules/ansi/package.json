{"_args": [[{"raw": "ansi@^0.3.1", "scope": null, "escapedName": "ansi", "name": "ansi", "rawSpec": "^0.3.1", "spec": ">=0.3.1 <0.4.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "ansi@>=0.3.1 <0.4.0", "_id": "ansi@0.3.1", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/ansi", "_nodeVersion": "5.3.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_npmVersion": "3.3.12", "_phantomChildren": {}, "_requested": {"raw": "ansi@^0.3.1", "scope": null, "escapedName": "ansi", "name": "ansi", "rawSpec": "^0.3.1", "spec": ">=0.3.1 <0.4.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/ansi/-/ansi-0.3.1.tgz", "_shasum": "0c42d4fb17160d5a9af1e484bace1c66922c1b21", "_shrinkwrap": null, "_spec": "ansi@^0.3.1", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tootallnate.net"}, "bugs": {"url": "https://github.com/TooTallNate/ansi.js/issues"}, "dependencies": {}, "description": "Advanced ANSI formatting tool for Node.js", "devDependencies": {}, "directories": {}, "dist": {"shasum": "0c42d4fb17160d5a9af1e484bace1c66922c1b21", "tarball": "https://registry.npmjs.org/ansi/-/ansi-0.3.1.tgz"}, "gitHead": "4d0d4af94e0bdaa648bd7262acd3bde4b98d5246", "homepage": "https://github.com/TooTallNate/ansi.js#readme", "keywords": ["ansi", "formatting", "cursor", "color", "terminal", "rgb", "256", "stream"], "license": "MIT", "main": "./lib/ansi.js", "maintainers": [{"name": "TooTallNate", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "name": "ansi", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/ansi.js.git"}, "scripts": {}, "version": "0.3.1"}
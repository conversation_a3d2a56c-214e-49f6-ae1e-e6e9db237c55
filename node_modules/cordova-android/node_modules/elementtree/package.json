{"_args": [[{"raw": "elementtree@0.1.6", "scope": null, "escapedName": "elementtree", "name": "elementtree", "rawSpec": "0.1.6", "spec": "0.1.6", "type": "version"}, "/Users/<USER>/repo/cordova/cordova-android"]], "_from": "elementtree@0.1.6", "_id": "elementtree@0.1.6", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/elementtree", "_npmUser": {"name": "rphilli<PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.3.24", "_phantomChildren": {}, "_requested": {"raw": "elementtree@0.1.6", "scope": null, "escapedName": "elementtree", "name": "elementtree", "rawSpec": "0.1.6", "spec": "0.1.6", "type": "version"}, "_requiredBy": ["/cordova-android", "/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/elementtree/-/elementtree-0.1.6.tgz", "_shasum": "2ac4c46ea30516c8c4cbdb5e3ac7418e592de20c", "_shrinkwrap": null, "_spec": "elementtree@0.1.6", "_where": "/Users/<USER>/repo/cordova/cordova-android", "author": {"name": "Rackspace US, Inc."}, "bugs": {"url": "https://github.com/racker/node-elementtree/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"sax": "0.3.5"}, "description": "XML Serialization and Parsing module based on Python's ElementTree.", "devDependencies": {"whiskey": "0.8.x"}, "directories": {"lib": "lib"}, "dist": {"shasum": "2ac4c46ea30516c8c4cbdb5e3ac7418e592de20c", "tarball": "https://registry.npmjs.org/elementtree/-/elementtree-0.1.6.tgz"}, "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/racker/node-elementtree", "keywords": ["xml", "sax", "parser", "seralization", "elementtree"], "licenses": [{"type": "Apache", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}], "main": "lib/elementtree.js", "maintainers": [{"name": "rphilli<PERSON>", "email": "<EMAIL>"}], "name": "elementtree", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/racker/node-elementtree.git"}, "scripts": {"test": "make test"}, "version": "0.1.6"}
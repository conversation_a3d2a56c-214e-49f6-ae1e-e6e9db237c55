{"_args": [[{"raw": "balanced-match@^0.4.1", "scope": null, "escapedName": "balanced-match", "name": "balanced-match", "rawSpec": "^0.4.1", "spec": ">=0.4.1 <0.5.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/brace-expansion"]], "_from": "balanced-match@>=0.4.1 <0.5.0", "_id": "balanced-match@0.4.2", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/balanced-match", "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/balanced-match-0.4.2.tgz_1468834991581_0.6590619895141572"}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.15.8", "_phantomChildren": {}, "_requested": {"raw": "balanced-match@^0.4.1", "scope": null, "escapedName": "balanced-match", "name": "balanced-match", "rawSpec": "^0.4.1", "spec": ">=0.4.1 <0.5.0", "type": "range"}, "_requiredBy": ["/cordova-android/brace-expansion"], "_resolved": "http://registry.npmjs.org/balanced-match/-/balanced-match-0.4.2.tgz", "_shasum": "cb3f3e3c732dc0f01ee70b403f302e61d7709838", "_shrinkwrap": null, "_spec": "balanced-match@^0.4.1", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/brace-expansion", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "dependencies": {}, "description": "Match balanced character pairs, like \"{\" and \"}\"", "devDependencies": {"tape": "^4.6.0"}, "directories": {}, "dist": {"shasum": "cb3f3e3c732dc0f01ee70b403f302e61d7709838", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.2.tgz"}, "gitHead": "57c2ea29d89a2844ae3bdcc637c6e2cbb73725e2", "homepage": "https://github.com/juliangruber/balanced-match", "keywords": ["match", "regexp", "test", "balanced", "parse"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "balanced-match", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "scripts": {"test": "make test"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "0.4.2"}
var common = require('./common');
var fs = require('fs');
var path = require('path');

//@
//@ ### 'string'.toEnd(file)
//@
//@ Examples:
//@
//@ ```javascript
//@ cat('input.txt').toEnd('output.txt');
//@ ```
//@
//@ Analogous to the redirect-and-append operator `>>` in Unix, but works with JavaScript strings (such as
//@ those returned by `cat`, `grep`, etc).
function _toEnd(options, file) {
  if (!file)
    common.error('wrong arguments');

  if (!fs.existsSync( path.dirname(file) ))
      common.error('no such file or directory: ' + path.dirname(file));

  try {
    fs.appendFileSync(file, this.toString(), 'utf8');
  } catch(e) {
    common.error('could not append to file (code '+e.code+'): '+file, true);
  }
}
module.exports = _toEnd;

//
// ShellJS
// Unix shell commands on top of Node's API
//
// Copyright (c) 2012 Artur Adib
// http://github.com/arturadib/shelljs
//

var common = require('./src/common');


//@
//@ All commands run synchronously, unless otherwise stated.
//@

//@include ./src/cd
var _cd = require('./src/cd');
exports.cd = common.wrap('cd', _cd);

//@include ./src/pwd
var _pwd = require('./src/pwd');
exports.pwd = common.wrap('pwd', _pwd);

//@include ./src/ls
var _ls = require('./src/ls');
exports.ls = common.wrap('ls', _ls);

//@include ./src/find
var _find = require('./src/find');
exports.find = common.wrap('find', _find);

//@include ./src/cp
var _cp = require('./src/cp');
exports.cp = common.wrap('cp', _cp);

//@include ./src/rm
var _rm = require('./src/rm');
exports.rm = common.wrap('rm', _rm);

//@include ./src/mv
var _mv = require('./src/mv');
exports.mv = common.wrap('mv', _mv);

//@include ./src/mkdir
var _mkdir = require('./src/mkdir');
exports.mkdir = common.wrap('mkdir', _mkdir);

//@include ./src/test
var _test = require('./src/test');
exports.test = common.wrap('test', _test);

//@include ./src/cat
var _cat = require('./src/cat');
exports.cat = common.wrap('cat', _cat);

//@include ./src/to
var _to = require('./src/to');
String.prototype.to = common.wrap('to', _to);

//@include ./src/toEnd
var _toEnd = require('./src/toEnd');
String.prototype.toEnd = common.wrap('toEnd', _toEnd);

//@include ./src/sed
var _sed = require('./src/sed');
exports.sed = common.wrap('sed', _sed);

//@include ./src/grep
var _grep = require('./src/grep');
exports.grep = common.wrap('grep', _grep);

//@include ./src/which
var _which = require('./src/which');
exports.which = common.wrap('which', _which);

//@include ./src/echo
var _echo = require('./src/echo');
exports.echo = _echo; // don't common.wrap() as it could parse '-options'

//@include ./src/dirs
var _dirs = require('./src/dirs').dirs;
exports.dirs = common.wrap("dirs", _dirs);
var _pushd = require('./src/dirs').pushd;
exports.pushd = common.wrap('pushd', _pushd);
var _popd = require('./src/dirs').popd;
exports.popd = common.wrap("popd", _popd);

//@include ./src/ln
var _ln = require('./src/ln');
exports.ln = common.wrap('ln', _ln);

//@
//@ ### exit(code)
//@ Exits the current process with the given exit code.
exports.exit = process.exit;

//@
//@ ### env['VAR_NAME']
//@ Object containing environment variables (both getter and setter). Shortcut to process.env.
exports.env = process.env;

//@include ./src/exec
var _exec = require('./src/exec');
exports.exec = common.wrap('exec', _exec, {notUnix:true});

//@include ./src/chmod
var _chmod = require('./src/chmod');
exports.chmod = common.wrap('chmod', _chmod);



//@
//@ ## Non-Unix commands
//@

//@include ./src/tempdir
var _tempDir = require('./src/tempdir');
exports.tempdir = common.wrap('tempdir', _tempDir);


//@include ./src/error
var _error = require('./src/error');
exports.error = _error;



//@
//@ ## Configuration
//@

exports.config = common.config;

//@
//@ ### config.silent
//@ Example:
//@
//@ ```javascript
//@ var sh = require('shelljs');
//@ var silentState = sh.config.silent; // save old silent state
//@ sh.config.silent = true;
//@ /* ... */
//@ sh.config.silent = silentState; // restore old silent state
//@ ```
//@
//@ Suppresses all command output if `true`, except for `echo()` calls.
//@ Default is `false`.

//@
//@ ### config.fatal
//@ Example:
//@
//@ ```javascript
//@ require('shelljs/global');
//@ config.fatal = true;
//@ cp('this_file_does_not_exist', '/dev/null'); // dies here
//@ /* more commands... */
//@ ```
//@
//@ If `true` the script will die on errors. Default is `false`.

{"_args": [[{"raw": "shelljs@^0.5.3", "scope": null, "escapedName": "<PERSON><PERSON>s", "name": "<PERSON><PERSON>s", "rawSpec": "^0.5.3", "spec": ">=0.5.3 <0.6.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android"]], "_from": "shelljs@>=0.5.3 <0.6.0", "_id": "shelljs@0.5.3", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/shelljs", "_nodeVersion": "1.2.0", "_npmUser": {"name": "artur", "email": "<EMAIL>"}, "_npmVersion": "2.5.1", "_phantomChildren": {}, "_requested": {"raw": "shelljs@^0.5.3", "scope": null, "escapedName": "<PERSON><PERSON>s", "name": "<PERSON><PERSON>s", "rawSpec": "^0.5.3", "spec": ">=0.5.3 <0.6.0", "type": "range"}, "_requiredBy": ["/cordova-android", "/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/shelljs/-/shelljs-0.5.3.tgz", "_shasum": "c54982b996c76ef0c1e6b59fbdc5825f5b713113", "_shrinkwrap": null, "_spec": "shelljs@^0.5.3", "_where": "/Users/<USER>/repo/cordova/cordova-android", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"shjs": "./bin/shjs"}, "bugs": {"url": "https://github.com/arturadib/shelljs/issues"}, "dependencies": {}, "description": "Portable Unix shell commands for Node.js", "devDependencies": {"jshint": "~2.1.11"}, "directories": {}, "dist": {"shasum": "c54982b996c76ef0c1e6b59fbdc5825f5b713113", "tarball": "https://registry.npmjs.org/shelljs/-/shelljs-0.5.3.tgz"}, "engines": {"node": ">=0.8.0"}, "gitHead": "22d0975040b9b8234755dc6e692d6869436e8485", "homepage": "http://github.com/arturadib/shelljs", "keywords": ["unix", "shell", "makefile", "make", "jake", "synchronous"], "license": "BSD*", "main": "./shell.js", "maintainers": [{"name": "artur", "email": "<EMAIL>"}], "name": "<PERSON><PERSON>s", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/arturadib/shelljs.git"}, "scripts": {"test": "node scripts/run-tests"}, "version": "0.5.3"}
{"_args": [[{"raw": "abbrev@1", "scope": null, "escapedName": "abbrev", "name": "abbrev", "rawSpec": "1", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/nopt"]], "_from": "abbrev@>=1.0.0 <2.0.0", "_id": "abbrev@1.1.0", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/abbrev", "_nodeVersion": "8.0.0-pre", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/abbrev-1.1.0.tgz_1487054000015_0.9229173036292195"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "4.3.0", "_phantomChildren": {}, "_requested": {"raw": "abbrev@1", "scope": null, "escapedName": "abbrev", "name": "abbrev", "rawSpec": "1", "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/nopt"], "_resolved": "http://registry.npmjs.org/abbrev/-/abbrev-1.1.0.tgz", "_shasum": "d0554c2256636e2f56e7c2e5ad183f859428d81f", "_shrinkwrap": null, "_spec": "abbrev@1", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/nopt", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dependencies": {}, "description": "Like ruby's abbrev module, but in js", "devDependencies": {"tap": "^10.1"}, "directories": {}, "dist": {"shasum": "d0554c2256636e2f56e7c2e5ad183f859428d81f", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.0.tgz"}, "files": ["abbrev.js"], "gitHead": "7136d4d95449dc44115d4f78b80ec907724f64e0", "homepage": "https://github.com/isaacs/abbrev-js#readme", "license": "ISC", "main": "abbrev.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "abbrev", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test.js --100"}, "version": "1.1.0"}
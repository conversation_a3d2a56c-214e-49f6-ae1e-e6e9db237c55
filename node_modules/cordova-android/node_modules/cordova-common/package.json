{"_args": [[{"raw": "cordova-common@^2.0.1", "scope": null, "escapedName": "cordova-common", "name": "cordova-common", "rawSpec": "^2.0.1", "spec": ">=2.0.1 <3.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android"]], "_from": "cordova-common@>=2.0.1 <3.0.0", "_id": "cordova-common@2.0.2", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/cordova-common", "_nodeVersion": "4.7.3", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cordova-common-2.0.2.tgz_1492453798445_0.6290795875247568"}, "_npmUser": {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.15.11", "_phantomChildren": {}, "_requested": {"raw": "cordova-common@^2.0.1", "scope": null, "escapedName": "cordova-common", "name": "cordova-common", "rawSpec": "^2.0.1", "spec": ">=2.0.1 <3.0.0", "type": "range"}, "_requiredBy": ["/cordova-android"], "_resolved": "http://registry.npmjs.org/cordova-common/-/cordova-common-2.0.2.tgz", "_shasum": "57467976b8afd5e0bd0a13111b66a420441601cb", "_shrinkwrap": null, "_spec": "cordova-common@^2.0.1", "_where": "/Users/<USER>/repo/cordova/cordova-android", "author": {"name": "Apache Software Foundation"}, "bugs": {"url": "https://issues.apache.org/jira/browse/CB", "email": "<EMAIL>"}, "contributors": [], "dependencies": {"ansi": "^0.3.1", "bplist-parser": "^0.1.0", "cordova-registry-mapper": "^1.1.8", "elementtree": "^0.1.6", "glob": "^5.0.13", "minimatch": "^3.0.0", "osenv": "^0.1.3", "plist": "^1.2.0", "q": "^1.4.1", "semver": "^5.0.1", "shelljs": "^0.5.3", "underscore": "^1.8.3", "unorm": "^1.3.3"}, "description": "Apache Cordova tools and platforms shared routines", "devDependencies": {"istanbul": "^0.4.5", "jasmine": "^2.5.2", "jshint": "^2.8.0", "promise-matchers": "^0.9.6", "rewire": "^2.5.1"}, "directories": {}, "dist": {"shasum": "57467976b8afd5e0bd0a13111b66a420441601cb", "tarball": "https://registry.npmjs.org/cordova-common/-/cordova-common-2.0.2.tgz"}, "engineStrict": true, "engines": {"node": ">=4.0.0"}, "license": "Apache-2.0", "main": "cordova-common.js", "maintainers": [{"name": "bowserj", "email": "<EMAIL>"}, {"name": "filmaj", "email": "<EMAIL>"}, {"name": "kotikov.vladimir", "email": "<EMAIL>"}, {"name": "purplecabbage", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timbarham", "email": "<EMAIL>"}], "name": "cordova-common", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://git-wip-us.apache.org/repos/asf/cordova-common.git"}, "scripts": {"cover": "istanbul cover --root src --print detail jasmine", "jasmine": "jasmine --captureExceptions --color", "jshint": "jshint src && jshint spec", "test": "npm run jshint && npm run jasmine"}, "version": "2.0.2"}
{"_args": [[{"raw": "properties-parser@^0.2.3", "scope": null, "escapedName": "properties-parser", "name": "properties-parser", "rawSpec": "^0.2.3", "spec": ">=0.2.3 <0.3.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android"]], "_from": "properties-parser@>=0.2.3 <0.3.0", "_id": "properties-parser@0.2.3", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/properties-parser", "_npmUser": {"name": "xavi", "email": "<EMAIL>"}, "_npmVersion": "1.3.23", "_phantomChildren": {}, "_requested": {"raw": "properties-parser@^0.2.3", "scope": null, "escapedName": "properties-parser", "name": "properties-parser", "rawSpec": "^0.2.3", "spec": ">=0.2.3 <0.3.0", "type": "range"}, "_requiredBy": ["/cordova-android"], "_resolved": "http://registry.npmjs.org/properties-parser/-/properties-parser-0.2.3.tgz", "_shasum": "f7591255f707abbff227c7b56b637dbb0373a10f", "_shrinkwrap": null, "_spec": "properties-parser@^0.2.3", "_where": "/Users/<USER>/repo/cordova/cordova-android", "bugs": {"url": "https://github.com/xavi-/node-properties-parser/issues"}, "dependencies": {}, "description": "A parser for .properties files written in javascript", "devDependencies": {}, "directories": {}, "dist": {"shasum": "f7591255f707abbff227c7b56b637dbb0373a10f", "tarball": "https://registry.npmjs.org/properties-parser/-/properties-parser-0.2.3.tgz"}, "engines": {"node": ">= 0.3.1"}, "homepage": "https://github.com/xavi-/node-properties-parser", "keywords": ["parser", ".properties", "properties", "java", "file parser", "actionscript"], "main": "./index.js", "maintainers": [{"name": "xavi", "email": "<EMAIL>"}], "name": "properties-parser", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/xavi-/node-properties-parser.git"}, "version": "0.2.3"}
{"_args": [[{"raw": "nopt@^3.0.1", "scope": null, "escapedName": "nopt", "name": "nopt", "rawSpec": "^3.0.1", "spec": ">=3.0.1 <4.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android"]], "_from": "nopt@>=3.0.1 <4.0.0", "_id": "nopt@3.0.6", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/nopt", "_nodeVersion": "4.2.1", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "_npmVersion": "2.14.10", "_phantomChildren": {}, "_requested": {"raw": "nopt@^3.0.1", "scope": null, "escapedName": "nopt", "name": "nopt", "rawSpec": "^3.0.1", "spec": ">=3.0.1 <4.0.0", "type": "range"}, "_requiredBy": ["/cordova-android"], "_resolved": "http://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "_shasum": "c6465dbf08abcd4db359317f79ac68a646b28ff9", "_shrinkwrap": null, "_spec": "nopt@^3.0.1", "_where": "/Users/<USER>/repo/cordova/cordova-android", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"nopt": "./bin/nopt.js"}, "bugs": {"url": "https://github.com/npm/nopt/issues"}, "dependencies": {"abbrev": "1"}, "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "devDependencies": {"tap": "^1.2.0"}, "directories": {}, "dist": {"shasum": "c6465dbf08abcd4db359317f79ac68a646b28ff9", "tarball": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz"}, "gitHead": "10a750c9bb99c1950160353459e733ac2aa18cb6", "homepage": "https://github.com/npm/nopt#readme", "license": "ISC", "main": "lib/nopt.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "name": "nopt", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "scripts": {"test": "tap test/*.js"}, "version": "3.0.6"}
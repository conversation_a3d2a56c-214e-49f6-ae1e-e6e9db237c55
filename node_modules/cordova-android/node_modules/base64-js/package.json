{"_args": [[{"raw": "base64-js@0.0.8", "scope": null, "escapedName": "base64-js", "name": "base64-js", "rawSpec": "0.0.8", "spec": "0.0.8", "type": "version"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/plist"]], "_from": "base64-js@0.0.8", "_id": "base64-js@0.0.8", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/base64-js", "_nodeVersion": "0.10.35", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "_npmVersion": "2.1.16", "_phantomChildren": {}, "_requested": {"raw": "base64-js@0.0.8", "scope": null, "escapedName": "base64-js", "name": "base64-js", "rawSpec": "0.0.8", "spec": "0.0.8", "type": "version"}, "_requiredBy": ["/cordova-android/plist"], "_resolved": "http://registry.npmjs.org/base64-js/-/base64-js-0.0.8.tgz", "_shasum": "1101e9544f4a76b1bc3b26d452ca96d7a35e7978", "_shrinkwrap": null, "_spec": "base64-js@0.0.8", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/plist", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "dependencies": {}, "description": "Base64 encoding/decoding in pure JS", "devDependencies": {"tape": "~2.3.2"}, "directories": {}, "dist": {"shasum": "1101e9544f4a76b1bc3b26d452ca96d7a35e7978", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.8.tgz"}, "engines": {"node": ">= 0.4"}, "gitHead": "b4a8a5fa9b0caeddb5ad94dd1108253d8f2a315f", "homepage": "https://github.com/beatgammit/base64-js", "license": "MIT", "main": "lib/b64.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "name": "base64-js", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "version": "0.0.8"}
{"_args": [[{"raw": "unorm@^1.3.3", "scope": null, "escapedName": "unorm", "name": "unorm", "rawSpec": "^1.3.3", "spec": ">=1.3.3 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "unorm@>=1.3.3 <2.0.0", "_id": "unorm@1.4.1", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/unorm", "_npmUser": {"name": "walling", "email": "<EMAIL>"}, "_npmVersion": "1.4.28", "_phantomChildren": {}, "_requested": {"raw": "unorm@^1.3.3", "scope": null, "escapedName": "unorm", "name": "unorm", "rawSpec": "^1.3.3", "spec": ">=1.3.3 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/unorm/-/unorm-1.4.1.tgz", "_shasum": "364200d5f13646ca8bcd44490271335614792300", "_shrinkwrap": null, "_spec": "unorm@^1.3.3", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/walling/unorm/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "oleg.gren<PERSON>@iki.fi"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "JavaScript Unicode 8.0 Normalization - NFC, NFD, NFKC, NFKD. Read <http://unicode.org/reports/tr15/> UAX #15 Unicode Normalization Forms.", "devDependencies": {"benchmark": "~1.0.0", "grunt": "~0.4.1", "grunt-contrib-jshint": "~0.8.0", "grunt-contrib-watch": "~0.5.0", "grunt-simple-mocha": "~0.4.0", "unorm": "1.4.1"}, "directories": {}, "dist": {"shasum": "364200d5f13646ca8bcd44490271335614792300", "tarball": "https://registry.npmjs.org/unorm/-/unorm-1.4.1.tgz"}, "engines": {"node": ">= 0.4.0"}, "gitHead": "e802d0d7844cf74b03742bce1147a82ace218396", "homepage": "https://github.com/walling/unorm", "license": "MIT or GPL-2.0", "main": "./lib/unorm.js", "maintainers": [{"name": "walling", "email": "<EMAIL>"}], "name": "unorm", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+ssh://**************/walling/unorm.git"}, "scripts": {"test": "grunt test"}, "version": "1.4.1"}
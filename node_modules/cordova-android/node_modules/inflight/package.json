{"_args": [[{"raw": "inflight@^1.0.4", "scope": null, "escapedName": "inflight", "name": "inflight", "rawSpec": "^1.0.4", "spec": ">=1.0.4 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/glob"]], "_from": "inflight@>=1.0.4 <2.0.0", "_id": "inflight@1.0.6", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/inflight", "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/inflight-1.0.6.tgz_1476330807696_0.10388551792129874"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.7", "_phantomChildren": {}, "_requested": {"raw": "inflight@^1.0.4", "scope": null, "escapedName": "inflight", "name": "inflight", "rawSpec": "^1.0.4", "spec": ">=1.0.4 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/glob"], "_resolved": "http://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "_shasum": "49bd6331d7d02d0c09bc910a1075ba8165b56df9", "_shrinkwrap": null, "_spec": "inflight@^1.0.4", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "description": "Add callbacks to requests in flight to avoid async duplication", "devDependencies": {"tap": "^7.1.2"}, "directories": {}, "dist": {"shasum": "49bd6331d7d02d0c09bc910a1075ba8165b56df9", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"}, "files": ["inflight.js"], "gitHead": "a547881738c8f57b27795e584071d67cf6ac1a57", "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "main": "inflight.js", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "name": "inflight", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/npm/inflight.git"}, "scripts": {"test": "tap test.js --100"}, "version": "1.0.6"}
{"_args": [[{"raw": "concat-map@0.0.1", "scope": null, "escapedName": "concat-map", "name": "concat-map", "rawSpec": "0.0.1", "spec": "0.0.1", "type": "version"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/brace-expansion"]], "_from": "concat-map@0.0.1", "_id": "concat-map@0.0.1", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/concat-map", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_npmVersion": "1.3.21", "_phantomChildren": {}, "_requested": {"raw": "concat-map@0.0.1", "scope": null, "escapedName": "concat-map", "name": "concat-map", "rawSpec": "0.0.1", "spec": "0.0.1", "type": "version"}, "_requiredBy": ["/cordova-android/brace-expansion"], "_resolved": "http://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "_shasum": "d8a96bd77fd68df7793a73036a3ba0d5405d477b", "_shrinkwrap": null, "_spec": "concat-map@0.0.1", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/brace-expansion", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-concat-map/issues"}, "dependencies": {}, "description": "concatenative mapdashery", "devDependencies": {"tape": "~2.4.0"}, "directories": {"example": "example", "test": "test"}, "dist": {"shasum": "d8a96bd77fd68df7793a73036a3ba0d5405d477b", "tarball": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"}, "homepage": "https://github.com/substack/node-concat-map", "keywords": ["concat", "concatMap", "map", "functional", "higher-order"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "name": "concat-map", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/substack/node-concat-map.git"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "version": "0.0.1"}
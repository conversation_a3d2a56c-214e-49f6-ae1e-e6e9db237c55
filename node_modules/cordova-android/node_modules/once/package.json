{"_args": [[{"raw": "once@^1.3.0", "scope": null, "escapedName": "once", "name": "once", "rawSpec": "^1.3.0", "spec": ">=1.3.0 <2.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/glob"]], "_from": "once@>=1.3.0 <2.0.0", "_id": "once@1.4.0", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/once", "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/once-1.4.0.tgz_1473196269128_0.537820661207661"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.7", "_phantomChildren": {}, "_requested": {"raw": "once@^1.3.0", "scope": null, "escapedName": "once", "name": "once", "rawSpec": "^1.3.0", "spec": ">=1.3.0 <2.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/glob", "/cordova-android/inflight"], "_resolved": "http://registry.npmjs.org/once/-/once-1.4.0.tgz", "_shasum": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "_shrinkwrap": null, "_spec": "once@^1.3.0", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/once/issues"}, "dependencies": {"wrappy": "1"}, "description": "Run a function exactly one time", "devDependencies": {"tap": "^7.0.1"}, "directories": {"test": "test"}, "dist": {"shasum": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "tarball": "https://registry.npmjs.org/once/-/once-1.4.0.tgz"}, "files": ["once.js"], "gitHead": "0e614d9f5a7e6f0305c625f6b581f6d80b33b8a6", "homepage": "https://github.com/isaacs/once#readme", "keywords": ["once", "function", "one", "single"], "license": "ISC", "main": "once.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "once", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "scripts": {"test": "tap test/*.js"}, "version": "1.4.0"}
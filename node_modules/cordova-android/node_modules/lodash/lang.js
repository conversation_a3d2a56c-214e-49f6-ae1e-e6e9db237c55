module.exports = {
  'clone': require('./lang/clone'),
  'cloneDeep': require('./lang/cloneDeep'),
  'eq': require('./lang/eq'),
  'gt': require('./lang/gt'),
  'gte': require('./lang/gte'),
  'isArguments': require('./lang/isArguments'),
  'isArray': require('./lang/isArray'),
  'isBoolean': require('./lang/isBoolean'),
  'isDate': require('./lang/isDate'),
  'isElement': require('./lang/isElement'),
  'isEmpty': require('./lang/isEmpty'),
  'isEqual': require('./lang/isEqual'),
  'isError': require('./lang/isError'),
  'isFinite': require('./lang/isFinite'),
  'isFunction': require('./lang/isFunction'),
  'isMatch': require('./lang/isMatch'),
  'isNaN': require('./lang/isNaN'),
  'isNative': require('./lang/isNative'),
  'isNull': require('./lang/isNull'),
  'isNumber': require('./lang/isNumber'),
  'isObject': require('./lang/isObject'),
  'isPlainObject': require('./lang/isPlainObject'),
  'isRegExp': require('./lang/isRegExp'),
  'isString': require('./lang/isString'),
  'isTypedArray': require('./lang/isTypedArray'),
  'isUndefined': require('./lang/isUndefined'),
  'lt': require('./lang/lt'),
  'lte': require('./lang/lte'),
  'toArray': require('./lang/toArray'),
  'toPlainObject': require('./lang/toPlainObject')
};

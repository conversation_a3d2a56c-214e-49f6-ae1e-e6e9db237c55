module.exports = {
  'chunk': require('./array/chunk'),
  'compact': require('./array/compact'),
  'difference': require('./array/difference'),
  'drop': require('./array/drop'),
  'dropRight': require('./array/dropRight'),
  'dropRightWhile': require('./array/dropRightWhile'),
  'dropWhile': require('./array/dropWhile'),
  'fill': require('./array/fill'),
  'findIndex': require('./array/findIndex'),
  'findLastIndex': require('./array/findLastIndex'),
  'first': require('./array/first'),
  'flatten': require('./array/flatten'),
  'flattenDeep': require('./array/flattenDeep'),
  'head': require('./array/head'),
  'indexOf': require('./array/indexOf'),
  'initial': require('./array/initial'),
  'intersection': require('./array/intersection'),
  'last': require('./array/last'),
  'lastIndexOf': require('./array/lastIndexOf'),
  'object': require('./array/object'),
  'pull': require('./array/pull'),
  'pullAt': require('./array/pullAt'),
  'remove': require('./array/remove'),
  'rest': require('./array/rest'),
  'slice': require('./array/slice'),
  'sortedIndex': require('./array/sortedIndex'),
  'sortedLastIndex': require('./array/sortedLastIndex'),
  'tail': require('./array/tail'),
  'take': require('./array/take'),
  'takeRight': require('./array/takeRight'),
  'takeRightWhile': require('./array/takeRightWhile'),
  'takeWhile': require('./array/takeWhile'),
  'union': require('./array/union'),
  'uniq': require('./array/uniq'),
  'unique': require('./array/unique'),
  'unzip': require('./array/unzip'),
  'unzipWith': require('./array/unzipWith'),
  'without': require('./array/without'),
  'xor': require('./array/xor'),
  'zip': require('./array/zip'),
  'zipObject': require('./array/zipObject'),
  'zipWith': require('./array/zipWith')
};

var baseSortByOrder = require('../internal/baseSortByOrder'),
    isArray = require('../lang/isArray'),
    isIterateeCall = require('../internal/isIterateeCall');

/**
 * This method is like `_.sortByAll` except that it allows specifying the
 * sort orders of the iteratees to sort by. If `orders` is unspecified, all
 * values are sorted in ascending order. Otherwise, a value is sorted in
 * ascending order if its corresponding order is "asc", and descending if "desc".
 *
 * If a property name is provided for an iteratee the created `_.property`
 * style callback returns the property value of the given element.
 *
 * If an object is provided for an iteratee the created `_.matches` style
 * callback returns `true` for elements that have the properties of the given
 * object, else `false`.
 *
 * @static
 * @memberOf _
 * @category Collection
 * @param {Array|Object|string} collection The collection to iterate over.
 * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.
 * @param {boolean[]} [orders] The sort orders of `iteratees`.
 * @param- {Object} [guard] Enables use as a callback for functions like `_.reduce`.
 * @returns {Array} Returns the new sorted array.
 * @example
 *
 * var users = [
 *   { 'user': 'fred',   'age': 48 },
 *   { 'user': 'barney', 'age': 34 },
 *   { 'user': 'fred',   'age': 42 },
 *   { 'user': 'barney', 'age': 36 }
 * ];
 *
 * // sort by `user` in ascending order and by `age` in descending order
 * _.map(_.sortByOrder(users, ['user', 'age'], ['asc', 'desc']), _.values);
 * // => [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 42]]
 */
function sortByOrder(collection, iteratees, orders, guard) {
  if (collection == null) {
    return [];
  }
  if (guard && isIterateeCall(iteratees, orders, guard)) {
    orders = undefined;
  }
  if (!isArray(iteratees)) {
    iteratees = iteratees == null ? [] : [iteratees];
  }
  if (!isArray(orders)) {
    orders = orders == null ? [] : [orders];
  }
  return baseSortByOrder(collection, iteratees, orders);
}

module.exports = sortByOrder;

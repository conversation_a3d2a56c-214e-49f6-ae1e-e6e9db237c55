var sample = require('./sample');

/** Used as references for `-Infinity` and `Infinity`. */
var POSITIVE_INFINITY = Number.POSITIVE_INFINITY;

/**
 * Creates an array of shuffled values, using a version of the
 * [<PERSON><PERSON><PERSON> shuffle](https://en.wikipedia.org/wiki/<PERSON>-<PERSON>_shuffle).
 *
 * @static
 * @memberOf _
 * @category Collection
 * @param {Array|Object|string} collection The collection to shuffle.
 * @returns {Array} Returns the new shuffled array.
 * @example
 *
 * _.shuffle([1, 2, 3, 4]);
 * // => [4, 1, 3, 2]
 */
function shuffle(collection) {
  return sample(collection, POSITIVE_INFINITY);
}

module.exports = shuffle;

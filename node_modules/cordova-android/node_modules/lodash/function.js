module.exports = {
  'after': require('./function/after'),
  'ary': require('./function/ary'),
  'backflow': require('./function/backflow'),
  'before': require('./function/before'),
  'bind': require('./function/bind'),
  'bindAll': require('./function/bindAll'),
  'bindKey': require('./function/bindKey'),
  'compose': require('./function/compose'),
  'curry': require('./function/curry'),
  'curryRight': require('./function/curryRight'),
  'debounce': require('./function/debounce'),
  'defer': require('./function/defer'),
  'delay': require('./function/delay'),
  'flow': require('./function/flow'),
  'flowRight': require('./function/flowRight'),
  'memoize': require('./function/memoize'),
  'modArgs': require('./function/modArgs'),
  'negate': require('./function/negate'),
  'once': require('./function/once'),
  'partial': require('./function/partial'),
  'partialRight': require('./function/partialRight'),
  'rearg': require('./function/rearg'),
  'restParam': require('./function/restParam'),
  'spread': require('./function/spread'),
  'throttle': require('./function/throttle'),
  'wrap': require('./function/wrap')
};

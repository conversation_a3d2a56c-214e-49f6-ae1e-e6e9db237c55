module.exports = {
  'assign': require('./object/assign'),
  'create': require('./object/create'),
  'defaults': require('./object/defaults'),
  'defaultsDeep': require('./object/defaultsDeep'),
  'extend': require('./object/extend'),
  'findKey': require('./object/findKey'),
  'findLastKey': require('./object/findLastKey'),
  'forIn': require('./object/forIn'),
  'forInRight': require('./object/forInRight'),
  'forOwn': require('./object/forOwn'),
  'forOwnRight': require('./object/forOwnRight'),
  'functions': require('./object/functions'),
  'get': require('./object/get'),
  'has': require('./object/has'),
  'invert': require('./object/invert'),
  'keys': require('./object/keys'),
  'keysIn': require('./object/keysIn'),
  'mapKeys': require('./object/mapKeys'),
  'mapValues': require('./object/mapValues'),
  'merge': require('./object/merge'),
  'methods': require('./object/methods'),
  'omit': require('./object/omit'),
  'pairs': require('./object/pairs'),
  'pick': require('./object/pick'),
  'result': require('./object/result'),
  'set': require('./object/set'),
  'transform': require('./object/transform'),
  'values': require('./object/values'),
  'valuesIn': require('./object/valuesIn')
};

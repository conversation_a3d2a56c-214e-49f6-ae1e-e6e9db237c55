var baseFlatten = require('../internal/baseFlatten');

/**
 * Recursively flattens a nested array.
 *
 * @static
 * @memberOf _
 * @category Array
 * @param {Array} array The array to recursively flatten.
 * @returns {Array} Returns the new flattened array.
 * @example
 *
 * _.flattenDeep([1, [2, 3, [4]]]);
 * // => [1, 2, 3, 4]
 */
function flattenDeep(array) {
  var length = array ? array.length : 0;
  return length ? baseFlatten(array, true) : [];
}

module.exports = flattenDeep;

{"_args": [[{"raw": "lodash@^3.5.0", "scope": null, "escapedName": "lodash", "name": "lodash", "rawSpec": "^3.5.0", "spec": ">=3.5.0 <4.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/xmlbuilder"]], "_from": "lodash@>=3.5.0 <4.0.0", "_id": "lodash@3.10.1", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/lodash", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "2.13.1", "_phantomChildren": {}, "_requested": {"raw": "lodash@^3.5.0", "scope": null, "escapedName": "lodash", "name": "lodash", "rawSpec": "^3.5.0", "spec": ">=3.5.0 <4.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/xmlbuilder"], "_resolved": "http://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "_shasum": "5bf45e8e49ba4189e17d482789dfd15bd140b7b6", "_shrinkwrap": null, "_spec": "lodash@^3.5.0", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/xmlbuilder", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "dependencies": {}, "description": "The modern build of lodash modular utilities.", "devDependencies": {}, "directories": {}, "dist": {"shasum": "5bf45e8e49ba4189e17d482789dfd15bd140b7b6", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz"}, "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["modules", "stdlib", "util"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "name": "lodash", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "version": "3.10.1"}
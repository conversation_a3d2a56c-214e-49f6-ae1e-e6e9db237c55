{"_args": [[{"raw": "osenv@^0.1.3", "scope": null, "escapedName": "osenv", "name": "osenv", "rawSpec": "^0.1.3", "spec": ">=0.1.3 <0.2.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "osenv@>=0.1.3 <0.2.0", "_id": "osenv@0.1.4", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/osenv", "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/osenv-0.1.4.tgz_1481655889868_0.3980878754518926"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.9", "_phantomChildren": {}, "_requested": {"raw": "osenv@^0.1.3", "scope": null, "escapedName": "osenv", "name": "osenv", "rawSpec": "^0.1.3", "spec": ">=0.1.3 <0.2.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/osenv/-/osenv-0.1.4.tgz", "_shasum": "42fe6d5953df06c8064be6f176c3d05aaaa34644", "_shrinkwrap": null, "_spec": "osenv@^0.1.3", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/npm/osenv/issues"}, "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}, "description": "Look up environment settings specific to different operating systems", "devDependencies": {"tap": "^8.0.1"}, "directories": {"test": "test"}, "dist": {"shasum": "42fe6d5953df06c8064be6f176c3d05aaaa34644", "tarball": "https://registry.npmjs.org/osenv/-/osenv-0.1.4.tgz"}, "gitHead": "ef718f0d20e38d45ec452b7faeefc692d3cd1062", "homepage": "https://github.com/npm/osenv#readme", "keywords": ["environment", "variable", "home", "tmpdir", "path", "prompt", "ps1"], "license": "ISC", "main": "osenv.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "name": "osenv", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/npm/osenv.git"}, "scripts": {"test": "tap test/*.js"}, "version": "0.1.4"}
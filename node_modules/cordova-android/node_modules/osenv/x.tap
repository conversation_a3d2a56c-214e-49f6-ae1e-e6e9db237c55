TAP version 13
    # Subtest: test/unix.js
    TAP version 13
        # Subtest: basic unix sanity test
        ok 1 - should be equal
        ok 2 - should be equal
        ok 3 - should be equal
        ok 4 - should be equivalent
        ok 5 - should be equal
        ok 6 - should be equal
        ok 7 - should be equal
        ok 8 - should be equal
        ok 9 - should be equal
        ok 10 - should be equal
        ok 11 - should be equal
        ok 12 - should be equal
        ok 13 - should be equal
        ok 14 - should be equal
        1..14
    ok 1 - basic unix sanity test # time=10.712ms

    1..1
    # time=18.422ms
ok 1 - test/unix.js # time=169.827ms

    # Subtest: test/windows.js
    TAP version 13
    1..0 # Skip windows tests, this is not windows

ok 2 - test/windows.js # SKIP Skip windows tests, this is not windows

    # Subtest: test/nada.js
    TAP version 13
    1..0

ok 2 - test/nada.js

1..3
# time=274.247ms

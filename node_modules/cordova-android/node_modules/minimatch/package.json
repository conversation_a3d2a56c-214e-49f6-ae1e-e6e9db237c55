{"_args": [[{"raw": "minimatch@^3.0.0", "scope": null, "escapedName": "minimatch", "name": "minimatch", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "minimatch@>=3.0.0 <4.0.0", "_id": "minimatch@3.0.3", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/minimatch", "_nodeVersion": "4.4.4", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minimatch-3.0.3.tgz_1470678322731_0.1892083385027945"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.6", "_phantomChildren": {}, "_requested": {"raw": "minimatch@^3.0.0", "scope": null, "escapedName": "minimatch", "name": "minimatch", "rawSpec": "^3.0.0", "spec": ">=3.0.0 <4.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common", "/cordova-android/glob"], "_resolved": "http://registry.npmjs.org/minimatch/-/minimatch-3.0.3.tgz", "_shasum": "2a4e4090b96b2db06a9d7df01055a62a77c9b774", "_shrinkwrap": null, "_spec": "minimatch@^3.0.0", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "dependencies": {"brace-expansion": "^1.0.0"}, "description": "a glob matcher in javascript", "devDependencies": {"standard": "^3.7.2", "tap": "^5.6.0"}, "directories": {}, "dist": {"shasum": "2a4e4090b96b2db06a9d7df01055a62a77c9b774", "tarball": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.3.tgz"}, "engines": {"node": "*"}, "files": ["minimatch.js"], "gitHead": "eed89491bd4a4e6bc463aac0dfb5c29ef0d1dc13", "homepage": "https://github.com/isaacs/minimatch#readme", "license": "ISC", "main": "minimatch.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "minimatch", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "scripts": {"posttest": "standard minimatch.js test/*.js", "test": "tap test/*.js"}, "version": "3.0.3"}
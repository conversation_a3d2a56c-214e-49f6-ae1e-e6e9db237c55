{"_args": [[{"raw": "inherits@2", "scope": null, "escapedName": "inherits", "name": "inherits", "rawSpec": "2", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/glob"]], "_from": "inherits@>=2.0.0 <3.0.0", "_id": "inherits@2.0.3", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/inherits", "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/inherits-2.0.3.tgz_1473295776489_0.08142363070510328"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.7", "_phantomChildren": {}, "_requested": {"raw": "inherits@2", "scope": null, "escapedName": "inherits", "name": "inherits", "rawSpec": "2", "spec": ">=2.0.0 <3.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/glob"], "_resolved": "http://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "_shasum": "633c2c83e3da42a502f52466022480f4208261de", "_shrinkwrap": null, "_spec": "inherits@2", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/glob", "browser": "./inherits_browser.js", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "dependencies": {}, "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "devDependencies": {"tap": "^7.1.0"}, "directories": {}, "dist": {"shasum": "633c2c83e3da42a502f52466022480f4208261de", "tarball": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"}, "files": ["inherits.js", "inherits_browser.js"], "gitHead": "e05d0fb27c61a3ec687214f0476386b765364d5f", "homepage": "https://github.com/isaacs/inherits#readme", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "license": "ISC", "main": "./inherits.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "inherits", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "scripts": {"test": "node test"}, "version": "2.0.3"}
{"_args": [[{"raw": "bplist-parser@^0.1.0", "scope": null, "escapedName": "bplist-parser", "name": "bplist-parser", "rawSpec": "^0.1.0", "spec": ">=0.1.0 <0.2.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "bplist-parser@>=0.1.0 <0.2.0", "_id": "bplist-parser@0.1.1", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/bplist-parser", "_nodeVersion": "5.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "3.4.0", "_phantomChildren": {}, "_requested": {"raw": "bplist-parser@^0.1.0", "scope": null, "escapedName": "bplist-parser", "name": "bplist-parser", "rawSpec": "^0.1.0", "spec": ">=0.1.0 <0.2.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/bplist-parser/-/bplist-parser-0.1.1.tgz", "_shasum": "d60d5dcc20cba6dc7e1f299b35d3e1f95dafbae6", "_shrinkwrap": null, "_spec": "bplist-parser@^0.1.0", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nearinfinity/node-bplist-parser/issues"}, "dependencies": {"big-integer": "^1.6.7"}, "description": "Binary plist parser.", "devDependencies": {"nodeunit": "~0.9.1"}, "directories": {}, "dist": {"shasum": "d60d5dcc20cba6dc7e1f299b35d3e1f95dafbae6", "tarball": "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.1.1.tgz"}, "gitHead": "c4f22650de2cc95edd21a6e609ff0654a2b951bd", "homepage": "https://github.com/nearinfinity/node-bplist-parser#readme", "keywords": ["bplist", "plist", "parser"], "license": "MIT", "main": "bplistParser.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "bplist-parser", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/nearinfinity/node-bplist-parser.git"}, "scripts": {"test": "./node_modules/nodeunit/bin/nodeunit test"}, "version": "0.1.1"}
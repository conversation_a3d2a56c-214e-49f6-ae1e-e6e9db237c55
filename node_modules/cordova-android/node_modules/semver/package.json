{"_args": [[{"raw": "semver@^5.0.1", "scope": null, "escapedName": "semver", "name": "semver", "rawSpec": "^5.0.1", "spec": ">=5.0.1 <6.0.0", "type": "range"}, "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common"]], "_from": "semver@>=5.0.1 <6.0.0", "_id": "semver@5.3.0", "_inBundle": true, "_inCache": true, "_location": "/cordova-android/semver", "_nodeVersion": "4.4.4", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/semver-5.3.0.tgz_1468515166602_0.9155273644719273"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmVersion": "3.10.6", "_phantomChildren": {}, "_requested": {"raw": "semver@^5.0.1", "scope": null, "escapedName": "semver", "name": "semver", "rawSpec": "^5.0.1", "spec": ">=5.0.1 <6.0.0", "type": "range"}, "_requiredBy": ["/cordova-android/cordova-common"], "_resolved": "http://registry.npmjs.org/semver/-/semver-5.3.0.tgz", "_shasum": "9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f", "_shrinkwrap": null, "_spec": "semver@^5.0.1", "_where": "/Users/<USER>/repo/cordova/cordova-android/node_modules/cordova-common", "bin": {"semver": "./bin/semver"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "dependencies": {}, "description": "The semantic version parser used by npm.", "devDependencies": {"tap": "^2.0.0"}, "directories": {}, "dist": {"shasum": "9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f", "tarball": "https://registry.npmjs.org/semver/-/semver-5.3.0.tgz"}, "files": ["bin", "range.bnf", "semver.js"], "gitHead": "d21444a0658224b152ce54965d02dbe0856afb84", "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "name": "semver", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"test": "tap test/*.js"}, "version": "5.3.0"}
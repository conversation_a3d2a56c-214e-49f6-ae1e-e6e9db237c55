{"name": "cordova-android", "version": "9.1.0", "description": "cordova-android release", "bin": {"create": "bin/create"}, "main": "bin/templates/cordova/Api.js", "repository": "github:apache/cordova-android", "bugs": "https://github.com/apache/cordova-android/issues", "keywords": ["android", "<PERSON><PERSON>", "apache"], "scripts": {"test": "npm run lint && npm run cover && npm run java-unit-tests", "lint": "eslint . \"bin/**/!(*.*|gitignore)\"", "unit-tests": "jasmine --config=spec/unit/jasmine.json", "cover": "nyc jasmine --config=spec/coverage.json", "e2e-tests": "jasmine --config=spec/e2e/jasmine.json", "java-unit-tests": "node test/run_java_unit_tests.js", "clean:java-unit-tests": "node test/clean.js"}, "author": "Apache Software Foundation", "license": "Apache-2.0", "dependencies": {"android-versions": "^1.5.0", "cordova-common": "^4.0.1", "execa": "^4.0.2", "fast-glob": "^3.2.4", "fs-extra": "^9.0.1", "is-path-inside": "^3.0.2", "nopt": "^4.0.3", "properties-parser": "^0.3.1", "semver": "^7.3.4", "which": "^2.0.2"}, "devDependencies": {"@cordova/eslint-config": "^3.0.0", "jasmine": "^3.5.0", "jasmine-spec-reporter": "^5.0.2", "nyc": "^15.1.0", "rewire": "^5.0.0"}, "engines": {"node": ">=10.10.0"}, "nyc": {"include": ["bin/lib/**", "bin/templates/cordova/**"], "reporter": ["lcov", "text"]}}
.DS_Store
default.properties
gen
assets/www/cordova.js
local.properties
proguard.cfg
proguard.cfg
proguard-project.txt
/framework/lib
/framework/build
/framework/bin
/framework/assets/www/.DS_Store
/framework/assets/www/cordova-*.js
/framework/assets/www/phonegap-*.js
/framework/libs
/framework/javadoc-public
/framework/javadoc-private
/test/libs
example
/test/bin
/test/assets/www/.tmp*
/test/assets/www/cordova.js
/test/gradle
/test/gradlew
/test/gradlew.bat
/test/build
.gradle
tmp/**
.metadata
tmp/**/*
Thumbs.db
Desktop.ini
*.tmp
*.bak
*.swp
*.class
*.jar
!/spec/fixtures/org.test.plugins.dummyplugin/src/android/TestLib.jar
# IntelliJ IDEA files
*.iml
.idea
npm-debug.log
node_modules/jshint
node_modules/promise-matchers
node_modules/jasmine
node_modules/rewire
node_modules/istanbul
node_modules/.bin/cake
node_modules/.bin/coffee
node_modules/.bin/escodegen
node_modules/.bin/esgenerate
node_modules/.bin/esparse
node_modules/.bin/esvalidate
node_modules/.bin/handlebars
node_modules/.bin/istanbul
node_modules/.bin/jasmine
node_modules/.bin/js-yaml
node_modules/.bin/jshint
node_modules/.bin/mkdirp
node_modules/.bin/r.js
node_modules/.bin/r_js
node_modules/.bin/strip-json-comments
node_modules/.bin/uglifyjs
node_modules/.bin/which
node_modules/align-text/
node_modules/amdefine/
node_modules/argparse/
node_modules/async/
node_modules/camelcase/
node_modules/center-align/
node_modules/cli/
node_modules/cliui/
node_modules/coffee-script/
node_modules/console-browserify/
node_modules/core-util-is/
node_modules/date-now/
node_modules/decamelize/
node_modules/deep-is/
node_modules/dom-serializer/
node_modules/domelementtype/
node_modules/domhandler/
node_modules/domutils/
node_modules/entities/
node_modules/escodegen/
node_modules/esprima/
node_modules/estraverse/
node_modules/esutils/
node_modules/exit/
node_modules/fast-levenshtein/
node_modules/fileset/
node_modules/gaze/
node_modules/growl/
node_modules/handlebars/
node_modules/has-flag/
node_modules/htmlparser2/
node_modules/is-buffer/
node_modules/isarray/
node_modules/isexe/
node_modules/jasmine-growl-reporter/
node_modules/jasmine-reporters/
node_modules/js-yaml/
node_modules/kind-of/
node_modules/lazy-cache/
node_modules/levn/
node_modules/longest/
node_modules/lru-cache/
node_modules/minimist/
node_modules/mkdirp/
node_modules/optimist/
node_modules/optionator/
node_modules/prelude-ls/
node_modules/readable-stream/
node_modules/repeat-string/
node_modules/requirejs/
node_modules/resolve/
node_modules/right-align/
node_modules/sigmund/
node_modules/source-map/
node_modules/sprintf-js/
node_modules/string_decoder/
node_modules/strip-json-comments/
node_modules/supports-color/
node_modules/type-check/
node_modules/uglify-js/
node_modules/uglify-to-browserify/
node_modules/walkdir/
node_modules/which/
node_modules/window-size/
node_modules/wordwrap/
node_modules/yargs/
node_modules/jasmine-core/
node_modules/fs.realpath/
/coverage

{"name": "nopt", "version": "4.0.3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "main": "lib/nopt.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/nopt.git"}, "bin": "./bin/nopt.js", "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "devDependencies": {"tap": "^14.10.6"}, "files": ["bin", "lib"]}
name: Node CI

on:
  push:
    branches:
    - master
    tags:
    - '!*'
  pull_request:

jobs:
  build:
    name: Test Node.js ${{ matrix.node-version }} on ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        node-version: [6.x, 8.x, 10.x, 12.x, 14.x]

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v1

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}

    - name: Print Node.js Version
      run: node --version

    - name: Install Dependencies
      run: npm install
      env:
        CI: true

    - name: Run "build" step
      run: npm run build --if-present
      env:
        CI: true

    - name: Run tests
      run: npm test
      env:
        CI: true

{"name": "proxy-agent", "version": "4.0.1", "description": "Maps proxy protocols to `http.Agent` implementations", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "engines": {"node": ">=6"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-proxy-agent.git"}, "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "homepage": "https://github.com/TooTallNate/node-proxy-agent", "dependencies": {"agent-base": "^6.0.0", "debug": "4", "http-proxy-agent": "^4.0.0", "https-proxy-agent": "^5.0.0", "lru-cache": "^5.1.1", "pac-proxy-agent": "^4.1.0", "proxy-from-env": "^1.0.0", "socks-proxy-agent": "^5.0.0"}, "devDependencies": {"@types/agent-base": "^4.2.0", "mocha": "^6.2.1", "proxy": "^1.0.1", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}}
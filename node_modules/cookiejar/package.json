{"_from": "cookiejar@^2.1.0", "_id": "cookiejar@2.1.2", "_inBundle": false, "_integrity": "sha512-Mw+adcfzPxcPeI+0WlvRrr/3lGVO0bD75SxX6811cxSh1Wbxx7xZBGK1eVtDf6si8rg2lhnUjsVLMFMfbRIuwA==", "_location": "/cookiejar", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cookiejar@^2.1.0", "name": "cookiejar", "escapedName": "cookiejar", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/superagent"], "_resolved": "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.2.tgz", "_shasum": "dd8a235530752f988f9a0844f3fc589e3111125c", "_spec": "cookiejar@^2.1.0", "_where": "D:\\projects\\soil-samples-app\\node_modules\\superagent", "author": {"name": "bradleymeck"}, "bugs": {"url": "https://github.com/bmeck/node-cookiejar/issues"}, "bundleDependencies": false, "deprecated": false, "description": "simple persistent cookiejar system", "devDependencies": {"jshint": "^2.9.4"}, "files": ["cookiejar.js"], "homepage": "https://github.com/bmeck/node-cookiejar#readme", "jshintConfig": {"node": true}, "license": "MIT", "main": "cookiejar.js", "name": "cookiejar", "repository": {"type": "git", "url": "git+https://github.com/bmeck/node-cookiejar.git"}, "scripts": {"test": "node tests/test.js"}, "version": "2.1.2"}
{"name": "recursive-fs", "version": "1.1.2", "description": "Asynchronous recursive file system operations", "keywords": ["asynchronous", "recursive", "file", "system", "read", "copy", "remove", "directory", "cli"], "license": "MIT", "homepage": "https://github.com/simov/recursive-fs", "author": "<PERSON><PERSON><PERSON> <simeonvel<PERSON><EMAIL>> (https://simov.github.io)", "repository": {"type": "git", "url": "https://github.com/simov/recursive-fs.git"}, "dependencies": {}, "devDependencies": {"coveralls": "^3.0.2", "istanbul": "^0.4.5", "mocha": "^5.2.0"}, "main": "index.js", "files": ["bin/", "lib/", "LICENSE", "README.md", "index.js"], "bin": {"recursive-copy": "./bin/recursive-copy", "recursive-delete": "./bin/recursive-delete"}, "scripts": {"test": "npm run test:ci", "test:ci": "mocha test/", "test:cov": "istanbul cover _mocha"}, "engines": {"node": ">=4.0.0"}}
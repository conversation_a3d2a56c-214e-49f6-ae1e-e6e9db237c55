"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const util_1 = require("./util");
/**
 * Resolves the given DNS hostname into an IP address, and returns it in the dot
 * separated format as a string.
 *
 * Example:
 *
 * ``` js
 * dnsResolve("home.netscape.com")
 *   // returns the string "*************".
 * ```
 *
 * @param {String} host hostname to resolve
 * @return {String} resolved IP address
 */
function dnsResolve(host) {
    return __awaiter(this, void 0, void 0, function* () {
        const family = 4;
        try {
            const r = yield util_1.dnsLookup(host, { family });
            if (typeof r === 'string') {
                return r;
            }
        }
        catch (err) {
            // @ignore
        }
        return null;
    });
}
exports.default = dnsResolve;
//# sourceMappingURL=dnsResolve.js.map
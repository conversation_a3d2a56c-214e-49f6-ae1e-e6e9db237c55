{"version": 3, "file": "isInNet.js", "sourceRoot": "", "sources": ["../src/isInNet.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;AAEH,qCAAkC;AAClC,iCAAmC;AAEnC;;;;;;;;;;;;;;;;;;;;;GAqBG;AAEH,SAA8B,OAAO,CACpC,IAAY,EACZ,OAAe,EACf,IAAY;;QAEZ,MAAM,MAAM,GAAG,CAAC,CAAC;QACjB,IAAI;YACH,MAAM,EAAE,GAAG,MAAM,gBAAS,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;gBAC3B,MAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC3C,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aAC5B;SACD;QAAC,OAAO,GAAG,EAAE,GAAE;QAChB,OAAO,KAAK,CAAC;IACd,CAAC;CAAA;AAdD,0BAcC"}
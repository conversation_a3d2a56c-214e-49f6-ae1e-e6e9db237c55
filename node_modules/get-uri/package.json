{"name": "get-uri", "version": "3.0.2", "description": "Returns a `stream.Readable` from a URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-get-uri.git"}, "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "devDependencies": {"@types/debug": "4", "@types/fs-extra": "^8.0.1", "@types/ftp": "^0.3.30", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "mocha": "^6.2.2", "rimraf": "^3.0.0", "st": "^1.2.2", "stream-to-array": "2", "typescript": "^3.7.3"}, "dependencies": {"@tootallnate/once": "1", "data-uri-to-buffer": "3", "debug": "4", "file-uri-to-path": "2", "fs-extra": "^8.1.0", "ftp": "^0.3.10"}, "engines": {"node": ">= 6"}}
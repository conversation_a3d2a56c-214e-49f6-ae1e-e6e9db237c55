{"_from": "co@^4.6.0", "_id": "co@4.6.0", "_inBundle": false, "_integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "_location": "/co", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "co@^4.6.0", "name": "co", "escapedName": "co", "rawSpec": "^4.6.0", "saveSpec": null, "fetchSpec": "^4.6.0"}, "_requiredBy": ["/pac-resolver"], "_resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "_shasum": "6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184", "_spec": "co@^4.6.0", "_where": "D:\\projects\\soil-samples-app\\node_modules\\pac-resolver", "bugs": {"url": "https://github.com/tj/co/issues"}, "bundleDependencies": false, "deprecated": false, "description": "generator async control flow goodness", "devDependencies": {"browserify": "^10.0.0", "istanbul-harmony": "0", "mocha": "^2.0.0", "mz": "^1.0.2"}, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "files": ["index.js"], "homepage": "https://github.com/tj/co#readme", "keywords": ["async", "flow", "generator", "coro", "coroutine"], "license": "MIT", "name": "co", "repository": {"type": "git", "url": "git+https://github.com/tj/co.git"}, "scripts": {"browserify": "browserify index.js -o ./co-browser.js -s co", "prepublish": "npm run browserify", "test": "mocha --harmony", "test-cov": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --reporter dot", "test-travis": "node --harmony node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --reporter dot"}, "version": "4.6.0"}
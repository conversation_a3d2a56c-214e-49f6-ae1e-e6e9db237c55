{"name": "cordova-zip-plugin", "version": "0.0.4", "description": "Cordova zip compress and decompress", "cordova": {"id": "com.jjdltc.cordova.plugin.zip", "platforms": ["android"]}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jjdltc/jjdltc-cordova-plugin-zip.git"}, "keywords": ["<PERSON><PERSON>", "zip", "compress", "decompress", "unzip", "ecosystem:cordova", "cordova-android"], "engines": [{"name": "<PERSON><PERSON>", "version": ">=3.6.0"}], "author": "<PERSON> - jjdltc", "license": "MIT", "bugs": {"url": "https://github.com/jjdltc/jjdltc-cordova-plugin-zip/issues"}, "homepage": "https://github.com/jjdltc/jjdltc-cordova-plugin-zip#readme"}
= 0.4.7 =
Clear old data from buffer on new connection fixes #286
Update package.json for Meteor #271 Thanks StephenClement
Support HM-10 on iOS #288

= 0.4.6 =
Add support for Android 6.0 (API 23) Permissions. Thanks <PERSON><PERSON><PERSON> #194, #226, #228

= 0.4.5 =
Resolve iOS conflicts with cordova-plugin-ble-central #177
Add support for broadcasted name when discoverable (Android) #137 Thanks ma<PERSON><PERSON><PERSON>ld
Add a 'browser' target for emulated bluetooth #115 Thanks PhracturedBlue

= 0.4.4 =
Move plugin to NPM #160
Support iOS 9 #154 #161 Thanks amplexdenmark
Work with Adafruit nRF8001 and newer Bluefruit hardware #157 Thanks legege
Drop support for Cordova 2.9

= 0.4.3 =
New API - Add device callback when scanning for unpaired Bluetooth devices. @shokre #119 #123
Send NO_RESULT to subscribe callback when unsubscribing #121
Fix bug in Chat example
Fix write for Adafruit hardware (CBCharacteristicWriteWithoutResponse)

= 0.4.2 =
Fix typo in WP8 code

= 0.4.1 =
New APIs
* showBluetoothSettings (Android & WP)
* enable (Android only)
* discoverUnpaired (Android only)

Implement isConnected and isEnabled for WP

= 0.4.0 =
Add Windows Phone 8 #63

= 0.3.5 =
Remote NO_RESULT from iOS implementation CB-8063
Switch Adafruit code to WriteWithoutResponse on iOS

= 0.3.4 =
Support for BlueGiga #96 - @ChrisMorter

= 0.3.3 =
Write accepts binary data #83 #39 - Adrián Pardini @pardo-bsso
Add rawSubscribe for callback with binary data #83 #57 - Adrián Pardini @pardo-bsso
Add fallback connection for finicky Android devices #89 - Matt Philips @SkySrfr
Fix UUIDs for Laird BL600 #90 - mrtomhoward

= 0.3.1 =
Add support for Laird BL600 #71
Add unsubscribe for iOS #66

= 0.3.0 =
Add support for Adafruit Bluefruit LE http://adafru.it/1697
Use RedBear Lab BLE 2.0.1 - supports for v1 and v2 of the shield in addition to BLEmini
Show RSSI for discovered BLE devices, when available
Add readRSSI api (BLE only)

Fixes issues #4, #5, #23, #24, #25, #26, #38, #54

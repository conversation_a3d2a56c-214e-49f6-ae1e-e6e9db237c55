{"_from": "cordova-plugin-bluetooth-serial@~0.4.6", "_id": "cordova-plugin-bluetooth-serial@0.4.7", "_inBundle": false, "_integrity": "sha1-HkYG7G1Ct8I/YKdKh8fEpOw4KMA=", "_location": "/cordova-plugin-bluetooth-serial", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cordova-plugin-bluetooth-serial@~0.4.6", "name": "cordova-plugin-bluetooth-serial", "escapedName": "cordova-plugin-bluetooth-serial", "rawSpec": "~0.4.6", "saveSpec": null, "fetchSpec": "~0.4.6"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/cordova-plugin-bluetooth-serial/-/cordova-plugin-bluetooth-serial-0.4.7.tgz", "_shasum": "1e4606ec6d42b7c23f60a74a87c7c4a4ec3828c0", "_spec": "cordova-plugin-bluetooth-serial@~0.4.6", "_where": "D:\\projects\\soil-samples-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/don/BluetoothSerial/issues"}, "bundleDependencies": false, "cordova": {"id": "cordova-plugin-bluetooth-serial", "platforms": ["android", "ios", "wp8"]}, "deprecated": false, "description": "Bluetooth Serial Communication Plugin", "homepage": "https://github.com/don/BluetoothSerial#readme", "keywords": ["<PERSON><PERSON>", "bluetooth", "serial", "ble", "a<PERSON><PERSON><PERSON>", "ecosystem:cordova"], "license": "Apache-2.0", "main": "./www/bluetoothSerial.js", "name": "cordova-plugin-bluetooth-serial", "repository": {"type": "git", "url": "git+https://github.com/don/BluetoothSerial.git"}, "version": "0.4.7"}